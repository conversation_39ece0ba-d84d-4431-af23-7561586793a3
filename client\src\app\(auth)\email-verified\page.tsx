"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import Link from 'next/link';
import { buttonVariants } from '@/components/ui/button';
import { PENDING_INVITATION_COOKIE } from '@/constants';
import { getCookie } from 'cookies-next/client';

export default function EmailVerifiedPage() {
  const router = useRouter();

  useEffect(() => {
    const checkPendingInvitation = async () => {
      try {
        // Verificar si hay una invitación pendiente en sessionStorage
        // const pendingInvitationUrl = sessionStorage.getItem("pendingInvitation");
        const pendingInvitationUrl = getCookie(PENDING_INVITATION_COOKIE);

        if (pendingInvitationUrl) {
          // Si hay una invitación pendiente, mostrar mensaje y redirigir
          toast.success("Email verificado! Redireccionando a la invitación pendiente ...", {
            duration: 5000,
          });
          setTimeout(() => {
            router.push(pendingInvitationUrl);
          }, 2000);
        } else {
          // Si no hay invitación, redirigir al home
          // toast.success("Email verified successfully!");
          toast.success("Email verificado! Redireccionando al dashboard ...", {
            duration: 5000,
          });
          router.push("/dashboard");
        }

      } catch (error) {
        console.error("Error:", error);
        toast.error("There was an error verifying your email");
      }
    };

    checkPendingInvitation();
  }, [router]);

  return (
    <div className="flex flex-col items-center justify-center grow p-4">
      <h1 className="mb-4 text-2xl font-bold text-green-500">
        Email verificado!
      </h1>
      <p className="mb-4 text-gray-600">
        Tu email ha sido verificado exitosamente.
      </p>
      <Link
        href="/dashboard"
        className={buttonVariants({
          variant: "default",
        })}
      >
        Ir al Dashboard
      </Link>
    </div>
  );
}
