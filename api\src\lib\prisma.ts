import { PrismaClient } from '@prisma/client'

declare global {
  var prisma: typeof prismaClient | undefined
}

const prismaClient = new PrismaClient({
  // log: ['query', 'info', 'warn', 'error']
  // log: env.NODE_ENV === 'development' ? ["query", "info", "warn", "error"] : ["error"]
})

export const prisma = global.prisma || prismaClient;

if (process.env.NODE_ENV !== 'production') {
  global.prisma = prisma
}

/* 
okey perfecto, dame de nuevo todas las funciones para encriptar y desencriptar datos (solamente, sin incluir lo de los metodos de pago con la db, las puras clases con la funcionalidad) utilizando aws kms
*/
