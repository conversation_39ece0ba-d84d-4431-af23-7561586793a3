import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Calendar<PERSON>lock, Clock, CheckCircle, XCircle } from "lucide-react"

const stats = [
  {
    title: "Reservas Activas",
    value: "2",
    change: "Próxima: 15 Ene",
    icon: CalendarClock,
    color: "text-blue-600",
  },
  {
    title: "Pendientes",
    value: "1",
    change: "Esperando confirmación",
    icon: Clock,
    color: "text-yellow-600",
  },
  {
    title: "Completadas",
    value: "8",
    change: "+2 este mes",
    icon: CheckCircle,
    color: "text-green-600",
  },
  {
    title: "Canceladas",
    value: "1",
    change: "Sin penalización",
    icon: XCircle,
    color: "text-red-600",
  },
]

export function ClientReservationStats() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {stats.map((stat, index) => (
        <Card key={index}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
            <stat.icon className={`h-4 w-4 ${stat.color}`} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
            <p className="text-xs text-muted-foreground">{stat.change}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
