'use client'

import { DateTime } from 'luxon'
import Image from 'next/image'
import React from 'react'

interface VehicleGalleryProps {
  vehicle: any
  images: string[]
}

export function VehicleGallery({ vehicle, images, /* currentImageIndex, setCurrentImageIndex */ }: VehicleGalleryProps) {

  const [currentImageIndex, setCurrentImageIndex] = React.useState(0)
  const [isHovered, setIsHovered] = React.useState(false)
  const isRecent = (createdAt: string) => {
    const createdDate = DateTime.fromISO(createdAt);
    const sevenDaysAgo = DateTime.now().minus({ days: 7 });
    return createdDate > sevenDaysAgo;
  }

  return (
    <>
      <div className="relative" >

        {isRecent(vehicle.createdAt) && (
          <div className="absolute top-4 left-4 z-10">
            <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-md">
              Recién publicado
            </span>
          </div>
        )}

        {/* Imagen principal */}
        <div className="relative aspect-[4/3] bg-gray-100 rounded-lg overflow-hidden" onMouseEnter={() => setIsHovered(true)} onMouseLeave={() => setIsHovered(false)}>
          <Image
            src={images[currentImageIndex] || "/placeholder.svg"}
            alt={`${vehicle.make} ${vehicle.model}`}
            fill
            className="object-cover"
          />

          {/* Controles de navegación */}
          <div
            className="absolute bottom-4 right-4 bg-white/80 rounded-full px-3 py-1 text-sm"
            style={{ opacity: isHovered ? 1 : 0, transition: 'opacity 0.3s ease' }}
          >
            {currentImageIndex + 1} / {images.length}
          </div>

          <button
            className="absolute left-4 top-1/2 -translate-y-1/2 bg-white rounded-full p-2"
            onClick={() => {
              setCurrentImageIndex((prev) => (prev > 0 ? prev - 1 : images.length - 1))
            }}
            style={{ opacity: isHovered ? 1 : 0, transition: 'opacity 0.3s ease' }}
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M15 18L9 12L15 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
          </button>

          <button
            className="absolute right-4 top-1/2 -translate-y-1/2 bg-white rounded-full p-2"
            onClick={() => setCurrentImageIndex(prev => (prev < images.length - 1 ? prev + 1 : 0))}
            style={{ opacity: isHovered ? 1 : 0, transition: 'opacity 0.3s ease' }}
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
          </button>
        </div>

        {/* Miniaturas */}
        <div className="grid grid-cols-6 gap-2 mt-2">
          {images.slice(0, 6).map((image, index) => (
            <div
              key={index}
              className={`aspect-[4/3] rounded-md overflow-hidden cursor-pointer border-2 ${index === currentImageIndex ? 'border-blue-500' : 'border-transparent'
                }`}
              onClick={() => setCurrentImageIndex(index)}
            >
              <Image
                src={image || "/placeholder.svg"}
                alt={`${vehicle.make} ${vehicle.model} - Vista ${index + 1}`}
                width={100}
                height={75}
                className="w-full h-full object-cover"
              />
            </div>
          ))}
        </div>
      </div>
    </>
  )
}
