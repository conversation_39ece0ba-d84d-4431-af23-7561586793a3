"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { AdditionalRoleRequest } from "@/components/settings/additional-role-request"

export default function HostSettingsPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Configuración</h1>
        <p className="text-muted-foreground">Gestiona tu perfil y preferencias de anfitrión</p>
      </div>

      <Tabs defaultValue="profile" className="space-y-4">
        <TabsList>
          <TabsTrigger value="profile">Perfil</TabsTrigger>
          <TabsTrigger value="notifications">Notificaciones</TabsTrigger>
          <TabsTrigger value="pricing">Precios</TabsTrigger>
          <TabsTrigger value="policies">Políticas</TabsTrigger>
          <TabsTrigger value="security">Seguridad</TabsTrigger>
        </TabsList>

        <TabsContent value="profile" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Información Personal</CardTitle>
              <CardDescription>Actualiza tu información de perfil</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName">Nombre</Label>
                  <Input id="firstName" defaultValue="Carlos" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastName">Apellido</Label>
                  <Input id="lastName" defaultValue="Rodriguez" />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input id="email" type="email" defaultValue="<EMAIL>" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">Teléfono</Label>
                <Input id="phone" defaultValue="+****************" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="bio">Biografía</Label>
                <Textarea
                  id="bio"
                  placeholder="Cuéntanos sobre ti como anfitrión..."
                  defaultValue="Anfitrión experimentado con más de 3 años en la plataforma. Me encanta ayudar a los viajeros a tener la mejor experiencia posible."
                />
              </div>
              <Button>Guardar Cambios</Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Verificaciones</CardTitle>
              <CardDescription>Estado de tus verificaciones</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Identidad</p>
                  <p className="text-sm text-muted-foreground">Documento de identidad verificado</p>
                </div>
                <Badge variant="default">Verificado</Badge>
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Licencia de Conducir</p>
                  <p className="text-sm text-muted-foreground">Licencia válida verificada</p>
                </div>
                <Badge variant="default">Verificado</Badge>
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Información Bancaria</p>
                  <p className="text-sm text-muted-foreground">Cuenta para recibir pagos</p>
                </div>
                <Badge variant="secondary">Pendiente</Badge>
              </div>
            </CardContent>
          </Card>

          {/* Componente para solicitar rol adicional */}
          <AdditionalRoleRequest />
        </TabsContent>

        <TabsContent value="notifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Preferencias de Notificación</CardTitle>
              <CardDescription>Configura cómo quieres recibir notificaciones</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Nuevas Reservas</p>
                    <p className="text-sm text-muted-foreground">
                      Recibir notificación cuando alguien reserve un vehículo
                    </p>
                  </div>
                  <Switch defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Mensajes</p>
                    <p className="text-sm text-muted-foreground">Notificaciones de nuevos mensajes</p>
                  </div>
                  <Switch defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Pagos</p>
                    <p className="text-sm text-muted-foreground">Confirmaciones de pagos recibidos</p>
                  </div>
                  <Switch defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Recordatorios</p>
                    <p className="text-sm text-muted-foreground">Recordatorios de entrega y recogida</p>
                  </div>
                  <Switch defaultChecked />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Canales de Notificación</CardTitle>
              <CardDescription>Elige cómo recibir las notificaciones</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Email</p>
                  <p className="text-sm text-muted-foreground">Notificaciones por correo electrónico</p>
                </div>
                <Switch defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">SMS</p>
                  <p className="text-sm text-muted-foreground">Mensajes de texto para notificaciones urgentes</p>
                </div>
                <Switch />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Push</p>
                  <p className="text-sm text-muted-foreground">Notificaciones push en la aplicación</p>
                </div>
                <Switch defaultChecked />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="pricing" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Configuración de Precios</CardTitle>
              <CardDescription>Establece tus estrategias de precios</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="basePrice">Precio Base por Día</Label>
                <Input id="basePrice" type="number" defaultValue="50" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="weeklyDiscount">Descuento Semanal (%)</Label>
                <Input id="weeklyDiscount" type="number" defaultValue="10" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="monthlyDiscount">Descuento Mensual (%)</Label>
                <Input id="monthlyDiscount" type="number" defaultValue="20" />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Precios Dinámicos</p>
                  <p className="text-sm text-muted-foreground">Ajustar precios automáticamente según la demanda</p>
                </div>
                <Switch />
              </div>
              <Button>Guardar Configuración</Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="policies" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Políticas de Renta</CardTitle>
              <CardDescription>Define las reglas para tus vehículos</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="minAge">Edad Mínima del Conductor</Label>
                <Select defaultValue="21">
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="18">18 años</SelectItem>
                    <SelectItem value="21">21 años</SelectItem>
                    <SelectItem value="25">25 años</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="cancellation">Política de Cancelación</Label>
                <Select defaultValue="flexible">
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="flexible">Flexible</SelectItem>
                    <SelectItem value="moderate">Moderada</SelectItem>
                    <SelectItem value="strict">Estricta</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="mileage">Límite de Kilometraje (por día)</Label>
                <Input id="mileage" type="number" defaultValue="200" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="rules">Reglas Adicionales</Label>
                <Textarea
                  id="rules"
                  placeholder="Ej: No fumar, no mascotas, etc."
                  defaultValue="- No fumar en el vehículo&#10;- No se permiten mascotas&#10;- Devolver con el mismo nivel de combustible"
                />
              </div>
              <Button>Actualizar Políticas</Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Seguridad de la Cuenta</CardTitle>
              <CardDescription>Protege tu cuenta con estas configuraciones</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="currentPassword">Contraseña Actual</Label>
                <Input id="currentPassword" type="password" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="newPassword">Nueva Contraseña</Label>
                <Input id="newPassword" type="password" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Confirmar Nueva Contraseña</Label>
                <Input id="confirmPassword" type="password" />
              </div>
              <Button>Cambiar Contraseña</Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Autenticación de Dos Factores</CardTitle>
              <CardDescription>Agrega una capa extra de seguridad</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">2FA por SMS</p>
                  <p className="text-sm text-muted-foreground">Recibir códigos por mensaje de texto</p>
                </div>
                <Switch />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">App Autenticadora</p>
                  <p className="text-sm text-muted-foreground">Usar Google Authenticator o similar</p>
                </div>
                <Switch />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
