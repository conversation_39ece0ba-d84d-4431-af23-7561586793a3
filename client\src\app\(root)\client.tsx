'use client'
import { BEARER_COOKIE_NAME } from '@/constants';
import { setCookie } from 'cookies-next/client';
import { useSearchParams } from 'next/navigation';
import { useEffect } from 'react'

export default function RootClient() {

  const params = useSearchParams();
  useEffect(() => {
    const tokenClient = params.get('tcl');
    console.log('tokenClient: ', tokenClient);

    if (tokenClient) {
      console.log('tokenClient: ', tokenClient);
      setCookie(BEARER_COOKIE_NAME, tokenClient);
      console.log('Bearer token set: ', tokenClient);
      // const url = new URL(window.location.href);
      // url.searchParams.delete('tcl');
      // window.history.replaceState({}, '', url.toString());
    }

  }, [params])

  return null;
}
