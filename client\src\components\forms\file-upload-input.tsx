
'use client'
import { <PERSON>Upload, X } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  FileUpload,
  FileUploadDropzone,
  FileUploadItem,
  FileUploadItemDelete,
  FileUploadItemMetadata,
  FileUploadItemPreview,
  FileUploadList,
  FileUploadTrigger,
} from "@/components/ui/file-upload";
import { cn } from '@/lib/utils';

// Infered type from the incoming form props
type FileUploadFormProps = {
  form: any;
  name: string;
  label: string;
  description?: string;
  minFiles?: number;
  maxFiles?: number;
  maxSize?: number;
  accept?: string;
  multiple?: boolean;
};

export default function FileUploadInput({
  form,
  name,
  label,
  description,
  maxFiles = 10,
  maxSize = 2 * 1024 * 1024, // 2MB por defecto
  accept = "image/*",
  multiple = false,
}: FileUploadFormProps) {
  'use no memo';
  const error = form.formState.errors[name];

  return (
    <>
      <FormField
        control={form.control}
        name={name}
        render={({ field }) => (
          <FormItem>
            <FormLabel>{label}</FormLabel>
            <FormControl>
              <FileUpload
                value={field.value}
                onValueChange={(files) => {
                  // Si no es multiple, reemplazar el archivo en lugar de agregar
                  if (!multiple) {
                    // Siempre tomamos solo el último archivo seleccionado
                    field.onChange([files[files.length - 1]]);
                  } else {
                    field.onChange(files);
                  }
                }}
                accept={accept}
                // Si no es multiple, permitir siempre seleccionar un archivo nuevo
                maxFiles={multiple ? maxFiles : undefined}
                maxSize={maxSize}
                onFileReject={(_, message) => {
                  form.setError(name, {
                    message,
                  });
                }}
                multiple={multiple}
              >
                <FileUploadDropzone
                  // className="flex-row flex-wrap border-dotted text-center"
                  className={cn(
                    "flex-row flex-wrap border-dotted text-center",
                    error && "border-red-500"
                  )}
                >
                  <CloudUpload className="size-4" />
                  Arrastra archivos aquí o
                  <FileUploadTrigger asChild>
                    <Button variant="link" size="sm" className="p-0 mt-1">
                      {/* Selecciona archivos */}
                      {
                        multiple ? "Selecciona archivos" : "Selecciona archivo"
                      }
                    </Button>
                  </FileUploadTrigger>
                  para subir
                </FileUploadDropzone>
                <FileUploadList>
                  {field.value && (field.value as File[])
                    .filter((file): file is File => file != null && file instanceof File)
                    .map((file, index) => (
                    <FileUploadItem key={index} value={file}>
                      <FileUploadItemPreview />
                      <FileUploadItemMetadata />
                      <FileUploadItemDelete asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="size-7"
                        >
                          <X />
                          <span className="sr-only">Eliminar</span>
                        </Button>
                      </FileUploadItemDelete>
                    </FileUploadItem>
                  ))}

                </FileUploadList>
              </FileUpload>
            </FormControl>
            {description && <FormDescription>{description}</FormDescription>}
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  )
}



