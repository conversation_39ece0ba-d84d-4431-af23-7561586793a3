import { Elysia, t } from "elysia";
import { authMiddleware } from "@/app/middlewares/auth.middleware";
import { UserVerificationService } from "./user-verification.service";
import { checkAdmin } from '@/lib/check-admin';

// Esquema para documentos de verificación
const verificationFilesSchema = t.Object({
  idFront: t.Optional(t.Files({ maxFileSize: 2 * 1024 * 1024, maxItems: 1 })),
  idBack: t.Optional(t.Files({ maxFileSize: 2 * 1024 * 1024, maxItems: 1 })),
  driverLicense: t.Optional(t.Files({ maxFileSize: 2 * 1024 * 1024, maxItems: 1 })),
  addressProof: t.Optional(t.Files({ maxFileSize: 2 * 1024 * 1024, maxItems: 1 })),
  selfieWithId: t.Optional(t.Files({ maxFileSize: 2 * 1024 * 1024, maxItems: 1 })),
  // Flags para eliminar documentos existentes
  remove_idFront: t.Optional(t.String()),
  remove_idBack: t.Optional(t.String()),
  remove_driverLicense: t.Optional(t.String()),
  remove_addressProof: t.Optional(t.String()),
  remove_selfieWithId: t.Optional(t.String()),
});

// Esquema para rechazo
const rejectSchema = t.Object({
  notes: t.String(),
});

// Controlador para verificación de usuarios
export const userVerificationController = new Elysia({ prefix: '/user/verification' })
  .use(authMiddleware)
  // Obtener estado de verificación
  .get('/status', async ({ user }) => {
    return await UserVerificationService.getVerificationStatus(user.id);
  })
  // Subir documentos de verificación
  .post('/upload',
    async ({ user, body }) => {
      console.log('Uploading verification documents for user with ID:', user.id);

      // Separar archivos de flags de eliminación
      const files = {
        idFront: body.idFront,
        idBack: body.idBack,
        driverLicense: body.driverLicense,
        addressProof: body.addressProof,
        selfieWithId: body.selfieWithId,
      };


      return await UserVerificationService.uploadVerificationDocuments({
        userId: user.id,
        files,
      });
    },
    {
      body: verificationFilesSchema
    }
  )
  .put('/upload',
    async ({ user, body }) => {
      console.log('Uploading verification documents for user with ID:', user.id);

      // Separar archivos de flags de eliminación
      const files = {
        idFront: body.idFront,
        idBack: body.idBack,
        driverLicense: body.driverLicense,
        addressProof: body.addressProof,
        selfieWithId: body.selfieWithId,
      };

      console.log('Files:', files);

      const removeFlags = {
        remove_idFront: body.remove_idFront === 'true',
        remove_idBack: body.remove_idBack === 'true',
        remove_driverLicense: body.remove_driverLicense === 'true',
        remove_addressProof: body.remove_addressProof === 'true',
        remove_selfieWithId: body.remove_selfieWithId === 'true',
      };

      console.log('Remove flags:', removeFlags);

      return await UserVerificationService.uploadVerificationDocuments({
        userId: user.id,
        files,
        removeFlags,
      });
    },
    {
      body: verificationFilesSchema
    }
  );

// Controlador para administradores
export const adminUserVerificationController = new Elysia({ prefix: '/admin/host-verification' })
  .use(authMiddleware)
  .derive(({ user }) => {
    checkAdmin(user);
  })
  // Obtener todas las verificaciones
  .get('/', async ({ query }) => {
    // return await UserVerificationService.getAllVerifications();
    return await UserVerificationService.getAllVerifications({
      query: {
        page: query.page!,
        limit: query.limit!
      }
    });
  }, {
    query: t.Optional(t.Object({
      page: t.Number({ default: 1 }),
      limit: t.Number({ default: 10, min: 1, max: 50 }),
    }))
  })

  // Obtener verificación por ID
  .get('/:userId', async ({ params }) => {
    return await UserVerificationService.getVerificationById(params.userId);
  })

  // Listar verificaciones pendientes
  .get('/pending', async ({ query }) => {
    // return await UserVerificationService.getPendingVerifications();
    return await UserVerificationService.getPendingVerifications({
      query: {
        page: query.page!,
        limit: query.limit!
      }
    });
  }, {
    query: t.Optional(t.Object({
      page: t.Number({ default: 1 }),
      limit: t.Number({ default: 10, min: 1, max: 50 }),
    }))
  })
  // Aprobar verificación
  .post('/:userId/approve', async ({ params }) => {
    return await UserVerificationService.approveVerification(params.userId);
  })
  // Rechazar verificación
  .post('/:userId/reject',
    async ({ params, body }) => {
      return await UserVerificationService.rejectVerification(params.userId, body.notes);
    },
    {
      body: rejectSchema
    }
  );
