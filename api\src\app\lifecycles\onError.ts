import Elysia from 'elysia';
import { colorizeRequest } from '../utils/colorizeLogRequest';
import { HttpException } from '@/exceptions/HttpExceptions';
import { Prisma } from '@prisma/client';

const error = new Elysia()
  .state({
    start: 0,
    requestId: '',
  })

type OnErrorParam = Parameters<typeof error.onError>[1];

// Mapeo de códigos de error de Prisma a mensajes amigables
const prismaErrorMessages: Record<string, string> = {
  P2002: 'Ya existe un registro con este valor único',
  P2003: 'Violación de restricción de clave foránea',
  P2025: 'Registro no encontrado',
  P2014: 'Violación de restricción de relación',
  P2000: 'El valor proporcionado es demasiado largo',
  P2001: 'El registro no existe',
  P2005: 'El valor proporcionado para el campo es inválido',
  P2006: 'El valor proporcionado no es válido',
  P2007: 'Error de validación de datos',
  P2008: 'Error en la consulta',
  P2009: 'Error en la consulta de validación',
  P2010: 'Error en la consulta raw',
  P2011: 'Error de valor nulo',
  P2012: 'Error de campo requerido',
  P2013: 'Error de tipo de dato',
  P2015: 'Relación no encontrada',
  P2016: 'Error en la consulta',
  P2017: 'Relación no encontrada',
  P2018: 'Relación requerida',
  P2019: 'Error de entrada',
  P2020: 'Error de valor',
  P2021: 'La tabla no existe',
  P2022: 'La columna no existe',
  P2023: 'Error de inconsistencia',
  P2024: 'Error de tiempo de espera',
  P2026: 'Error en la consulta',
  P2027: 'Error de mapeo',
  P2028: 'Error de transacción',
  P2030: 'Error de conexión',
  P2033: 'Error de número',
  P2034: 'Error de transacción'
};

// Función para obtener un mensaje de error amigable basado en el error de Prisma
function getPrismaErrorMessage(error: Prisma.PrismaClientKnownRequestError): { message: string, field?: string } {
  const code = error.code;
  let message = prismaErrorMessages[code] || 'Error en la operación de base de datos';
  let field: string | undefined;

  // Personalizar mensaje según el código de error
  if (code === 'P2002') {
    const target = error.meta?.target as string[] | undefined;
    field = target?.[0];

    if (field) {
      // Mapeo de nombres de campo a nombres amigables
      const fieldNames: Record<string, string> = {
        vin: 'número VIN',
        plate: 'placa',
        email: 'correo electrónico',
        username: 'nombre de usuario',
        phone: 'número de teléfono'
      };

      const friendlyFieldName = fieldNames[field] || field;
      message = `Ya existe un registro con este ${friendlyFieldName}`;
    }
  }

  return { message, field };
}

export const onError: OnErrorParam = (ctx) => {
  const { code, error, path, request, store } = ctx;

  // console.log('code:', code);
  console.log('error:', error);
  // console.log('path:', path);
  // console.log('request:', request);

  if (path === '/favicon.ico') {
    return;
  }

  const end = performance.now();
  const method = request.method;
  const time = end - store.start;
  const timeTreeDecimals = +time.toFixed(3);

  // Manejar errores de Prisma
  if (error instanceof Prisma.PrismaClientKnownRequestError) {
    const { message, field } = getPrismaErrorMessage(error);
    const status = 400; // La mayoría de errores de Prisma son errores de cliente (400)

    ctx.set.status = status;
    colorizeRequest(method, path, status, timeTreeDecimals);

    return {
      message,
      field,
      code: error.code,
      status,
      type: 'prisma_error'
    };
  }

  // Manejar errores de validación de Prisma
  if (error instanceof Prisma.PrismaClientValidationError) {
    const status = 400;
    ctx.set.status = status;
    colorizeRequest(method, path, status, timeTreeDecimals);

    return {
      message: 'Error de validación en los datos proporcionados',
      status,
      type: 'prisma_validation_error'
    };
  }

  // Manejar errores HTTP personalizados
  if (error instanceof HttpException) {
    ctx.set.status = error.statusCode;
    colorizeRequest(method, path, error.statusCode, timeTreeDecimals);
    return {
      message: error.message,
      status: error.statusCode,
    }
  }

  // Manejar otros errores
  if (error instanceof Error && code !== 'VALIDATION') {
    ctx.set.status = 404;
    colorizeRequest(method, path, 404, timeTreeDecimals);
    return {
      // message: error.message,
      message: 'Something went wrong', // Avoid exposing app functions errors to the errors
      status: 404,
    }
  }

  // El resto del código para manejar errores de validación, etc.
  if (code === 'VALIDATION') {
    const message = error.message;
    const parsed = JSON.parse(message);

    const { errors, type, on } = parsed;

    // return parsed;
    const updatedErrors = errors.map((err: any) => {
      if (err.message.includes("Expected string to match")) {

        const property = err.path.replace("/", '');
        const prop = "'" + property + "'";

        if (err.summary.includes("Expected string to match")) {
          err.summary = `The value provided for ${prop} is invalid. Please ensure it doesn't start or end with spaces and meets the length requirements.`;
        }
        if ('pattern' in err.schema) {
          console.log('pattern', err.schema.pattern);
        }
        return {
          ...err,
          message: `The value provided for ${prop} is invalid. Please ensure it doesn't start or end with spaces and meets the length requirements.`
        };
      }

      if ('pattern' in err.schema) {
        delete err.schema.pattern;
      }

      return err;
    });

    const validationStatus = 400;
    colorizeRequest(method, path, validationStatus, timeTreeDecimals);
    ctx.set.status = validationStatus;
    return {
      // ...parsed,
      type,
      on,
      errors: updatedErrors,
    };
  }

  // Error genérico
  ctx.set.status = 500;
  return {
    status: 500,
    message: 'Internal server error'
  }
}
