"use client"

import { useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { useQuery } from "@tanstack/react-query"
import { vehiclesApi } from "@/lib/api/vehicles.api"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { CheckCircle, Filter, Plus, Search } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"
import { AdminVehicleTable } from "@/components/admin/admin-vehicle-table"
import { AdminVehicleStats } from "@/components/admin/admin-vehicle-stats"


export default function AdminVehiclesPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const router = useRouter()
  const searchParams = useSearchParams()

  // Obtener parámetros de paginación
  const page = Number(searchParams.get('page') || 1)
  const limit = Number(searchParams.get('limit') || 10)

  const { data, isLoading, error } = useQuery({
    queryKey: ['admin-vehicles-list', page, limit],
    queryFn: () => vehiclesApi.admin.getAll({ page, limit }),
  })

  const vehicles = data?.data || []
  const pagination = data?.pagination || { total: 0, totalPages: 1 }

  // Filtrar vehículos según el término de búsqueda
  const filteredVehicles = vehicles.filter(
    (vehicle) =>
      vehicle.make.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vehicle.model.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vehicle.plate?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vehicle.host.name.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  // Manejar cambio de página
  const handlePageChange = (pageIndex: number) => {
    if (pageIndex !== Number(page)) {
      router.push(`/dashboard/admin/vehicles?page=${pageIndex}&limit=${limit}`)
    }
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          No se pudieron cargar los vehículos. Por favor, intenta de nuevo más tarde.
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Vehículos</h1>
          <p className="text-muted-foreground">Supervisa todos los vehículos registrados en la plataforma</p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline" onClick={() => router.push("/dashboard/admin/vehicles/approvals")}>
            <CheckCircle className="mr-2 h-4 w-4" />
            Vehículos Pendientes
          </Button>
          <Button onClick={() => router.push("/dashboard/admin/vehicles/new")}>
            <Plus className="mr-2 h-4 w-4" />
            Registrar Vehículo
          </Button>
        </div>
      </div>

      {/* Stats Cards - Componente independiente */}
      <AdminVehicleStats />

      {/* Vehicles Table */}
      <Card>
        <CardHeader>
          <CardTitle>Lista de Vehículos</CardTitle>
          <CardDescription>Administra la información y estado de todos los vehículos</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Buscar vehículos..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <Button variant="outline">
              <Filter className="mr-2 h-4 w-4" />
              Filtros
            </Button>
          </div>

          <div className="rounded-md border">

            <AdminVehicleTable
              vehicles={filteredVehicles}
              rowCount={pagination.total}
              isLoading={isLoading}
              pageSize={limit}
              pageIndex={page - 1}
              onPageChange={handlePageChange}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}


