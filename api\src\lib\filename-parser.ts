

/**
 * Parse a filename to remove empty spaces and special characters, adding a timestamp to avoid duplicates
 * @param filename The filename to parse
 * @returns The name and extension of the file
 */

import { randomId } from '.';

export function parseFilename(filename: string) {
  const name = filename.replace(/\s/g, '-');
  const timestamp = Date.now();
  // extension should be the last point, because can be multiple points in the name
  const extension = name.split('.').pop()!;
  // const nameWithoutExtension = name.split('.').slice(0, -1).join('.');

  const id = randomId(12);

  return `${timestamp}-${id}.${extension}`;
}