'use client';
import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { X } from "lucide-react";
import Image from 'next/image';
import { Skeleton } from '../ui/skeleton';
import { cn } from '@/lib/utils';

interface DocumentPreviewProps {
  documentKey: string | null;
  label: string;
  onRemove?: () => void;
  showButtonOnHover?: boolean;
}

// const DocumentPreview: React.FC<DocumentPreviewProps> = ({
export default function DocumentPreview({
  documentKey,
  label,
  onRemove,
  showButtonOnHover = true,
}: DocumentPreviewProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(false);

  if (!documentKey) {
    return (
      <div className="flex flex-col items-center justify-center p-4 border border-dashed rounded-md bg-muted/50 h-40">
        <p className="text-sm text-muted-foreground">No hay documento</p>
        <p className="text-xs text-muted-foreground mt-1">{label}</p>
      </div>
    );
  }

  // Construir URL para el documento privado
  // const documentUrl = `${process.env.NEXT_PUBLIC_API_URL}/api/v1/files/download?key=${documentKey}`;
  const documentUrl = `/api/proxy/v1/files/download?key=${documentKey}`;


  const handleImageLoad = () => {
    setIsLoading(false);
  };

  const handleImageError = () => {
    setIsLoading(false);
    setError(true);
  };

  return (
    <div className="relative border rounded-md overflow-hidden group">
      {isLoading && (
        <Skeleton className="w-full h-40" />
      )}

      {error ? (
        <div className="flex flex-col items-center justify-center p-4 bg-muted/50 h-40">
          <p className="text-sm text-muted-foreground">Error al cargar el documento</p>
          <p className="text-xs text-muted-foreground mt-1">{label}</p>
        </div>
      ) : (
        <>
          <div className="relative aspect-video">
            <Image
              src={documentUrl}
              alt={label}
              fill
                unoptimized={true}
              className={`object-cover transition-opacity duration-300 ${isLoading ? 'opacity-0' : 'opacity-100'}`}
              onLoad={handleImageLoad}
              onError={handleImageError}
            />

          </div>
          <div className="absolute inset-0 flex items-center justify-center bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity">
            <a
              href={documentUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="bg-white text-black px-3 py-1 rounded-md text-xs font-medium hover:bg-gray-200 transition-colors"
            >
              Ver documento
            </a>
          </div>
        </>
      )}

      {onRemove && (
        <Button
          variant="destructive"
          size="icon"
          // className="absolute top-2 right-2 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
          className={cn(
            // should do the same as above commented line but
            // if showButtonOnHover is false, it should always be visible
            // otherwise it should only be visible on hover
            "absolute top-2 right-2 h-6 w-6 ",
            !showButtonOnHover ? "opacity-100" : "opacity-0 group-hover:opacity-100 transition-opacity"
          )}
          onClick={onRemove}
        >
          <X className="h-4 w-4" />
        </Button>
      )}

      <div className="absolute bottom-0 left-0 right-0 bg-black/60 text-white text-xs p-1 text-center">
        {label}
      </div>
    </div>
  );
};
