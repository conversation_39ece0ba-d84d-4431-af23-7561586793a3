import { Elysia, t } from "elysia";
import { authMiddleware } from "@/app/middlewares/auth.middleware";
import { checkAdmin } from '@/lib/check-admin';
import { getQueueStats, reviewNotificationQueue, reviewQueueEvents } from '@/lib/bullmq';

// Controlador para administración de BullMQ (solo admins)
export const bullmqController = new Elysia({ prefix: '/admin/bullmq' })
  .use(authMiddleware)
  .derive(({ user }) => {
    checkAdmin(user);
  })
  .get('/stats', async () => {
    try {
      const stats = await getQueueStats();

      if (!stats) {
        return {
          success: false,
          error: 'Error obteniendo estadísticas de la queue',
          status: 500
        };
      }

      return {
        success: true,
        data: {
          queueStats: stats,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      console.error('Error getting BullMQ stats:', error);
      return {
        success: false,
        error: 'Error interno del servidor',
        status: 500
      };
    }
  })
  .get('/jobs', async ({ query }) => {

    try {
      const { status = 'waiting', start = '0', count = '10' } = query;
      const startNum = parseInt(start as string);
      const countNum = parseInt(count as string);

      let jobs;
      switch (status) {
        case 'waiting':
          jobs = await reviewNotificationQueue.getWaiting(startNum, startNum + countNum - 1);
          break;
        case 'active':
          jobs = await reviewNotificationQueue.getActive(startNum, startNum + countNum - 1);
          break;
        case 'completed':
          jobs = await reviewNotificationQueue.getCompleted(startNum, startNum + countNum - 1);
          break;
        case 'failed':
          jobs = await reviewNotificationQueue.getFailed(startNum, startNum + countNum - 1);
          break;
        default:
          jobs = await reviewNotificationQueue.getWaiting(startNum, startNum + countNum - 1);
      }

      return {
        success: true,
        data: {
          jobs: jobs.map(job => ({
            id: job.id,
            name: job.name,
            data: job.data,
            opts: job.opts,
            progress: job.progress,
            returnvalue: job.returnvalue,
            failedReason: job.failedReason,
            timestamp: job.timestamp,
            processedOn: job.processedOn,
            finishedOn: job.finishedOn,
          })),
          pagination: {
            start: startNum,
            count: countNum,
            total: jobs.length
          }
        }
      };
    } catch (error) {
      console.error('Error getting BullMQ jobs:', error);
      return {
        success: false,
        error: 'Error interno del servidor',
        status: 500
      };
    }
  })
  .post('/jobs/:jobId/retry', async ({ params }) => {

    try {
      const job = await reviewNotificationQueue.getJob(params.jobId);

      if (!job) {
        return {
          success: false,
          error: 'Job no encontrado',
          status: 404
        };
      }

      await job.retry();

      return {
        success: true,
        message: 'Job reintentado exitosamente'
      };
    } catch (error) {
      console.error('Error retrying job:', error);
      return {
        success: false,
        error: 'Error interno del servidor',
        status: 500
      };
    }
  })
  .delete('/jobs/:jobId', async ({ params }) => {

    try {
      const job = await reviewNotificationQueue.getJob(params.jobId);

      if (!job) {
        return {
          success: false,
          error: 'Job no encontrado',
          status: 404
        };
      }

      await job.remove();

      return {
        success: true,
        message: 'Job eliminado exitosamente'
      };
    } catch (error) {
      console.error('Error removing job:', error);
      return {
        success: false,
        error: 'Error interno del servidor',
        status: 500
      };
    }
  });
