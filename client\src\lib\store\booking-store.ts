import { create } from "zustand"
import { persist } from "zustand/middleware"

export interface VehicleInfo {
  id: string
  make: string
  model: string
  year: number
  color: string
  vin?: string
  plate?: string
  price: number
}

export interface DateRange {
  startDate: Date | null
  endDate: Date | null
}

export interface Location {
  address: string
  city: string
  state: string
  zipCode: string
}

export interface CoverageOption {
  type: "basic" | "standard" | "premium"
  price: number
}

export interface AddOn {
  id: string
  name: string
  price: number
  selected: boolean
}

export interface BookingState {
  vehicle: VehicleInfo | null
  dateRange: DateRange
  pickupLocation: Location | null
  returnLocation: Location | null
  coverage: CoverageOption | null
  addOns: AddOn[]
  mileagePackage: {
    id: string
    name: string
    limit: number
    price: number
  } | null
  currentStep: number
  totalPrice: number
  contactInfo: {
    contactName?: string
    contactEmail?: string
    contactPhone?: string
  }

  // Actions
  setVehicle: (vehicle: VehicleInfo) => void
  setDateRange: (dateRange: DateRange) => void
  setPickupLocation: (location: Location) => void
  setReturnLocation: (location: Location) => void
  setCoverage: (coverage: CoverageOption) => void
  toggleAddOn: (id: string) => void
  setMileagePackage: (pkg: { id: string; name: string; limit: number; price: number }) => void
  nextStep: () => void
  prevStep: () => void
  goToStep: (step: number) => void
  calculateTotal: () => void
  reset: () => void
  setContactInfo: (contactInfo: {
    contactName?: string;
    contactEmail?: string;
    contactPhone?: string;
  }) => void
}

const initialState = {
  vehicle: null,
  dateRange: {
    startDate: null,
    endDate: null,
  },
  pickupLocation: null,
  returnLocation: null,
  coverage: null,
  addOns: [],
  mileagePackage: null,
  currentStep: 1,
  totalPrice: 0,
  contactInfo: {
    contactName: "",
    contactEmail: "",
    contactPhone: "",
  },
}

export const useBookingStore = create<BookingState>()(
  persist(
    (set, get) => ({
      ...initialState,

      setVehicle: (vehicle) => set({ vehicle }),

      setDateRange: (dateRange) => {
        set({ dateRange })
        get().calculateTotal()
      },

      setPickupLocation: (location) => set({ pickupLocation: location }),

      setReturnLocation: (location) => set({ returnLocation: location }),

      setCoverage: (coverage) => {
        set({ coverage })
        get().calculateTotal()
      },

      toggleAddOn: (id) => {
        const addOns = get().addOns.map((addon) => (addon.id === id ? { ...addon, selected: !addon.selected } : addon))
        set({ addOns })
        get().calculateTotal()
      },

      setMileagePackage: (pkg) => {
        set({ mileagePackage: pkg })
        get().calculateTotal()
      },

      nextStep: () => set((state) => ({ currentStep: state.currentStep + 1 })),

      prevStep: () => set((state) => ({ currentStep: state.currentStep - 1 })),

      goToStep: (step) => set({ currentStep: step }),

      calculateTotal: () => {
        const { vehicle, dateRange, coverage, addOns, mileagePackage } = get()

        if (!vehicle || !dateRange.startDate || !dateRange.endDate) {
          set({ totalPrice: 0 })
          return
        }

        const days = Math.ceil(
          (dateRange.endDate.getTime() - dateRange.startDate.getTime()) / (1000 * 60 * 60 * 24)
        )

        const basePrice = vehicle.price * days

        // Calculate total including coverage, addOns, and mileagePackage
        let total = basePrice

        // Add coverage cost if selected
        if (coverage) {
          total += coverage.price * days
        }

        // Add selected add-ons
        addOns.forEach((addon) => {
          if (addon.selected) {
            total += addon.price
          }
        })

        // Add mileage package if selected
        if (mileagePackage) {
          total += mileagePackage.price
        }

        set({ totalPrice: total })
      },

      reset: () => set(initialState),
      setContactInfo: (contactInfo) => set({ contactInfo }),
    }),
    {
      name: "autoop-booking-storage",
    },
  ),
)
