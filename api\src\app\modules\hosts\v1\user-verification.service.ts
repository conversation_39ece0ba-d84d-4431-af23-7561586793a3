import { prisma } from '@/lib/prisma';
import { HttpException } from '@/exceptions/HttpExceptions';
import { deleteFileFromR2, uploadUserVerificationFilesToR2 } from '@/services/r2';

export class UserVerificationService {

  // Obtener todas las verificaciones
  static async getAllVerifications({ query }: { query: { page: number; limit: number } }) {
    // return await prisma.userVerification.findMany({
    //   include: {
    //     user: {
    //       select: {
    //         id: true,
    //         name: true,
    //         email: true,
    //         image: true,
    //       }
    //     }
    //   },
    //   orderBy: {
    //     createdAt: 'asc'
    //   }
    // });
    const { page, limit } = query;

    const skip = (page - 1) * limit;

    const verifications = await prisma.userVerification.findMany({
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          }
        }
      },
      orderBy: {
        createdAt: 'asc' // Más antiguas primero para asegurar que se aprueben en orden conforme se reciban
      },
      take: limit,
      skip
    });

    const totalVerifications = await prisma.userVerification.count();
    const totalPages = Math.ceil(totalVerifications / limit);

    return {
      data: verifications,
      pagination: {
        page,
        limit,
        totalPages,
        totalItems: totalVerifications,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      }
    };
  }

  // Obtener verificación por ID
  static async getVerificationById(userId: string) {
    return await prisma.userVerification.findUnique({
      where: { userId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          }
        }
      }
    });
  }

  // Subir documentos de verificación
  static async uploadVerificationDocuments({
    userId,
    files,
    removeFlags,
  }: {
    userId: string;
    files: {
      idFront?: File[];
      idBack?: File[];
      driverLicense?: File[];
      addressProof?: File[];
      selfieWithId?: File[];
    };
    removeFlags?: {
      remove_idFront?: boolean;
      remove_idBack?: boolean;
      remove_driverLicense?: boolean;
      remove_addressProof?: boolean;
      remove_selfieWithId?: boolean;
    };
  }) {
    console.log('Uploading verification documents for user with ID:', userId);
    console.log('Incoming Files:', files);
    console.log('Remove flags:', removeFlags);

    // Verificar si el usuario existe y es host
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        userType: true,
        userVerification: true,
      }
    });

    if (!user) {
      throw HttpException.NotFound("User not found");
    }

    if (user.userType !== 'host') {
      throw HttpException.BadRequest("User is not a host");
    }

    // Manejar eliminación de archivos existentes si se especifica
    if (removeFlags && user.userVerification) {
      const filesToDelete: { [key: string]: string } = {};

      if (removeFlags.remove_idFront && user.userVerification.idFront) {
        filesToDelete.idFront = user.userVerification.idFront;
      }
      if (removeFlags.remove_idBack && user.userVerification.idBack) {
        filesToDelete.idBack = user.userVerification.idBack;
      }
      if (removeFlags.remove_driverLicense && user.userVerification.driverLicense) {
        filesToDelete.driverLicense = user.userVerification.driverLicense;
      }
      if (removeFlags.remove_addressProof && user.userVerification.addressProof) {
        filesToDelete.addressProof = user.userVerification.addressProof;
      }
      if (removeFlags.remove_selfieWithId && user.userVerification.selfieWithId) {
        filesToDelete.selfieWithId = user.userVerification.selfieWithId;
      }

      // Eliminar archivos marcados para eliminación
      for (const [field, filePath] of Object.entries(filesToDelete)) {
        try {
          // await deleteFileFromR2(filePath);
          await deleteFileFromR2({
            path: filePath,
            isPublic: false,
          });
          console.log(`Deleted file for field ${field}: ${filePath}`);
        } catch (error) {
          console.error(`Error deleting file for field ${field}:`, error);
          // Continuar con el proceso aunque falle la eliminación
        }
      }
    }

    // Guardar referencias a archivos anteriores ANTES de subir los nuevos
    const oldFilesToDelete: { [key: string]: string } = {};
    if (user.userVerification) {
      if (files.idFront && files.idFront.length > 0 && user.userVerification.idFront) {
        oldFilesToDelete.idFront = user.userVerification.idFront;
      }
      if (files.idBack && files.idBack.length > 0 && user.userVerification.idBack) {
        oldFilesToDelete.idBack = user.userVerification.idBack;
      }
      if (files.driverLicense && files.driverLicense.length > 0 && user.userVerification.driverLicense) {
        oldFilesToDelete.driverLicense = user.userVerification.driverLicense;
      }
      if (files.addressProof && files.addressProof.length > 0 && user.userVerification.addressProof) {
        oldFilesToDelete.addressProof = user.userVerification.addressProof;
      }
      if (files.selfieWithId && files.selfieWithId.length > 0 && user.userVerification.selfieWithId) {
        oldFilesToDelete.selfieWithId = user.userVerification.selfieWithId;
      }
    }

    console.log('Old files to delete after upload:', oldFilesToDelete);

    // Subir nuevos archivos usando la función especializada
    console.log('About to call uploadUserVerificationFilesToR2 with files:', files);
    const uploadedFiles = await uploadUserVerificationFilesToR2({
      userId,
      files,
    });

    console.log('Files uploaded successfully:', uploadedFiles);

    // Eliminar archivos anteriores DESPUÉS de subir los nuevos exitosamente
    for (const [field, filePath] of Object.entries(oldFilesToDelete)) {
      try {
        // await deleteFileFromR2(filePath);
        await deleteFileFromR2({
          path: filePath,
          isPublic: false,
        });
        console.log(`Deleted old file for field ${field}: ${filePath}`);
      } catch (error) {
        console.error(`Error deleting old file for field ${field}:`, error);
        // Continuar con el proceso aunque falle la eliminación
      }
    }

    // Preparar datos para actualizar/crear verificación
    const updateData: any = {};

    // Solo agregar campos que tienen archivos subidos
    if (uploadedFiles.idFront) {
      updateData.idFront = uploadedFiles.idFront;
    }
    if (uploadedFiles.idBack) {
      updateData.idBack = uploadedFiles.idBack;
    }
    if (uploadedFiles.driverLicense) {
      updateData.driverLicense = uploadedFiles.driverLicense;
    }
    if (uploadedFiles.addressProof) {
      updateData.addressProof = uploadedFiles.addressProof;
    }
    if (uploadedFiles.selfieWithId) {
      updateData.selfieWithId = uploadedFiles.selfieWithId;
    }

    // Manejar eliminación de campos específicos
    if (removeFlags) {
      if (removeFlags.remove_idFront) {
        updateData.idFront = null;
      }
      if (removeFlags.remove_idBack) {
        updateData.idBack = null;
      }
      if (removeFlags.remove_driverLicense) {
        updateData.driverLicense = null;
      }
      if (removeFlags.remove_addressProof) {
        updateData.addressProof = null;
      }
      if (removeFlags.remove_selfieWithId) {
        updateData.selfieWithId = null;
      }
    }

    console.log('Final updateData:', updateData);

    // Solo actualizar si hay cambios
    if (Object.keys(updateData).length > 0) {
      // Agregar timestamp de actualización
      updateData.updatedAt = new Date();

      console.log('About to update/create verification with data:', updateData);

      // Buscar verificación existente o crear una nueva
      let verification = await prisma.userVerification.findUnique({
        where: { userId }
      });

      console.log('Existing verification:', verification);

      if (verification) {
        // Actualizar verificación existente
        console.log('Updating existing verification...');
        console.log('Update data:', updateData);
        verification = await prisma.userVerification.update({
          where: { userId },
          data: updateData
        });
        console.log('Verification updated successfully:', verification);
      } else {
        // Crear nueva verificación
        console.log('Creating new verification...');
        verification = await prisma.userVerification.create({
          data: {
            userId,
            ...updateData,
            status: 'pending',
          }
        });
        console.log('Verification created successfully:', verification);
      }
    } else {
      console.log('No changes detected, updateData is empty');
    }

    // Si no hay cambios, devolver la verificación existente
    const existingVerification = await prisma.userVerification.findUnique({
      where: { userId }
    });

    return existingVerification || null;
  }

  // Obtener estado de verificación
  static async getVerificationStatus(userId: string) {
    const verification = await prisma.userVerification.findUnique({
      where: { userId },
    });

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        isHostVerified: true,
      }
    });

    return {
      data: {
        isVerified: user?.isHostVerified || false,
        verification: verification || {
          status: 'unverified',
          idFront: null,
          idBack: null,
          driverLicense: null,
          addressProof: null,
          selfieWithId: null,
        }
      }
    };
  }

  // Para administradores: aprobar verificación
  static async approveVerification(userId: string) {
    console.log('Approving verification for userId:', userId);

    // Verificar que la verificación existe
    const verification = await prisma.userVerification.findUnique({
      where: { userId }
    });

    if (!verification) {
      throw HttpException.NotFound("Verification not found");
    }

    // Actualizar el estado de verificación
    await prisma.userVerification.update({
      where: { userId },
      data: {
        status: 'approved',
        updatedAt: new Date(),
      }
    });

    // Actualizar el campo isHostVerified en el usuario
    await prisma.user.update({
      where: { id: userId },
      data: {
        isHostVerified: true,
      }
    });

    console.log('Verification approved successfully for userId:', userId);
    return { success: true, message: "Verification approved successfully" };
  }

  // Para administradores: rechazar verificación
  static async rejectVerification(userId: string, notes: string) {
    await prisma.userVerification.update({
      where: { userId },
      data: {
        status: 'rejected',
        notes,
      }
    });

    // Asegurarse de que isHostVerified sea false
    await prisma.user.update({
      where: { id: userId },
      data: {
        isHostVerified: false,
      }
    });

    return { success: true };
  }

  // Para administradores: listar verificaciones pendientes
  static async getPendingVerifications({ query }: { query: { page: number; limit: number } }) {
    const { page, limit } = query;

    const skip = (page - 1) * limit;

    const verifications = await prisma.userVerification.findMany({
      where: {
        status: 'pending'
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
            userType: true,
          }
        }
      },
      orderBy: {
        createdAt: 'asc' // Más antiguas primero para asegurar que se aprueben en orden conforme se reciban
      },
      take: limit,
      skip
    });

    const totalVerifications = await prisma.userVerification.count({
      where: {
        status: 'pending'
      }
    });
    const totalPages = Math.ceil(totalVerifications / limit);

    return {
      data: verifications,
      pagination: {
        page,
        limit,
        totalPages,
        totalItems: totalVerifications,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      }
    };
  }
}
