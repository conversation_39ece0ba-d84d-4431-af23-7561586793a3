import env from "@/constants/env";
import { NextResponse } from "next/server";

export const runtime = 'edge';

export async function GET(request: Request) {

    const headersStore = request.headers;

    const headers = Object.fromEntries(headersStore);

    if(headers.cookie) {
        delete headers.cookie;
    }

    return NextResponse.json({
        headers,
        apiUrl: env.NEXT_PUBLIC_API_URL,
    });
}