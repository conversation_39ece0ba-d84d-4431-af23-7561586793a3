import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Star, Heart } from "lucide-react"
import Image from "next/image"

const vehicles = [
  {
    id: 1,
    name: "Toyota Camry 2023",
    image: "/placeholder.svg?height=200&width=300",
    price: "$45",
    rating: 4.8,
    reviews: 124,
    location: "Centro, Ciudad",
    features: ["Automático", "5 asientos", "Gasolina", "A/C"],
    host: "<PERSON>",
    instantBook: true,
  },
  {
    id: 2,
    name: "Honda Civic 2022",
    image: "/placeholder.svg?height=200&width=300",
    price: "$38",
    rating: 4.6,
    reviews: 89,
    location: "Norte, Ciudad",
    features: ["Manual", "5 asientos", "Gasolina", "A/C"],
    host: "<PERSON> M.",
    instantBook: false,
  },
  {
    id: 3,
    name: "Nissan Sentra 2023",
    image: "/placeholder.svg?height=200&width=300",
    price: "$42",
    rating: 4.7,
    reviews: 156,
    location: "Sur, Ciudad",
    features: ["Automático", "5 asientos", "Gasolina", "A/C"],
    host: "<PERSON> G.",
    instantBook: true,
  },
  {
    id: 4,
    name: "Mazda CX-5 2023",
    image: "/placeholder.svg?height=200&width=300",
    price: "$65",
    rating: 4.9,
    reviews: 203,
    location: "Este, Ciudad",
    features: ["Automático", "5 asientos", "Gasolina", "A/C"],
    host: "María L.",
    instantBook: true,
  },
]

export function ClientVehicleGrid() {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Vehículos disponibles</h3>
        <p className="text-sm text-muted-foreground">{vehicles.length} vehículos encontrados</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {vehicles.map((vehicle) => (
          <Card key={vehicle.id} className="overflow-hidden hover:shadow-lg transition-shadow">
            <div className="relative">
              <Image
                src={vehicle.image || "/placeholder.svg"}
                alt={vehicle.name}
                width={300}
                height={200}
                className="w-full h-48 object-cover"
              />
              <Button variant="ghost" size="icon" className="absolute top-2 right-2 bg-white/80 hover:bg-white">
                <Heart className="h-4 w-4" />
              </Button>
              {vehicle.instantBook && <Badge className="absolute top-2 left-2 bg-green-600">Reserva Instantánea</Badge>}
            </div>

            <CardContent className="p-4">
              <div className="flex items-start justify-between mb-2">
                <h4 className="font-semibold text-lg">{vehicle.name}</h4>
                <div className="text-right">
                  <p className="font-bold text-lg">{vehicle.price}</p>
                  <p className="text-sm text-muted-foreground">por día</p>
                </div>
              </div>

              <div className="flex items-center gap-2 mb-2">
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <span className="text-sm font-medium">{vehicle.rating}</span>
                <span className="text-sm text-muted-foreground">({vehicle.reviews} reseñas)</span>
              </div>

              <p className="text-sm text-muted-foreground mb-3">{vehicle.location}</p>

              <div className="flex flex-wrap gap-1 mb-3">
                {vehicle.features.map((feature, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {feature}
                  </Badge>
                ))}
              </div>

              <div className="flex items-center justify-between">
                <p className="text-sm text-muted-foreground">Anfitrión: {vehicle.host}</p>
                <Button size="sm">Ver detalles</Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
