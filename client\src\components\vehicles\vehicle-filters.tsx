'use client'
import React from "react"
import { Checkbox } from "@/components/ui/checkbox"
import { Slider } from "@/components/ui/slider"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"

export default function VehicleFilters() {

  const [value, setValue] = React.useState([250])


  return (
    <div className="w-full lg:w-64 bg-white p-4 rounded-lg border sticky top-4">
      <div className="flex justify-between items-center mb-4">
        <h3 className="font-semibold text-lg">Filters</h3>
        <button className="text-sm text-blue-600 hover:underline">Clear All</button>
      </div>

      <Accordion type="multiple" defaultValue={["vehicleType", "price", "features", "rating"]}>
        <AccordionItem value="vehicleType">
          <AccordionTrigger className="py-2">Vehicle Types</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-2 pt-1">
              <div className="flex items-center">
                <Checkbox id="all" />
                <label htmlFor="all" className="ml-2 text-sm">
                  All Vehicle Types
                </label>
              </div>
              <div className="flex items-center">
                <Checkbox id="luxury" defaultChecked />
                <label htmlFor="luxury" className="ml-2 text-sm">
                  Luxury
                </label>
              </div>
              <div className="flex items-center">
                <Checkbox id="suv" />
                <label htmlFor="suv" className="ml-2 text-sm">
                  SUV
                </label>
              </div>
              <div className="flex items-center">
                <Checkbox id="sedan" />
                <label htmlFor="sedan" className="ml-2 text-sm">
                  Sedan
                </label>
              </div>
              <div className="flex items-center">
                <Checkbox id="sports" />
                <label htmlFor="sports" className="ml-2 text-sm">
                  Sports
                </label>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="price">
          <AccordionTrigger className="py-2">Price Range</AccordionTrigger>
          <AccordionContent>
            <div className="pt-2 pb-4">
              <Slider defaultValue={value} max={500} step={10} onValueChange={setValue} />
              <div className="flex justify-between mt-2">
                <span className="text-sm text-gray-500">$0</span>
                <span className="text-sm text-gray-500">${value[0]}</span>
                <span className="text-sm text-gray-500">$500</span>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="features">
          <AccordionTrigger className="py-2">Features</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-2 pt-1">
              <div className="flex items-center">
                <Checkbox id="automatic" defaultChecked />
                <label htmlFor="automatic" className="ml-2 text-sm">
                  Automatic
                </label>
              </div>
              <div className="flex items-center">
                <Checkbox id="hybrid" defaultChecked />
                <label htmlFor="hybrid" className="ml-2 text-sm">
                  Hybrid
                </label>
              </div>
              <div className="flex items-center">
                <Checkbox id="climate" />
                <label htmlFor="climate" className="ml-2 text-sm">
                  Climate Control
                </label>
              </div>
              <div className="flex items-center">
                <Checkbox id="leather" />
                <label htmlFor="leather" className="ml-2 text-sm">
                  Leather Seats
                </label>
              </div>
              <div className="flex items-center">
                <Checkbox id="sound" />
                <label htmlFor="sound" className="ml-2 text-sm">
                  Premium Sound
                </label>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="rating">
          <AccordionTrigger className="py-2">Rating</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-2 pt-1">
              <div className="flex items-center">
                <Checkbox id="rating-all" />
                <label htmlFor="rating-all" className="ml-2 text-sm">
                  All Ratings
                </label>
              </div>
              <div className="flex items-center">
                <Checkbox id="rating-5" defaultChecked />
                <label htmlFor="rating-5" className="ml-2 text-sm">
                  5.0 & Up
                </label>
              </div>
              <div className="flex items-center">
                <Checkbox id="rating-4" />
                <label htmlFor="rating-4" className="ml-2 text-sm">
                  4.0 & Up
                </label>
              </div>
              <div className="flex items-center">
                <Checkbox id="rating-3" />
                <label htmlFor="rating-3" className="ml-2 text-sm">
                  3.0 & Up
                </label>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="location">
          <AccordionTrigger className="py-2">Location</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-2 pt-1">
              <div className="flex items-center">
                <Checkbox id="location-all" defaultChecked />
                <label htmlFor="location-all" className="ml-2 text-sm">
                  All Locations
                </label>
              </div>
              <div className="flex items-center">
                <Checkbox id="location-downtown" />
                <label htmlFor="location-downtown" className="ml-2 text-sm">
                  Downtown
                </label>
              </div>
              <div className="flex items-center">
                <Checkbox id="location-airport" />
                <label htmlFor="location-airport" className="ml-2 text-sm">
                  Airport
                </label>
              </div>
              <div className="flex items-center">
                <Checkbox id="location-suburbs" />
                <label htmlFor="location-suburbs" className="ml-2 text-sm">
                  Suburbs
                </label>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      <Button className="w-full mt-4 bg-[#1a2b5e] hover:bg-[#152348]">
        <span className="font-medium">Apply Filters</span>
      </Button>
    </div>
  )
}

function Button({ children, className }: { children: React.ReactNode; className?: string }) {
  return <button className={`px-4 py-2 rounded-md text-white transition-colors ${className}`}>{children}</button>
}
