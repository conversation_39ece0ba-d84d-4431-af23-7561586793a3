import { Elysia, t } from 'elysia';
import { authMiddleware } from '@/app/middlewares/auth.middleware';
import { prisma } from '@/lib/prisma';
import { HttpException } from '@/exceptions/HttpExceptions';

const VALID_USER_TYPES = ['client', 'host'] as const;
type UserType = typeof VALID_USER_TYPES[number];

export const setUserTypeController = new Elysia({ prefix: '/user' })
  .use(authMiddleware)
  .post('/set-user-type', async ({ body, user }) => {
    const { userType } = body as { userType: UserType };

    // Validar que el userType sea válido
    if (!VALID_USER_TYPES.includes(userType)) {
      throw HttpException.BadRequest('Invalid user type. Must be "client" or "host"');
    }

    // Obtener el usuario actual
    const currentUser = await prisma.user.findUnique({
      where: { id: user.id },
      select: {
        id: true,
        userType: true,
        availableUserTypes: true,
        isHostVerified: true,
      }
    });

    if (!currentUser) {
      throw HttpException.NotFound('User not found');
    }

    // Si el usuario ya tiene un userType, no permitir cambio (usar switch-role en su lugar)
    if (currentUser.userType) {
      throw HttpException.BadRequest('User already has a userType. Use switch-role endpoint instead.');
    }

    // Establecer el userType inicial y availableUserTypes
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: { 
        userType,
        availableUserTypes: [userType],
        mainUserType: userType  // Establecer el tipo de usuario original
      },
      select: {
        id: true,
        userType: true,
        availableUserTypes: true,
        isHostVerified: true,
      }
    });

    return {
      success: true,
      message: `Tipo de usuario establecido como ${userType === 'host' ? 'Anfitrión' : 'Cliente'} satisfactoriamente.`,
      userType: updatedUser.userType,
      availableUserTypes: updatedUser.availableUserTypes,
      isHostVerified: updatedUser.isHostVerified,
    };
  }, {
    body: t.Object({
      userType: t.String()
    }),
    detail: {
      tags: ['User'],
      summary: 'Set initial user type',
      description: 'Set the initial user type for users who registered without selecting one (e.g., OAuth users)'
    }
  });
