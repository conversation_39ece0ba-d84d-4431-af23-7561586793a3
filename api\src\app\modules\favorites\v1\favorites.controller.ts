import { Elysia, t } from "elysia";
import { VehicleFavoritesService } from "./favorites.service";
import { auth } from "@/lib/auth";

// Controlador de favoritos para usuarios client
export const clientFavoritesController = new Elysia({ prefix: '/client/favorites' })
  // Obtener favoritos del usuario con paginación
  .get('/', async ({ query, headers }) => {
    try {
      // Verificar autenticación
      const session = await auth.api.getSession({ headers });
      if (!session?.user) {
        return {
          success: false,
          message: "No autorizado"
        };
      }

      const userId = session.user.id;

      // Verificar que el usuario tenga tipo client
      if (session.user.userType !== 'client') {
        return {
          success: false,
          message: "Solo usuarios de tipo client pueden acceder a favoritos"
        };
      }

      const result = await VehicleFavoritesService.getUserFavorites({
        userId,
        query: {
          page: query.page!,
          limit: query.limit!
        }
      });

      return result;
    } catch (error: any) {
      console.error('Error getting favorites:', error);
      return {
        success: false,
        message: "Error interno del servidor"
      };
    }
  }, {
    query: t.Optional(t.Object({
      page: t.Number({ default: 1 }),
      limit: t.Number({ default: 10, min: 1, max: 50 }),
    }))
  })

  // Agregar vehículo a favoritos
  .post('/:vehicleId', async ({ params, headers }) => {
    try {
      // Verificar autenticación
      const session = await auth.api.getSession({ headers });
      if (!session?.user) {
        return {
          success: false,
          message: "No autorizado"
        };
      }

      const userId = session.user.id;
      const { vehicleId } = params;

      // Verificar que el usuario tenga tipo client
      if (session.user.userType !== 'client') {
        return {
          success: false,
          message: "Solo usuarios de tipo client pueden agregar favoritos"
        };
      }

      const result = await VehicleFavoritesService.addToFavorites(userId, vehicleId);
      return result;
    } catch (error: any) {
      console.error('Error adding to favorites:', error);
      return {
        success: false,
        message: "Error interno del servidor"
      };
    }
  }, {
    params: t.Object({
      vehicleId: t.String()
    })
  })

  // Remover vehículo de favoritos
  .delete('/:vehicleId', async ({ params, headers }) => {
    try {
      // Verificar autenticación
      const session = await auth.api.getSession({ headers });
      if (!session?.user) {
        return {
          success: false,
          message: "No autorizado"
        };
      }

      const userId = session.user.id;
      const { vehicleId } = params;

      // Verificar que el usuario tenga tipo client
      if (session.user.userType !== 'client') {
        return {
          success: false,
          message: "Solo usuarios de tipo client pueden remover favoritos"
        };
      }

      const result = await VehicleFavoritesService.removeFromFavorites(userId, vehicleId);
      return result;
    } catch (error: any) {
      console.error('Error removing from favorites:', error);
      return {
        success: false,
        message: "Error interno del servidor"
      };
    }
  }, {
    params: t.Object({
      vehicleId: t.String()
    })
  })

  // Verificar si un vehículo está en favoritos
  .get('/:vehicleId/status', async ({ params, headers }) => {
    try {
      // Verificar autenticación
      const session = await auth.api.getSession({ headers });
      if (!session?.user) {
        return {
          success: false,
          message: "No autorizado",
          isFavorite: false
        };
      }

      const userId = session.user.id;
      const { vehicleId } = params;

      // Verificar que el usuario tenga tipo client
      if (session.user.userType !== 'client') {
        return {
          success: false,
          message: "Solo usuarios de tipo client pueden verificar favoritos",
          isFavorite: false
        };
      }

      const result = await VehicleFavoritesService.isFavorite(userId, vehicleId);
      return result;
    } catch (error: any) {
      console.error('Error checking favorite status:', error);
      return {
        success: false,
        message: "Error interno del servidor",
        isFavorite: false
      };
    }
  }, {
    params: t.Object({
      vehicleId: t.String()
    })
  })

  // Obtener estados de favoritos para múltiples vehículos
  .post('/status', async ({ body, headers }) => {
    try {
      // Verificar autenticación
      const session = await auth.api.getSession({ headers });
      if (!session?.user) {
        return {
          success: false,
          message: "No autorizado",
          data: {}
        };
      }

      const userId = session.user.id;
      const { vehicleIds } = body;

      // Verificar que el usuario tenga tipo client
      if (session.user.userType !== 'client') {
        return {
          success: false,
          message: "Solo usuarios de tipo client pueden verificar favoritos",
          data: {}
        };
      }

      const result = await VehicleFavoritesService.getFavoritesStatus(userId, vehicleIds);
      return result;
    } catch (error: any) {
      console.error('Error checking favorites status:', error);
      return {
        success: false,
        message: "Error interno del servidor",
        data: {}
      };
    }
  }, {
    body: t.Object({
      vehicleIds: t.Array(t.String())
    })
  });
