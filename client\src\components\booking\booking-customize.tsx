"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { useBookingStore } from "@/lib/store/booking-store"

export default function BookingCustomize() {
  const { vehicle, addOns, mileagePackage, toggleAddOn, setMileagePackage, nextStep, prevStep } = useBookingStore()

  // Initialize add-ons if not already set
  useState(() => {
    if (addOns.length === 0) {
      useBookingStore.setState({
        addOns: [
          { id: "gps", name: "GPS Navigation", price: 20, selected: false },
          { id: "childSeat", name: "Child Safety Seat", price: 45, selected: false },
          { id: "delivery", name: "Vehicle Delivery", price: 50, selected: false },
        ],
      })
    }
  })

  const mileageOptions = [
    { id: "standard", name: "Standard (200 km/day included)", limit: 200, price: 0 },
    { id: "extended", name: "Extended (300 km/day)", limit: 300, price: 20 },
    { id: "unlimited", name: "Unlimited", limit: -1, price: 40 },
  ]

  const handleMileageChange = (id: string) => {
    const selected = mileageOptions.find((option) => option.id === id)
    if (selected) {
      setMileagePackage(selected)
    }
  }

  return (
    <div className="max-w-3xl mx-auto">
      <div className="flex items-center mb-6">
        <div className="w-16 h-16 bg-gray-100 rounded-lg mr-4 flex items-center justify-center">
          <span className="text-[#1a2b5e] font-bold">
            {vehicle?.make.charAt(0)}
            {vehicle?.model.charAt(0)}
          </span>
        </div>
        <div>
          <h2 className="text-xl font-bold">
            {vehicle?.make} {vehicle?.model}
          </h2>
          <p className="text-gray-500">${vehicle?.price}/day</p>
        </div>
      </div>

      <h2 className="text-xl font-bold mb-6">Customize Your Trip</h2>

      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4">Mileage Package</h3>

        <div className="space-y-4">
          {mileageOptions.map((option) => (
            <div
              key={option.id}
              className={`border rounded-lg p-4 cursor-pointer transition-all ${
                mileagePackage?.id === option.id ? "border-[#1a2b5e] bg-blue-50" : "hover:border-gray-400"
              }`}
              onClick={() => handleMileageChange(option.id)}
            >
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <div
                    className={`w-5 h-5 rounded-full border flex items-center justify-center mr-3 ${
                      mileagePackage?.id === option.id ? "bg-[#1a2b5e] border-[#1a2b5e]" : "border-gray-300"
                    }`}
                  >
                    {mileagePackage?.id === option.id && <div className="w-2 h-2 rounded-full bg-white" />}
                  </div>
                  <div>
                    <div className="font-medium">{option.name}</div>
                    <div className="text-sm text-gray-500">
                      {option.limit === -1
                        ? "Drive as much as you want"
                        : `Perfect for trips with ${option.limit} km or less per day`}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  {option.price === 0 ? (
                    <span className="font-semibold text-green-600">Included</span>
                  ) : (
                    <>
                      <span className="font-semibold">+${option.price}/day</span>
                      <div className="text-xs text-gray-500">+${option.price * 3} total</div>
                    </>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4">Optional Add-ons</h3>

        <div className="space-y-4">
          {addOns.map((addon) => (
            <div
              key={addon.id}
              className={`border rounded-lg p-4 cursor-pointer transition-all ${
                addon.selected ? "border-[#1a2b5e] bg-blue-50" : "hover:border-gray-400"
              }`}
              onClick={() => toggleAddOn(addon.id)}
            >
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <Checkbox
                    id={addon.id}
                    checked={addon.selected}
                    onCheckedChange={() => toggleAddOn(addon.id)}
                    className="mr-3"
                  />
                  <div>
                    <label htmlFor={addon.id} className="font-medium cursor-pointer">
                      {addon.name}
                    </label>
                    {addon.id === "gps" && <div className="text-sm text-gray-500">Navigate with real-time traffic</div>}
                    {addon.id === "childSeat" && (
                      <div className="text-sm text-gray-500">Safe, appropriate child seat with installation</div>
                    )}
                    {addon.id === "delivery" && (
                      <div className="text-sm text-gray-500">Delivered to your specified location</div>
                    )}
                  </div>
                </div>
                <div className="text-right">
                  <span className="font-semibold">+${addon.price}/day</span>
                  <div className="text-xs text-gray-500">+${addon.price} total</div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="flex justify-between">
        <Button type="button" variant="outline" onClick={prevStep}>
          <span className="font-medium">Back</span>
        </Button>
        <Button type="button" className="bg-[#1a2b5e] hover:bg-[#152348]" onClick={nextStep}>
          <span className="font-medium">Continue</span>
        </Button>
      </div>
    </div>
  )
}
