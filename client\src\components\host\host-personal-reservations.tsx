"use client"

import { useState } from "react"
import { useQuery } from "@tanstack/react-query"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
// import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { /* MoreHorizontal, */ Eye, X } from "lucide-react"
import { format } from "date-fns"
import { es } from "date-fns/locale"
import { ReservationResponse, reservationsApi } from "@/lib/api/reservations.api"
import { useRouter } from "next/navigation"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { createColumns } from '../data-table/columns'
import { DataTable } from '../data-table/data-table'
import Image from 'next/image'

export function HostPersonalReservations() {
  const router = useRouter()
  const [cancelReservationId, setCancelReservationId] = useState<string | null>(null)

  const { data: reservations, isLoading, refetch } = useQuery({
    queryKey: ['host-personal-reservations'],
    queryFn: reservationsApi.host.getPersonalReservations,
    staleTime: 60 * 1000, // 1 minuto
  })

  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return <Badge className="bg-green-500">Confirmada</Badge>
      case 'pending':
        return <Badge className="bg-blue-500">Pendiente</Badge>
      case 'completed':
        return <Badge className="bg-gray-500">Completada</Badge>
      case 'cancelled':
        return <Badge className="bg-red-500">Cancelada</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "d MMM yyyy", { locale: es })
  }

  const handleCancelReservation = async () => {
    if (!cancelReservationId) return

    try {
      await reservationsApi.host.cancelReservation(cancelReservationId)
      refetch()
      setCancelReservationId(null)
    } catch (error) {
      console.error("Error al cancelar la reserva:", error)
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-96 w-full" />
      </div>
    )
  }

  if (!reservations || reservations.length === 0) {
    return (
      <div className="text-center p-8 border rounded-lg">
        <h3 className="text-lg font-medium">No tienes reservas personales</h3>
        <p className="text-muted-foreground mt-2">Aún no has reservado ningún vehículo.</p>
      </div>
    )
  }

  const columns = createColumns<ReservationResponse>([
    {
      accessorKey: "vehicle",
      header: "Vehículo",
      cell: ({ row }) => {
        const vehicle = row.original.vehicle
        return (
          <div className="flex items-center gap-2">
            {/* <img src={vehicle.image} alt={vehicle.name} className="h-10 w-10 rounded-md object-cover" /> */}
            <Image
              src={vehicle.images?.[0] || "/placeholder.svg?height=40&width=40"}
              alt={`${vehicle.make} ${vehicle.model}`}
              width={40}
              height={40}
              className="rounded-md object-cover"
            />
            <div>
              <p className="font-medium">{vehicle.make} {vehicle.model}</p>
              <p className="text-sm text-muted-foreground">{vehicle.year}</p>
            </div>
          </div>
        )
      },
    },
    {
      accessorKey: "dates",
      header: "Fechas",
      cell: ({ row }) => {
        const reservation = row.original
        return (
          <div>{formatDate(reservation.startDate)} - {formatDate(reservation.endDate)}</div>
        )
      },
    },
    {
      accessorKey: "reason",
      header: "Razón",
      cell: ({ row }) => {
        const reservation = row.original
        return (
          <div>{reservation.reason || "No especificada"}</div>
        )
      },
    },
    {
      accessorKey: "status",
      header: "Estado",
      cell: ({ row }) => {
        const reservation = row.original
        return (
          <div>{getStatusBadge(reservation.status)}</div>
        )
      },
    },
    {
      accessorKey: "totalPrice",
      header: "Total",
      cell: ({ row }) => {
        const reservation = row.original
        return (
          <div>${reservation.totalPrice}</div>
        )
      },
    },
    {
      accessorKey: "actions",
      header: "Acciones",
      cell: ({ row }) => {
        const reservation = row.original
        return (
          <div className="flex items-center justify-end gap-2">
            <Button variant="ghost" size="icon" onClick={() => router.push(`/dashboard/host/reservations/${reservation.id}`)}>
              <Eye className="h-4 w-4" />
            </Button>
            {['pending', 'confirmed'].includes(reservation.status.toLowerCase()) && (
              <AlertDialog open={cancelReservationId === reservation.id} onOpenChange={(open) => !open && setCancelReservationId(null)}>
                <AlertDialogTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <X className="h-4 w-4" />
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>¿Estás seguro?</AlertDialogTitle>
                    <AlertDialogDescription>
                      Esta acción no se puede deshacer. Cancelarás tu reserva para este vehículo.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancelar</AlertDialogCancel>
                    <AlertDialogAction onClick={handleCancelReservation}>Confirmar</AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}
          </div>
        )
      },
    },
  ])

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        {/* <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Vehículo</TableHead>
              <TableHead>Fechas</TableHead>
              <TableHead>Razón</TableHead>
              <TableHead>Estado</TableHead>
              <TableHead>Total</TableHead>
              <TableHead className="text-right">Acciones</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {reservations.map((reservation) => (
              <TableRow key={reservation.id}>
                <TableCell>
                  <div className="font-medium">{reservation.vehicle.make} {reservation.vehicle.model}</div>
                  <div className="text-sm text-muted-foreground">{reservation.vehicle.year}</div>
                </TableCell>
                <TableCell>
                  <div>{formatDate(reservation.startDate)} - {formatDate(reservation.endDate)}</div>
                </TableCell>
                <TableCell>
                  <div>{reservation.reason || "No especificada"}</div>
                </TableCell>
                <TableCell>
                  {getStatusBadge(reservation.status)}
                </TableCell>
                <TableCell>${reservation.totalPrice}</TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Abrir menú</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => router.push(`/vehicles/${reservation.vehicleId}`)}>
                        <Eye className="mr-2 h-4 w-4" />
                        Ver vehículo
                      </DropdownMenuItem>
                      {['pending', 'confirmed'].includes(reservation.status.toLowerCase()) && (
                        <AlertDialog open={cancelReservationId === reservation.id} onOpenChange={(open) => !open && setCancelReservationId(null)}>
                          <AlertDialogTrigger asChild>
                            <DropdownMenuItem onSelect={(e) => {
                              e.preventDefault()
                              setCancelReservationId(reservation.id)
                            }}>
                              <X className="mr-2 h-4 w-4" />
                              Cancelar reserva
                            </DropdownMenuItem>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>¿Estás seguro?</AlertDialogTitle>
                              <AlertDialogDescription>
                                Esta acción no se puede deshacer. Cancelarás tu reserva para este vehículo.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancelar</AlertDialogCancel>
                              <AlertDialogAction onClick={handleCancelReservation}>Confirmar</AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table> */}
        <DataTable
          columns={columns}
          data={reservations}
          isLoading={isLoading}
          rowCount={reservations.length}
          pageSize={10}
          pageIndex={0}
          onPageChange={() => { }}
        />
      </div>
    </div>
  )
}
