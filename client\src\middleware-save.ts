// import { getServerSession } from "./actions/getSession";
// import { NextResponse, type NextRequest } from "next/server";
// import { sendLogToLogflare } from "./lib/log-requests";
// import env from "@/constants/env";
// import { BEARER_COOKIE_NAME } from './constants';

// const authRoutes = ["/sign-in", "/sign-up"];
// const passwordRoutes = ["/reset-password", "/forgot-password"];
// const adminRoutes = ["/admin"];
// // Agregar rutas públicas que no requieren autenticación
// const publicRoutes = ["/accept-invitation"];

// export const runtime = 'experimental-edge';

// export default async function authMiddleware(request: NextRequest) {
//   const pathName = request.nextUrl.pathname;
  
//   if (pathName.includes('favicon.ico')) {
//     return NextResponse.next();
//   }

//   if (pathName.includes('.')) {
//     return NextResponse.next();
//   }

//   const isAuthRoute = authRoutes.includes(pathName);
//   const isPasswordRoute = passwordRoutes.includes(pathName);
//   const isAdminRoute = adminRoutes.includes(pathName);
//   // Verificar si es una ruta pública
//   const isPublicRoute = publicRoutes.some(route => pathName.startsWith(route));
//   const res = NextResponse.next();
  
//   // Start timing the request for performance logging
//   const startTime = Date.now();

//   const tokenQuery = request.nextUrl.searchParams.get('token');

//   // if token query comes
//   if (tokenQuery) {

//     // if token query is coming, I need to set it to the cookie and redirect to same page without the token query
//     const url = request.nextUrl.clone();
//     url.searchParams.delete('token');
//     res.cookies.set(BEARER_COOKIE_NAME, tokenQuery);
//     return NextResponse.redirect(url, {
//       headers: {
//         'Set-Cookie': `${BEARER_COOKIE_NAME}=${tokenQuery}`
//       }
//     });
//   }

//   try {
//     await logResponse(res, `Session log and API URL: ${env.NEXT_PUBLIC_API_URL}`, Date.now() - startTime, request, pathName);
//     const session = await getServerSession('middleware: ----');

//     // Set session to headers to be able to access it from Layout and Pages
//     res.headers.set('session', JSON.stringify(session));

//     // console.log('activeOrganizationId', session.session.activeOrganizationId)

//     await logResponse(res, 'Request processed', Date.now() - startTime, request, pathName, session);

//     if (!session) {
//       // Permitir acceso a rutas de autenticación y rutas públicas sin sesión
//       if (isAuthRoute || isPasswordRoute || isPublicRoute) {
//         const responseTime = Date.now() - startTime;
//         await logResponse(res, 'Public route access without session', responseTime, request, pathName);
//         return NextResponse.next();
//       }
//       const responseTime = Date.now() - startTime;
//       await logResponse(res, 'Redirecting to sign-in (no session)', responseTime, request, pathName);
//       return NextResponse.redirect(new URL("/sign-in", request.url));
//     }

//     if (isAuthRoute || isPasswordRoute) {
//       const responseTime = Date.now() - startTime;
//       await logResponse(res, 'Redirecting to home (already authenticated)', responseTime, request, pathName, session);
//       return NextResponse.redirect(new URL("/", request.url));
//     }

//     if (isAdminRoute && session.user.role !== "admin") {
//       const responseTime = Date.now() - startTime;
//       await logResponse(res, 'Unauthorized admin access attempt', responseTime, request, pathName, session);
//       return NextResponse.redirect(new URL("/", request.url));
//     }

//     const responseTime = Date.now() - startTime;
//     const requestMethod = request.method;
    
//     // log incoming request:
//     await logResponse(res,
//       `${requestMethod} ${pathName} ${request.url}`,
//       responseTime,
//       request,
//       pathName,
//       session
//     )
    
//     return res;
//   } catch (error) {
//     const responseTime = Date.now() - startTime;
    
//     // Log the error
//     sendLogToLogflare({
//       message: `Error in middleware: ${error instanceof Error ? error.message : String(error)}`,
//       metadata: {
//         error: {
//           message: error instanceof Error ? error.message : String(error),
//           stack: error instanceof Error ? error.stack : undefined,
//           timestamp: new Date().toISOString()
//         },
//         request: {
//           method: request.method,
//           url: request.url,
//           path: pathName,
//           headers: Object.fromEntries(request.headers)
//         },
//         performance: {
//           responseTime: responseTime
//         }
//       }
//     });
    
//     console.log('error', error);
//     return NextResponse.redirect(new URL("/sign-in", request.url));
//   }
// }

// // Helper function to log response information
// async function logResponse(
//   response: NextResponse, 
//   message: string, 
//   responseTime: number, 
//   request: NextRequest, 
//   path: string,
//   session?: any
// ) {
//   const clonedRequest = request.clone();
//   let requestBody = undefined
//   try {
//     // Only attempt to read body for POST/PUT/PATCH requests
//     if (['POST', 'PUT', 'PATCH'].includes(request.method)) {
//       requestBody = await clonedRequest.text();
//     }
//   } catch {
//     // If we can't read the body, just log that fact
//     requestBody = 'Could not read request body';
//   }

//   sendLogToLogflare({
//     message: message,
//     metadata: {
//       response: {
//         status: response.status,
//         statusText: response.statusText,
//         headers: Object.fromEntries(response.headers),
//         responseTime: responseTime
//       },
//       request: {
//         method: request.method,
//         url: request.url,
//         path: path,
//         headers: Object.fromEntries(request.headers),
//         body: requestBody
//       },
//       user: session ? {
//         id: session.user.id,
//         email: session.user.email,
//         role: session.user.role
//       } : 'unauthenticated',
//       sessionText: session ? JSON.stringify(session) : 'not found', // for debugging purposes, will be removed in production later
//       timestamp: new Date().toISOString()
//     }
//   });
// }

// // Avoid running /favicon.ico?favicon.45db1c09.ico on middleware

// export const config = {
//   matcher: [
//     // Excluir rutas específicas
//     // '/((?!api|_next/static|_next/image|favicon.ico|.*\\.png$).*)'
//     // '/((?!api|_next/static|_next/image|favicon.ico|.*\\.png$|public|.*\\.svg$|.*\\.ico$).*)' 
//     '/((?!api|_next/static|_next/image|\\.png$|public|.*\\.svg$|.*\\.ico$).*)'
//   ], // match all routes except api, next static, next image, favicon.ico, png files, svg files, ico files, and whatever other image files
// };
