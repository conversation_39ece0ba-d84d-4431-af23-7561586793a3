
import React from 'react'
import VehicleDetailClient from './vehicle-detail-client'
import { UserProvider } from '@/context/user-context'
import { getServerSession } from '@/actions/getSession';
import { getVehicleById } from '@/lib/api/vehicles.api';

export async function generateMetadata({ params }: { params: Promise<{ id: string }> }) {
  const params2 = await params;
  try {
    const vehicle = await getVehicleById(params2.id);
    return {
      title: `Autoop - ${vehicle.make} ${vehicle.model}`,
      description: `Detalles del vehículo ${vehicle.make} ${vehicle.model}`,
    }
  } catch (error: any) {
    console.log('error', error);
    return {
      title: `Autoop - No encontrado`,
      description: `No se pudo cargar la información del vehículo.`,
    }
  }
}


export default async function VehicleDetailPage({ params: paramsProp }: { params: Promise<{ id: string }> }) {
  const params = await paramsProp;
  const session = await getServerSession(undefined, { shouldRedirect: false })

  const vehicle = await getVehicleById(params.id);

  return (
    <>
      <UserProvider
        session={session}
      >
        <VehicleDetailClient params={params} vehicle={vehicle} />
      </UserProvider>
    </>
  )

}
