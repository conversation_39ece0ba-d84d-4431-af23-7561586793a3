'use client';

import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from "@tanstack/react-table";
// import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Eye, Check, X, MoreHorizontal } from 'lucide-react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import Image from "next/image";
import { UserVerification } from '@/lib/api/user-verification.api';


interface VerificationDataTableProps {
  data: UserVerification[];
  onApprove: (userId: string) => void;
  onReject: (verification: UserVerification) => void;
  onViewDocument: (url: string, title: string) => void;
}

export function VerificationDataTable({
  data,
  onApprove,
  onReject,
  onViewDocument
}: VerificationDataTableProps) {
  // Formatear fecha
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'dd MMM yyyy, HH:mm', { locale: es });
  };


  // Definir columnas
  const columns: ColumnDef<UserVerification>[] = [
    {
      accessorKey: 'user',
      header: 'Usuario',
      cell: ({ row }) => {
        const verification = row.original;
        const userImage = verification.user.image || "/placeholder.jpg";

        return (
          <div className="flex items-center space-x-3">
            <div className="relative h-10 w-10 rounded-full overflow-hidden">
              <Image
                src={userImage}
                alt={verification.user.name}
                width={40}
                height={40}
                className="object-cover"
                onError={(e) => {
                  (e.target as HTMLImageElement).src = '/placeholder.svg';
                }}
              />
            </div>
            <div>
              <div className="font-medium">{verification.user.name}</div>
              <div className="text-sm text-muted-foreground">ID: {verification.userId.slice(0, 8)}</div>
            </div>
          </div>
        );
      },
    },
    // Columna para mostrar tipo de usuario
    {
      accessorKey: 'user.userType',
      header: 'Tipo de usuario',
      cell: ({ row }) => {

        const userTypeMap: Record<string, string> = {
          client: 'Cliente',
          host: 'Anfitrión',
        };
        const user = row.original.user;

        return (
          <>
            {userTypeMap[user.userType] || 'Desconocido'}
          </>
        )
      },
    },
    {
      accessorKey: 'user.email',
      header: 'Correo',
      cell: ({ row }) => row.original.user.email,
    },
    {
      accessorKey: 'createdAt',
      header: 'Fecha de solicitud',
      cell: ({ row }) => formatDate(row.original.createdAt),
    },
    {
      id: 'actions',
      cell: ({ row }) => {
        const verification = row.original;
        return (
          <div className="text-right">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Abrir menú</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {verification.idFront && (
                  <DropdownMenuItem onClick={() => onViewDocument(verification.idFront!, 'ID Frontal')}>
                    <Eye className="mr-2 h-4 w-4" />
                    Ver ID Frontal
                  </DropdownMenuItem>
                )}
                {verification.idBack && (
                  <DropdownMenuItem onClick={() => onViewDocument(verification.idBack!, 'ID Posterior')}>
                    <Eye className="mr-2 h-4 w-4" />
                    Ver ID Posterior
                  </DropdownMenuItem>
                )}
                {verification.driverLicense && (
                  <DropdownMenuItem onClick={() => onViewDocument(verification.driverLicense!, 'Licencia de Conducir')}>
                    <Eye className="mr-2 h-4 w-4" />
                    Ver Licencia
                  </DropdownMenuItem>
                )}
                {verification.addressProof && (
                  <DropdownMenuItem onClick={() => onViewDocument(verification.addressProof!, 'Comprobante de Domicilio')}>
                    <Eye className="mr-2 h-4 w-4" />
                    Ver Comprobante
                  </DropdownMenuItem>
                )}
                {verification.selfieWithId && (
                  <DropdownMenuItem onClick={() => onViewDocument(verification.selfieWithId!, 'Selfie con ID')}>
                    <Eye className="mr-2 h-4 w-4" />
                    Ver Selfie
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem
                  className="text-green-600"
                  onClick={() => onApprove(verification.userId)}
                >
                  <Check className="mr-2 h-4 w-4" />
                  Aprobar
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="text-red-600"
                  onClick={() => onReject(verification)}
                >
                  <X className="mr-2 h-4 w-4" />
                  Rechazar
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
    },
  ];

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    initialState: {
      pagination: {
        pageSize: 10,
      },
    },
  });

  return (
    <div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No hay verificaciones pendientes
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredRowModel().rows.length} verificación(es) en total
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Anterior
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Siguiente
          </Button>
        </div>
      </div>
    </div>
  );
}
