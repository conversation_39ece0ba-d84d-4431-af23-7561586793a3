"use client"

import React from "react"

import type { ColumnDef } from "@tanstack/react-table"
import { ArrowUpDown, MoreHorizontal } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

// Función para crear columnas con acciones comunes
export function createColumns<T>(
  columns: ColumnDef<T>[],
  actions?: {
    onView?: (data: T) => void
    onEdit?: (data: T) => void
    onDelete?: (data: T) => void
    extraActions?: (data: T) => React.ReactNode[]
  },
): ColumnDef<T>[] {
  const baseColumns: ColumnDef<T>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    ...columns,
  ]

  // Si hay acciones, añadir columna de acciones
  if (actions) {
    baseColumns.push({
      id: "actions",
      cell: ({ row }) => {
        const data = row.original

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Abrir menú</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Acciones</DropdownMenuLabel>
              {actions.onView && (
                <DropdownMenuItem onClick={() => actions.onView?.(data)}>Ver detalles</DropdownMenuItem>
              )}
              {actions.onEdit && <DropdownMenuItem onClick={() => actions.onEdit?.(data)}>Editar</DropdownMenuItem>}
              {actions.extraActions && (
                <>
                  <DropdownMenuSeparator />
                  {actions.extraActions(data).map((action, index) => (
                    <React.Fragment key={index}>{action}</React.Fragment>
                  ))}
                </>
              )}
              {actions.onDelete && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={() => actions.onDelete?.(data)}
                    className="text-red-600 focus:text-red-600"
                  >
                    Eliminar
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
    })
  }

  return baseColumns
}

// Función para crear un encabezado de columna ordenable
export function createSortableHeader(label: string) {
  const SortableHeader = ({ column }: { column: any }) => {
    return (
      <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
        {label}
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    )
  }
  SortableHeader.displayName = `SortableHeader(${label})`
  return SortableHeader
}

