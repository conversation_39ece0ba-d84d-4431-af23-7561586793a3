import { Card, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { MapPin } from "lucide-react"

export function ClientMapView() {
  return (
    <Card className="h-fit">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MapPin className="h-5 w-5" />
          Mapa
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="bg-gray-100 rounded-lg h-96 flex items-center justify-center">
          <div className="text-center text-muted-foreground">
            <MapPin className="h-12 w-12 mx-auto mb-2" />
            <p>Vista del mapa</p>
            <p className="text-sm">Integración con Google Maps</p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
