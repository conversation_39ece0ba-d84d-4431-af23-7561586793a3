import { getServerSession } from '@/actions/getSession';
import Link from 'next/link';

export default async function GoDashboard() {

  const session = await getServerSession(undefined, { shouldRedirect: false });

  if (session) {
    return (
      <Link href="/dashboard" className="bg-[#1a2b5e] text-white px-4 py-2 rounded-md hover:bg-[#152348] transition">
        Dashboard
      </Link>
    )
  }

  return (
    <Link href="/sign-in" className="bg-[#1a2b5e] text-white px-4 py-2 rounded-md hover:bg-[#152348] transition">
      Iniciar <PERSON><PERSON><PERSON>
    </Link>
  )
}
