'use client'

import { useState, use<PERSON>emo, useEffect } from "react"
import { useParams } from "next/navigation"
import { useQuery, useQueryClient } from "@tanstack/react-query"
import { vehiclesApi } from "@/lib/api/vehicles.api"
import { DateRange } from 'react-date-range'
import { format } from "date-fns"
import { es } from "date-fns/locale"
import toast from 'react-hot-toast'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { AlertTriangle } from "lucide-react"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import VehicleAvailabilitySettings from "./availability-settings"
import "react-date-range/dist/styles.css"
import "react-date-range/dist/theme/default.css"
import { DateTime } from 'luxon';
import { normalizeDates, isDateDisabled } from "@/lib/utils"

export default function VehicleCalendarPage() {
  const { id } = useParams()
  const queryClient = useQueryClient()
  const [selectedDates, setSelectedDates] = useState<Date[]>([])
  const [blockReason, setBlockReason] = useState("")
  const [isBlockDialogOpen, setIsBlockDialogOpen] = useState(false)
  const [activeTab, setActiveTab] = useState("calendar")

  // Consultar la información del vehículo
  const { data: vehicle, isLoading } = useQuery({
    queryKey: ['vehicle', id],
    queryFn: () => vehiclesApi.getById(id as string),
    enabled: !!id
  })

  // Obtener fechas no disponibles
  const { data: unavailableDatesData } = useQuery({
    queryKey: ['vehicle-unavailable-dates', id],
    queryFn: () => vehiclesApi.getUnavailableDates(id as string),
    enabled: !!id
  })

  if (unavailableDatesData) {
    console.log('unavailableDatesData', unavailableDatesData.map(date => date))
  }

  // Convertir las fechas no disponibles a objetos Date
  const unavailableDates = useMemo(() => {
    if (!unavailableDatesData) return [];
    return normalizeDates(unavailableDatesData);
  }, [unavailableDatesData]);
  console.log('---------------------------------------------------------')
  console.log('---------------------------------------------------------')
  console.log('---------------------------------------------------------')
  console.log('unavailableDates', unavailableDates.map(date => date.toISOString()))

  // Función para manejar la selección de fechas en el calendario
  const handleDateSelect = (item: any) => {
    if (item && item.selection) {
      const { startDate, endDate } = item.selection;

      // Crear un array con todas las fechas entre startDate y endDate
      const dates: Date[] = [];
      const currentDate = new Date(startDate);

      while (currentDate <= endDate) {
        dates.push(new Date(currentDate));
        currentDate.setDate(currentDate.getDate() + 1);
      }

      setSelectedDates(dates);
    }
  };

  const [direction, setDirection] = useState<"horizontal" | "vertical">("horizontal")

  useEffect(() => {
    const handleResize = () => {
      setDirection(window.innerWidth < 1050 ? "vertical" : "horizontal")
    }

    handleResize()
    window.addEventListener("resize", handleResize)
    return () => window.removeEventListener("resize", handleResize)
  }, [])

  // Función para bloquear fechas
  const handleBlockDates = () => {
    if (selectedDates.length === 0) {
      toast.error("Debes seleccionar al menos una fecha para bloquear.");
      return;
    }

    // Obtener la primera y última fecha del rango usando Luxon
    // Convertir a formato ISO sin información de zona horaria
    const startDate = DateTime.fromJSDate(selectedDates[0]).toISODate();
    const endDate = DateTime.fromJSDate(selectedDates[selectedDates.length - 1]).toISODate();

    // Verificar que las fechas no sean null
    if (!startDate || !endDate) {
      toast.error("Error al procesar las fechas seleccionadas.");
      return;
    }

    const requestData = {
      startDate,
      endDate,
      reason: blockReason || "No disponible"
    };

    console.log("Enviando solicitud para bloquear fechas:", {
      vehicleId: id,
      ...requestData
    });

    // Mostrar estado de carga
    toast.loading("Bloqueando fechas...", { id: "blocking-dates" });

    // Usar vehiclesApi.host.blockDates
    vehiclesApi.host.blockDates(id as string, requestData)
      .then((data) => {
        console.log("Fechas bloqueadas exitosamente:", data);
        // Invalidar la consulta para actualizar los datos
        queryClient.invalidateQueries({ queryKey: ['vehicle-unavailable-dates', id] });
        setSelectedDates([]);
        setBlockReason("");
        setIsBlockDialogOpen(false);
        toast.dismiss("blocking-dates");
        toast.success("Fechas bloqueadas exitosamente.");
      })
      .catch(error => {
        console.error("Error al bloquear fechas:", error);
        toast.dismiss("blocking-dates");
        toast.error(`No se pudieron bloquear las fechas: ${error.message || error}`);
      });
  };

  // Función para determinar si una fecha está deshabilitada
  const isDateDisabledHandler = (date: Date) => {
    return isDateDisabled(date, unavailableDates);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-6 flex flex-col items-center justify-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        <p className="mt-4 text-muted-foreground">Cargando calendario...</p>
      </div>
    )
  }



  return (
    <div className="container ">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Calendario de {vehicle?.make} {vehicle?.model}</h1>
      </div>

      <Tabs defaultValue="calendar" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="calendar">Calendario</TabsTrigger>
          <TabsTrigger value="settings">Configuración</TabsTrigger>
        </TabsList>

        <TabsContent value="calendar" className="space-y-4">
          <Card className='w-[max-content] s:w-full' >
            <CardHeader>
              <CardTitle>Calendario de Disponibilidad</CardTitle>
              <CardDescription>
                Visualiza y gestiona las fechas disponibles para la renta de tu vehículo.
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0 sm:p-4 flex justify-center sm:justify-start ">
              <DateRange
                onChange={handleDateSelect}
                moveRangeOnFirstSelection={false}
                ranges={[{
                  startDate: selectedDates[0] || new Date(),
                  endDate: selectedDates[selectedDates.length - 1] || new Date(),
                  key: 'selection'
                }]}
                // set unavailableDates as disabled dates
                months={2}
                direction={direction}
                locale={es}
                disabledDay={isDateDisabledHandler}
                minDate={new Date()}
                rangeColors={["#5569ff"]}
                className="border rounded-md"
                showDateDisplay={true}
              />

            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={() => setIsBlockDialogOpen(true)}>
                <AlertTriangle className="mr-2 h-4 w-4" />
                Bloquear Fechas
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <VehicleAvailabilitySettings vehicleId={id as string} />
        </TabsContent>
      </Tabs>

      {/* Diálogo para bloquear fechas */}
      <Dialog open={isBlockDialogOpen} onOpenChange={setIsBlockDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Bloquear Fechas</DialogTitle>
            <DialogDescription>
              Bloquea fechas para que no estén disponibles para reservas.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="blockReason">Motivo del bloqueo</Label>
              <Textarea
                id="blockReason"
                placeholder="Ej: Mantenimiento programado"
                value={blockReason}
                onChange={(e) => setBlockReason(e.target.value)}
              />
            </div>
            <div>
              <Label>Fechas seleccionadas</Label>
              <div className="flex flex-wrap gap-2 mt-2">
                {selectedDates.length > 0 ? (
                  selectedDates.map((date, index) => (
                    <div key={index} className="bg-blue-100 text-blue-800 px-2 py-1 rounded-md text-xs">
                      {format(date, "dd/MM/yyyy")}
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-muted-foreground">No hay fechas seleccionadas</p>
                )}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsBlockDialogOpen(false)}>Cancelar</Button>
            <Button onClick={handleBlockDates}>Bloquear</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

