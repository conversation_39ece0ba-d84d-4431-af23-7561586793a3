import { t } from 'elysia';

// Esquema para creación de vehículos
export const createVehicleDto = t.Object({
  make: t.String(),
  model: t.String(),
  year: t.Number(),
  color: t.String(),
  vin: t.String(),
  plate: t.String(),
  state_code: t.String(),
  country_code: t.String(),
  price: t.Number(),
  description: t.String(),
  engineSize: t.Number(),
  transmission: t.String(),
  trim: t.String(),
  bodyType: t.String(),
  features: t.Object({
    fuelType: t.String(),
    seats: t.Number(),
    mileage: t.Number(),
    registrationNumber: t.String(),
    insurancePolicy: t.String(),
    rules: t.String(),
    location: t.String(),
    weeklyRate: t.Optional(t.Number()),
    monthlyRate: t.Optional(t.Number()),
  }),
  amenities: t.Array(t.String()),
})

// Esquema para subida de archivos
export const uploadFilesDto = t.Object({
  images: t.Files({ minItems: 3, maxItems: 10 }),
  plateDocument: t.Files({ maxItems: 1 }),
  vinDocument: t.Files({ maxItems: 1 }),
  registrationDocument: t.Files({ maxItems: 1 }),
  insurancePolicyDocument: t.Files({ maxItems: 1 }),
})

// Esquema para actualización de archivos
export const updateFilesDto = t.Object({
  images: t.Optional(t.Files({ minItems: 3, maxItems: 10 })),
  plateDocument: t.Optional(t.Files({ maxItems: 1 })),
  vinDocument: t.Optional(t.Files({ maxItems: 1 })),
  registrationDocument: t.Optional(t.Files({ maxItems: 1 })),
  insurancePolicyDocument: t.Optional(t.Files({ maxItems: 1 })),
  imagesToRemove: t.Optional(t.String()),
})

// Esquema para validación de vehículos
export const vehicleSchema = t.Object({
  make: t.String(),
  model: t.String(),
  year: t.Number(),
  color: t.String(),
  vin: t.String(),
  plate: t.String(),
  state_code: t.String(),
  country_code: t.String(),
  price: t.Number(),
  description: t.String(),

  // Nuevos campos estructurados
  engineSize: t.Number(),
  transmission: t.String(),
  trim: t.String(),
  bodyType: t.String(),

  // Campos existentes
  features: t.Any(),
  amenities: t.Array(t.String()),
  images: t.Optional(t.Array(t.String())),
  status: t.Optional(t.String())
});

// Esquema para actualización de estado
export const statusSchema = t.Object({
  status: t.String()
});

// Esquema para rechazo de vehículo
export const rejectSchema = t.Object({
  reason: t.String()
});

// Esquema para actualización de disponibilidad
export const availabilityUpdateSchema = t.Object({
  defaultCheckInTime: t.Optional(t.String()),
  defaultCheckOutTime: t.Optional(t.String()),
  minimumRentalNights: t.Optional(t.Number()),
  maximumRentalNights: t.Optional(t.Number()),
  advanceBookingPeriod: t.Optional(t.Number()),
  instantBooking: t.Optional(t.Boolean()),
  allowSameDayBooking: t.Optional(t.Boolean()),
  cancellationPolicy: t.Optional(t.String()),
  customAvailableDays: t.Optional(t.Boolean()),
  mondayAvailable: t.Optional(t.Boolean()),
  tuesdayAvailable: t.Optional(t.Boolean()),
  wednesdayAvailable: t.Optional(t.Boolean()),
  thursdayAvailable: t.Optional(t.Boolean()),
  fridayAvailable: t.Optional(t.Boolean()),
  saturdayAvailable: t.Optional(t.Boolean()),
  sundayAvailable: t.Optional(t.Boolean())
});
