import { parseFilename } from '@/lib/filename-parser';
import { S3Client } from "bun";
import sharp from "sharp";

export const r2Public = new S3Client({
  accessKeyId: process.env.R2_ACCESS_KEY_ID!,
  secretAccessKey: process.env.R2_SECRET_ACCESS_KEY!,
  bucket: process.env.R2_BUCKET!,
  endpoint: `https://${process.env.CF_ACCOUNT_ID}.r2.cloudflarestorage.com`,
})

export const r2Private = new S3Client({
  accessKeyId: process.env.R2_ACCESS_KEY_ID!,
  secretAccessKey: process.env.R2_SECRET_ACCESS_KEY!,
  bucket: process.env.PRIVATE_R2_BUCKET! || process.env.R2_BUCKET! + "-private",
  endpoint: `https://${process.env.CF_ACCOUNT_ID}.r2.cloudflarestorage.com`,
})


/**
 * Comprime una imagen a formato AVIF para reducir su tamaño
 * @param file El archivo de imagen a comprimir
 * @returns Buffer de la imagen comprimida
 */
async function compressImageToAvif(file: File): Promise<Buffer> {
  const arrayBuffer = await file.arrayBuffer();
  return sharp(Buffer.from(arrayBuffer))
    // .avif()
    .webp({ quality: 50 })
    .toBuffer();
}

/**
 * Verifica si un archivo es una imagen basado en su tipo MIME
 * @param file El archivo a verificar
 * @returns true si es una imagen, false en caso contrario
 */
function isImageFile(file: File): boolean {
  return file.type.startsWith('image/');
}


/** 
 * Upload a file to R2
 * @param file The file to upload
 * @param path The path to upload the file to
 * @param isPublic Whether the file should be public or private
 * @returns The URL of the file
 */
export async function uploadFileToR2(
  {
    file,
    path,
    isPublic = false,
  }
    : {
      file: File,
      path: string,
      isPublic?: boolean,
    }
) {

  // Parse the filename to avoid duplicates
  path = path.replace(file.name, parseFilename(file.name));


  // Si es una imagen convertir a webp
  let fileData: Buffer | ArrayBuffer;
  if (isImageFile(file)) {
    fileData = await compressImageToAvif(file);
    // Cambiar la extensión a .webp
    path = path.replace(/\.[^.]+$/, '.webp');
  } else {
    fileData = await file.arrayBuffer();
  }

  const fileRef = isPublic
    ? r2Public.file(path)
    : r2Private.file(path);

  await Bun.write(fileRef, fileData);

  console.log('File uploaded to R2:', path);

  const url = isPublic ? `https://pedromtz8.com/${path}` : path; // If public return public bucket if not, return the key
  return url;

}

/** 
 * Get a file url from R2
 */

export async function getFileUrlFromR2(
  {
    path,
    isPublic = false,
  }
    : {
      path: string,
      isPublic?: boolean,
    }
) {
  const fileRef = isPublic
    ? r2Public.file(path)
    : r2Private.file(path);
  const url = fileRef.presign({ acl: "public-read", expiresIn: 3600 }); // 1 hora
  return url;
}

/**
 * Get file reference from R2
 * @param path The path to the file
 * @param isPublic Whether the file is public or private
 * @returns The file reference
 */
export function getFileReferenceFromR2(
  {
    path,
    isPublic = false,
  }
    : {
      path: string,
      isPublic?: boolean,
    }
) {
  const fileRef = isPublic
    ? r2Public.file(path)
    : r2Private.file(path);
  return fileRef;
}

/** 
 * Delete a file from R2
 * @param path The path to the file
 * @param isPublic Whether the file is public or private
 */

export async function deleteFileFromR2(
  {
    path,
    isPublic = false,
  }
    : {
      path: string,
      isPublic?: boolean,
    }
) {
  const fileRef = isPublic
    ? r2Public.file(path)
    : r2Private.file(path);
  await fileRef.delete();
}

/**
 * Procesa y sube múltiples archivos a R2
 * @param files Array de archivos a subir
 * @param basePath Ruta base para los archivos
 * @param isPublic Si los archivos deben ser públicos o privados
 * @returns Array de URLs de los archivos subidos
 */
export async function uploadMultipleFilesToR2({
  files,
  basePath,
  isPublic = false,
}: {
  files: File[];
  basePath: string;
  isPublic?: boolean;
}): Promise<string[]> {
  if (!files || files.length === 0) return [];

  const uploadPromises = files.map(async (file) => {
    const path = `${basePath}/${parseFilename(file.name)}`;
    return uploadFileToR2({
      file,
      path,
      isPublic,
    });
  });

  return Promise.all(uploadPromises);
}

/**
 * Procesa y sube archivos de verificación de host a R2
 * @param userId ID del usuario
 * @param files Objeto con los diferentes tipos de archivos de verificación
 * @returns Objeto con las URLs de los archivos subidos
 */
export async function uploadUserVerificationFilesToR2({
  userId,
  files,
}: {
  userId: string;
  files: {
    idFront?: File[];
    idBack?: File[];
    driverLicense?: File[];
    addressProof?: File[];
    selfieWithId?: File[];
  };
}): Promise<{
  idFront?: string;
  idBack?: string;
  driverLicense?: string;
  addressProof?: string;
  selfieWithId?: string;
}> {

  const result: any = {};

  // Subir cada tipo de documento de verificación (privados)
  if (files.idFront && files.idFront.length > 0) {
    const urls = await uploadMultipleFilesToR2({
      files: files.idFront,
      basePath: `users/${userId}/verification/idFront`,
      isPublic: false,
    });
    result.idFront = urls[0];
  }

  if (files.idBack && files.idBack.length > 0) {
    const urls = await uploadMultipleFilesToR2({
      files: files.idBack,
      basePath: `users/${userId}/verification/idBack`,
      isPublic: false,
    });
    // console.log('idBack uploaded, URLs:', urls);
    result.idBack = urls[0];
    // console.log('Set result.idBack to:', result.idBack);
  }

  if (files.driverLicense && files.driverLicense.length > 0) {
    // console.log('Uploading driverLicense files:', files.driverLicense.length);
    const urls = await uploadMultipleFilesToR2({
      files: files.driverLicense,
      basePath: `users/${userId}/verification/driverLicense`,
      isPublic: false,
    });
    // console.log('driverLicense uploaded, URLs:', urls);
    result.driverLicense = urls[0];
    // console.log('Set result.driverLicense to:', result.driverLicense);
  }

  if (files.addressProof && files.addressProof.length > 0) {
    // console.log('Uploading addressProof files:', files.addressProof.length);
    const urls = await uploadMultipleFilesToR2({
      files: files.addressProof,
      basePath: `users/${userId}/verification/addressProof`,
      isPublic: false,
    });
    // console.log('addressProof uploaded, URLs:', urls);
    result.addressProof = urls[0];
    // console.log('Set result.addressProof to:', result.addressProof);
  }

  if (files.selfieWithId && files.selfieWithId.length > 0) {
    // console.log('Uploading selfieWithId files:', files.selfieWithId.length);
    const urls = await uploadMultipleFilesToR2({
      files: files.selfieWithId,
      basePath: `users/${userId}/verification/selfieWithId`,
      isPublic: false,
    });
    // console.log('selfieWithId uploaded, URLs:', urls);
    result.selfieWithId = urls[0];
    // console.log('Set result.selfieWithId to:', result.selfieWithId);
  }

  return result;
}

/**
 * Procesa y sube archivos de vehículo a R2
 * @param vehicleId ID del vehículo
 * @param files Objeto con los diferentes tipos de archivos
 * @returns Objeto con las URLs de los archivos subidos
 */
export async function uploadVehicleFilesToR2({
  vehicleId,
  files,
  userId,
}: {
  vehicleId: string;
  files: {
    images?: File[];
    plateDocument?: File[];
    vinDocument?: File[];
    registrationDocument?: File[];
    insurancePolicyDocument?: File[];
  };
  userId: string;
}): Promise<{
  imageUrls?: string[];
  plateDocumentUrl?: string;
  vinDocumentUrl?: string;
  registrationDocumentUrl?: string;
  insurancePolicyDocumentUrl?: string;
}> {
  const result: any = {};

  // Subir imágenes (públicas)
  if (files.images && files.images.length > 0) {
    result.imageUrls = await uploadMultipleFilesToR2({
      files: files.images,
      basePath: `vehicles/${vehicleId}/images`,
      isPublic: true,
    });
  }

  // Subir documentos (privados)
  if (files.plateDocument && files.plateDocument.length > 0) {
    const urls = await uploadMultipleFilesToR2({
      files: files.plateDocument,
      // basePath: `vehicles/${vehicleId}/documents/plate`,
      basePath: `users/${userId}/vehicles/${vehicleId}/documents/plate`,
      isPublic: false,
    });
    result.plateDocumentUrl = urls[0];
  }

  if (files.vinDocument && files.vinDocument.length > 0) {
    const urls = await uploadMultipleFilesToR2({
      files: files.vinDocument,
      // basePath: `vehicles/${vehicleId}/documents/vin`,
      basePath: `users/${userId}/vehicles/${vehicleId}/documents/vin`,
      isPublic: false,
    });
    result.vinDocumentUrl = urls[0];
  }

  if (files.registrationDocument && files.registrationDocument.length > 0) {
    const urls = await uploadMultipleFilesToR2({
      files: files.registrationDocument,
      // basePath: `vehicles/${vehicleId}/documents/registration`,
      basePath: `users/${userId}/vehicles/${vehicleId}/documents/registration`,
      isPublic: false,
    });
    result.registrationDocumentUrl = urls[0];
  }

  if (files.insurancePolicyDocument && files.insurancePolicyDocument.length > 0) {
    const urls = await uploadMultipleFilesToR2({
      files: files.insurancePolicyDocument,
      // basePath: `vehicles/${vehicleId}/documents/insurance`,
      basePath: `users/${userId}/vehicles/${vehicleId}/documents/insurance`,
      isPublic: false,
    });
    result.insurancePolicyDocumentUrl = urls[0];
  }

  return result;
}
