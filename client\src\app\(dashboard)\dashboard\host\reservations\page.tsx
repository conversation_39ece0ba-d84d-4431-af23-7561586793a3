"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { HostReservationStats } from "@/components/host/host-reservation-stats"
import { HostReservationFilters } from "@/components/host/host-reservation-filters"
import { HostReservationTable } from "@/components/host/host-reservation-table"
import { HostPersonalReservations } from "@/components/host/host-personal-reservations"

export default function HostReservationsPage() {
  const [activeTab, setActiveTab] = useState("clients")

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-2">
        <h1 className="text-2xl font-bold">Reservas</h1>
        <p className="text-muted-foreground">Gestiona todas las reservas de tus vehículos.</p>
      </div>

      <Tabs defaultValue={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2 mb-6">
          <TabsTrigger value="clients">Reservas de Clientes</TabsTrigger>
          <TabsTrigger value="personal">Mis Reservas Personales</TabsTrigger>
        </TabsList>

        <TabsContent value="clients" className="space-y-6">
          <HostReservationStats />
          <HostReservationFilters />
          <HostReservationTable />
        </TabsContent>

        <TabsContent value="personal">
          <HostPersonalReservations />
        </TabsContent>
      </Tabs>
    </div>
  )
}
