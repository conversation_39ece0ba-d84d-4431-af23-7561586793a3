"use client"

import { <PERSON>, MoreHorizontal, Download, <PERSON>fresh<PERSON><PERSON>, AlertCircle } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

// Mock data
const payouts = [
  {
    id: "TRX-78901",
    host: {
      name: "<PERSON>",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "J<PERSON>",
      email: "<EMAIL>",
    },
    amount: "$1,200.00",
    fee: "$120.00",
    net: "$1,080.00",
    date: "May 15, 2025",
    status: "Completed",
    method: "Bank Transfer",
    reference: "REF-12345",
  },
  {
    id: "TRX-78902",
    host: {
      name: "<PERSON>",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "MB",
      email: "<EMAIL>",
    },
    amount: "$850.00",
    fee: "$85.00",
    net: "$765.00",
    date: "May 15, 2025",
    status: "Pending",
    method: "PayPal",
    reference: "REF-12346",
  },
  {
    id: "TRX-78903",
    host: {
      name: "Robert Chen",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "RC",
      email: "<EMAIL>",
    },
    amount: "$650.00",
    fee: "$65.00",
    net: "$585.00",
    date: "May 14, 2025",
    status: "Processing",
    method: "Bank Transfer",
    reference: "REF-12347",
  },
  {
    id: "TRX-78904",
    host: {
      name: "Sophia Martinez",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "SM",
      email: "<EMAIL>",
    },
    amount: "$1,500.00",
    fee: "$150.00",
    net: "$1,350.00",
    date: "May 13, 2025",
    status: "Failed",
    method: "Bank Transfer",
    reference: "REF-12348",
  },
]

export function PayoutTable() {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Transaction ID</TableHead>
          <TableHead>Host</TableHead>
          <TableHead>Amount</TableHead>
          <TableHead>Fee</TableHead>
          <TableHead>Net Amount</TableHead>
          <TableHead>Date</TableHead>
          <TableHead>Status</TableHead>
          <TableHead>Method</TableHead>
          <TableHead className="text-right">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {payouts.map((payout) => (
          <TableRow key={payout.id}>
            <TableCell className="font-medium">{payout.id}</TableCell>
            <TableCell>
              <div className="flex items-center gap-2">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={payout.host.avatar || "/placeholder.svg"} alt={payout.host.name} />
                  <AvatarFallback>{payout.host.initials}</AvatarFallback>
                </Avatar>
                <div>
                  <div className="text-sm font-medium">{payout.host.name}</div>
                  <div className="text-xs text-muted-foreground">{payout.host.email}</div>
                </div>
              </div>
            </TableCell>
            <TableCell>{payout.amount}</TableCell>
            <TableCell className="text-muted-foreground">{payout.fee}</TableCell>
            <TableCell className="font-medium">{payout.net}</TableCell>
            <TableCell>{payout.date}</TableCell>
            <TableCell>
              <Badge
                variant={
                  payout.status === "Completed"
                    ? "outline"
                    : payout.status === "Pending"
                      ? "secondary"
                      : payout.status === "Processing"
                        ? "default"
                        : "destructive"
                }
                className={
                  payout.status === "Completed"
                    ? "bg-green-100 text-green-800 hover:bg-green-100"
                    : payout.status === "Pending"
                      ? "bg-yellow-100 text-yellow-800 hover:bg-yellow-100"
                      : payout.status === "Processing"
                        ? "bg-blue-100 text-blue-800 hover:bg-blue-100"
                        : "bg-red-100 text-red-800 hover:bg-red-100"
                }
              >
                {payout.status}
              </Badge>
            </TableCell>
            <TableCell>{payout.method}</TableCell>
            <TableCell className="text-right">
              <div className="flex justify-end gap-2">
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <Eye className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <Download className="h-4 w-4" />
                </Button>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem>
                      <Eye className="mr-2 h-4 w-4" />
                      <span>View Details</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Download className="mr-2 h-4 w-4" />
                      <span>Download Receipt</span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    {payout.status === "Failed" && (
                      <DropdownMenuItem className="text-blue-600">
                        <RefreshCw className="mr-2 h-4 w-4" />
                        <span>Retry Payout</span>
                      </DropdownMenuItem>
                    )}
                    {payout.status === "Pending" && (
                      <DropdownMenuItem className="text-green-600">
                        <RefreshCw className="mr-2 h-4 w-4" />
                        <span>Process Now</span>
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuItem className="text-red-600">
                      <AlertCircle className="mr-2 h-4 w-4" />
                      <span>Report Issue</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}
