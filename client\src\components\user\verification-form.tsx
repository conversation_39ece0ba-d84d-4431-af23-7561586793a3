import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useMutation } from '@tanstack/react-query';
import { verificationApi } from '@/lib/api/user-verification.api';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Form } from '@/components/ui/form';
import toast from 'react-hot-toast';
import FileUploadInput from '../forms/file-upload-input';

// Esquema de validación
const verificationFormSchema = z.object({
  idFront: z.array(z.custom<File>())
    .min(1, "Por favor, sube una imagen del frente de tu identificación")
    .max(1, "Máximo 1 archivo")
    .refine((files) => files.every((file) => file.size <= 2 * 1024 * 1024), {
      message: "El archivo debe pesar menos de 2MB",
    }),
  idBack: z.array(z.custom<File>())
    .min(1, "Por favor, sube una imagen del reverso de tu identificación")
    .max(1, "Máximo 1 archivo")
    .refine((files) => files.every((file) => file.size <= 2 * 1024 * 1024), {
      message: "El archivo debe pesar menos de 2MB",
    }),
  driverLicense: z.array(z.custom<File>())
    .min(1, "Por favor, sube una imagen de tu licencia de conducir")
    .max(1, "Máximo 1 archivo")
    .refine((files) => files.every((file) => file.size <= 2 * 1024 * 1024), {
      message: "El archivo debe pesar menos de 2MB",
    }),
  addressProof: z.array(z.custom<File>())
    .min(1, "Por favor, sube un comprobante de domicilio")
    .max(1, "Máximo 1 archivo")
    .refine((files) => files.every((file) => file.size <= 2 * 1024 * 1024), {
      message: "El archivo debe pesar menos de 2MB",
    }),
  selfieWithId: z.array(z.custom<File>())
    .min(1, "Por favor, sube una selfie sosteniendo tu identificación")
    .max(1, "Máximo 1 archivo")
    .refine((files) => files.every((file) => file.size <= 2 * 1024 * 1024), {
      message: "El archivo debe pesar menos de 2MB",
    }),
});

type VerificationFormValues = z.infer<typeof verificationFormSchema>;


export function UserVerificationForm() {
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<VerificationFormValues>({
    resolver: zodResolver(verificationFormSchema),
    defaultValues: {
      idFront: [],
      idBack: [],
      driverLicense: [],
      addressProof: [],
      selfieWithId: [],
    }
  });

  const uploadMutation = useMutation({
    mutationFn: (data: FormData) => verificationApi.user.uploadDocuments(data),
    onSuccess: () => {
      toast.success("Documentos enviados para revisión\nTe notificaremos cuando sean aprobados.")
      setIsLoading(false);

      setTimeout(() => {
        window.location.reload();
      }, 1200);

    },
    onError: (error) => {
      console.error("Error al subir documentos:", error)

      toast.error("No se pudieron subir los documentos. Por favor, intenta de nuevo.")

      setIsLoading(false);
    }
  });

  const onSubmit = (data: VerificationFormValues) => {
    setIsLoading(true);

    const formData = new FormData();

    // Agregar archivos al FormData
    if (data.idFront[0]) formData.append('idFront', data.idFront[0]);
    if (data.idBack[0]) formData.append('idBack', data.idBack[0]);
    if (data.driverLicense[0]) formData.append('driverLicense', data.driverLicense[0]);
    if (data.addressProof[0]) formData.append('addressProof', data.addressProof[0]);
    if (data.selfieWithId[0]) formData.append('selfieWithId', data.selfieWithId[0]);

    uploadMutation.mutate(formData);
  };


  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle>Verificación de identidad</CardTitle>
        <CardDescription>
          Para garantizar la seguridad de nuestra plataforma, necesitamos verificar tu identidad antes de que puedas registrar vehículos.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FileUploadInput
                form={form}
                name="idFront"
                label="Identificación (Frente)"
                description="Sube una foto clara del frente de tu identificación oficial"
                maxSize={2 * 1024 * 1024}
                maxFiles={1}
                accept="image/*"
              />

              <FileUploadInput
                form={form}
                name="idBack"
                label="Identificación (Reverso)"
                description="Sube una foto clara del reverso de tu identificación oficial"
                maxSize={2 * 1024 * 1024}
                maxFiles={1}
                accept="image/*"
              />

              <FileUploadInput
                form={form}
                name="driverLicense"
                label="Licencia de conducir"
                description="Sube una foto clara de tu licencia de conducir vigente"
                maxSize={2 * 1024 * 1024}
                maxFiles={1}
                accept="image/*"
              />

              <FileUploadInput
                form={form}
                name="addressProof"
                label="Comprobante de domicilio"
                description="Sube un comprobante de domicilio reciente (no mayor a 3 meses)"
                maxSize={2 * 1024 * 1024}
                maxFiles={1}
                accept="image/*,application/pdf"
              />

              <div className="md:col-span-2">
                <FileUploadInput
                  form={form}
                  name="selfieWithId"
                  label="Selfie con identificación"
                  description="Tómate una foto sosteniendo tu identificación junto a tu rostro"
                  maxSize={2 * 1024 * 1024}
                  maxFiles={1}
                  accept="image/*"
                />
              </div>
            </div>

            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? "Enviando documentos..." : "Enviar documentos para verificación"}
            </Button>
          </form>
        </Form>
      </CardContent>
      <CardFooter className="flex flex-col text-sm text-muted-foreground">
        <p>Todos tus documentos son almacenados de forma segura y solo serán utilizados para el proceso de verificación.</p>
      </CardFooter>
    </Card>
  );
}