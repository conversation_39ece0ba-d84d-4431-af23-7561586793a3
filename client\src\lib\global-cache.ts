type CacheItem = {
    value: any;
    timestamp: number;
  };
  
  const GLOBAL_CACHE = new Map<string, CacheItem>();
  const CACHE_TTL = 1000 * 5; // 3 segundos de vida útil
  
  export const getGlobalCache = async <T>(key: string, fetcher: () => Promise<T>): Promise<T> => {
    const now = Date.now();
    const cached = GLOBAL_CACHE.get(key);
  
    if (cached && now - cached.timestamp < CACHE_TTL) {
      return cached.value;
    }
  
    const freshData = await fetcher();
    GLOBAL_CACHE.set(key, { value: freshData, timestamp: now });
    return freshData;
  };