import { NextRequest, NextResponse } from 'next/server';
import { apiService } from '@/services/api';

export const runtime = 'edge';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  try {
    // Resolve params promise
    const resolvedParams = await params;

    // Construir el path para apiService
    // apiService ya tiene /api/v1 como base, así que removemos esas partes del path
    let pathParts = [...resolvedParams.path];

    // Si el path empieza con 'v1', lo removemos porque apiService ya lo incluye
    if (pathParts[0] === 'v1') {
      pathParts = pathParts.slice(1);
    }

    // Limpiar y construir la URL correctamente
    const cleanPath = `/${pathParts.join('/')}`;
    const searchParams = new URLSearchParams(request.nextUrl.search);

    // Remover el parámetro 'path' si existe (parece ser agregado por error)
    searchParams.delete('path');

    const apiPath = cleanPath + (searchParams.toString() ? `?${searchParams.toString()}` : '');

    console.log('Original path parts:', resolvedParams.path);
    console.log('Original search:', request.nextUrl.search);
    console.log('Cleaned path for apiService:', apiPath);

    // Debug: Verificar si tenemos token antes de hacer la llamada
    const { getCookieServerByName } = await import('@/actions/cookies');
    const debugToken = await getCookieServerByName({ name: 'bearer_token' });
    console.log('Debug - Token before apiService call:', debugToken ? 'EXISTS' : 'NULL');
    console.log('Debug - Token length:', debugToken?.length || 0);

    // Usar apiService directamente con responseType arraybuffer para archivos
    const result = await apiService.get(apiPath, {
      responseType: 'arraybuffer'
    });

    if (!result.success) {
      console.error('API service error:', result.error);
      return new NextResponse(`Error fetching file: ${result.status} ${result.statusText}`, {
        status: result.status
      });
    }

    // Obtener el contenido y los headers de la respuesta
    const data = result.data as ArrayBuffer;
    const contentType = result.headers['content-type'] || 'application/octet-stream';
    const contentDisposition = result.headers['content-disposition'];
    const etag = result.headers['etag'];
    const lastModified = result.headers['last-modified'];
    const cacheControl = result.headers['cache-control'];

    console.log('Response content type:', contentType);
    console.log('Response size:', data.byteLength, 'bytes');

    // Crear una nueva respuesta con los mismos headers y contenido
    const newResponse = new NextResponse(data, {
      status: result.status,
      headers: {
        'Content-Type': contentType,
        ...(contentDisposition && { 'Content-Disposition': contentDisposition }),
        ...(etag && { 'ETag': etag }),
        ...(lastModified && { 'Last-Modified': lastModified }),
        ...(cacheControl && { 'Cache-Control': cacheControl }),
      }
    });

    return newResponse;
  } catch (error: any) {
    console.error('Error proxying file request:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}

