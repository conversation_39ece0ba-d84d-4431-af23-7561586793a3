import chalk from 'chalk';

export function colorizeRequest(method: string, path: string, status: number, time: number) {
  // Aplica diferentes colores para los métodos HTTP
  let methodColor;
  switch (method) {
    case 'GET':
      methodColor = status >= 400 ? chalk.red(method) : chalk.green(method); // Verde para GET
      break;
    case 'POST':
      methodColor = status >= 400 ? chalk.red(method) : chalk.yellow(method); // Amarillo para POST
      break;
    case 'PUT':
      methodColor = status >= 400 ? chalk.red(method) : chalk.blue(method); // Azul para PUT
      break;
    case 'DELETE':
      methodColor = status >= 400 ? chalk.red(method) : chalk.redBright(method); // Rojo para DELETE
      break;
    case 'PATCH':
      methodColor = status >= 400 ? chalk.red(method) : chalk.magenta(method); // Magenta para PATCH
      break
    default:
      methodColor = chalk.white(method); // Blanco para otros métodos
  }

  const pathColor = status >= 400 ? chalk.red(path) : chalk.cyan(path);
  const statusColor = status >= 400 ? chalk.red(status) : chalk.green(status);
  const timeColor = time >= 1000 ? chalk.red(time + 'ms') : chalk.green(time + 'ms');

  const timeNow = new Date().toISOString();

  // const MexicoTime = DateTime.now().setZone('America/Mexico_City')
  //   .toISO()
  // .toLocaleString(DateTime.DATETIME_FULL);

  console.log(`[${timeNow}] - ${methodColor} ${pathColor} ${statusColor} ${timeColor}`);
}