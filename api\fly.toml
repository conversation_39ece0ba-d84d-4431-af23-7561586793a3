# fly.toml app configuration file generated for autoop-prod on 2025-05-29T11:16:10-06:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'autoop-prod'
primary_region = 'iad'

kill_signal = "SIGTERM"   # Envía SIGTERM para el shutdown controlado
kill_timeout = 20         # Tiempo en segundos antes de forzar el cierre del servidor

swap_size_mb = 512        # Tamaño de la memoria swap

[build]

[env]
  PORT = '3000'

[http_service]
  internal_port = 3000     # Puerto en el que tu aplicación escucha (cambiado a 3000)
  force_https = false
  auto_stop_machines = "suspend"
  auto_start_machines = true
  min_machines_running = 1
  idle_timeout = "5m"
  processes = ["app"]

[[vm]]
  processes = ["app"]
  size = 'shared-cpu-1x'
  memory = '256mb'