import { getServerSession } from '@/actions/getSession'
import { DashboardThemeProvider } from '@/components/theme-provider-dashboard'
import DashboardLayout2 from './layout-2'
import { UserTypeModal } from '@/components/UserTypeModal'
import { UserProvider } from '@/context/user-context'


export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {

  const session = await getServerSession();

  const role = session?.user.role;
  const userType = session?.user.userType;
  // console.log('Session on [dashboard layout]:', session);

  return (
    <>
      <DashboardThemeProvider attribute="class" defaultTheme="light" enableSystem disableTransitionOnChange>
        <UserProvider session={session}>

          {
            role !== 'admin' && !userType ? (
              <>
                {/* Show modal to select user type here */}
                <UserTypeModal />
              </>
            ) : (
              <DashboardLayout2>
                {children}
              </DashboardLayout2>
            )
          }
        </UserProvider>
      </DashboardThemeProvider>
    </>
  )
}

