'use client'

// import { authClient } from '@/auth-client';
import { Calendar, Car, HeadphonesIcon, Key, Shield, Star, UserCheck } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

export default function Home() {

  // const session = authClient.useSession();

  // console.log('session: ', session.data);

  return (
    <>
      {/* Navigation */}

      {/* Hero Section */}
      <Hero />

      {/* How It Works */}
      <section id="how-it-works" className="py-16 md:py-24 bg-white">
        <div className="container mx-auto px-6">
          <h2 className="text-3xl font-bold text-center text-[#1a2b5e] mb-16">How Autoop Works</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-10">
            <div className="flex flex-col items-center text-center">
              <div className="bg-blue-50 p-4 rounded-full mb-6">
                <Car className="w-10 h-10 text-[#1a2b5e]" />
              </div>
              <h3 className="text-xl font-semibold mb-3 text-[#1a2b5e]">List or Browse</h3>
              <p className="text-gray-600">
                List your car or browse available vehicles in your area with detailed descriptions.
              </p>
            </div>
            <div className="flex flex-col items-center text-center">
              <div className="bg-blue-50 p-4 rounded-full mb-6">
                <Calendar className="w-10 h-10 text-[#1a2b5e]" />
              </div>
              <h3 className="text-xl font-semibold mb-3 text-[#1a2b5e]">Book & Verify</h3>
              <p className="text-gray-600">Select your dates and complete the verification process securely.</p>
            </div>
            <div className="flex flex-col items-center text-center">
              <div className="bg-blue-50 p-4 rounded-full mb-6">
                <Key className="w-10 h-10 text-[#1a2b5e]" />
              </div>
              <h3 className="text-xl font-semibold mb-3 text-[#1a2b5e]">Drive & Enjoy</h3>
              <p className="text-gray-600">Pick up your car and enjoy your journey with full insurance coverage.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Trust & Safety Features */}
      <section id="features" className="py-16 md:py-24 bg-gray-50">
        <div className="container mx-auto px-6">
          <h2 className="text-3xl font-bold text-center text-[#1a2b5e] mb-16">Trust & Safety Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="text-[#ff8c00] mb-4">
                <Shield className="w-10 h-10" />
              </div>
              <h3 className="text-xl font-semibold mb-2 text-[#1a2b5e]">Full Insurance</h3>
              <p className="text-gray-600">Comprehensive coverage for peace of mind during your rental.</p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="text-[#ff8c00] mb-4">
                <UserCheck className="w-10 h-10" />
              </div>
              <h3 className="text-xl font-semibold mb-2 text-[#1a2b5e]">ID Verification</h3>
              <p className="text-gray-600">Secure verification process for all users.</p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="text-[#ff8c00] mb-4">
                <HeadphonesIcon className="w-10 h-10" />
              </div>
              <h3 className="text-xl font-semibold mb-2 text-[#1a2b5e]">24/7 Support</h3>
              <p className="text-gray-600">Round-the-clock assistance whenever you need it.</p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="text-[#ff8c00] mb-4">
                <Star className="w-10 h-10" />
              </div>
              <h3 className="text-xl font-semibold mb-2 text-[#1a2b5e]">Rating System</h3>
              <p className="text-gray-600">Transparent feedback from verified users.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section id="testimonials" className="py-16 md:py-24 bg-white">
        <div className="container mx-auto px-6">
          <h2 className="text-3xl font-bold text-center text-[#1a2b5e] mb-16">What Our Users Say</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-gray-50 p-6 rounded-lg">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 rounded-full overflow-hidden mr-4">
                  <Image
                    src="/placeholder.svg?height=48&width=48"
                    alt="User avatar"
                    width={48}
                    height={48}
                    className="rounded-full"
                  />
                </div>
                <div>
                  <h4 className="font-semibold text-[#1a2b5e]">Michael Roberts</h4>
                  <div className="flex text-[#ff8c00]">
                    <Star className="w-4 h-4 fill-current" />
                    <Star className="w-4 h-4 fill-current" />
                    <Star className="w-4 h-4 fill-current" />
                    <Star className="w-4 h-4 fill-current" />
                    <Star className="w-4 h-4 fill-current" />
                  </div>
                </div>
              </div>
              <p className="text-gray-600">
                &quot;Renting a car through Autoop was a breeze. The verification process was thorough and I feel completely
                safe.&quot;
              </p>
            </div>
            <div className="bg-gray-50 p-6 rounded-lg">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 rounded-full overflow-hidden mr-4">
                  <Image
                    src="/placeholder.svg?height=48&width=48"
                    alt="User avatar"
                    width={48}
                    height={48}
                    className="rounded-full"
                  />
                </div>
                <div>
                  <h4 className="font-semibold text-[#1a2b5e]">Sarah Chen</h4>
                  <div className="flex text-[#ff8c00]">
                    <Star className="w-4 h-4 fill-current" />
                    <Star className="w-4 h-4 fill-current" />
                    <Star className="w-4 h-4 fill-current" />
                    <Star className="w-4 h-4 fill-current" />
                    <Star className="w-4 h-4 fill-current" />
                  </div>
                </div>
              </div>
              <p className="text-gray-600">
                &quot;I love how easy it was to list my car. The process was smooth and the insurance coverage gave me peace
                of mind.&quot;
              </p>
            </div>
            <div className="bg-gray-50 p-6 rounded-lg">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 rounded-full overflow-hidden mr-4">
                  <Image
                    src="/placeholder.svg?height=48&width=48"
                    alt="User avatar"
                    width={48}
                    height={48}
                    className="rounded-full"
                  />
                </div>
                <div>
                  <h4 className="font-semibold text-[#1a2b5e]">David Thompson</h4>
                  <div className="flex text-[#ff8c00]">
                    <Star className="w-4 h-4 fill-current" />
                    <Star className="w-4 h-4 fill-current" />
                    <Star className="w-4 h-4 fill-current" />
                    <Star className="w-4 h-4 fill-current" />
                    <Star className="w-4 h-4 fill-current" />
                  </div>
                </div>
              </div>
              <p className="text-gray-600">
                &quot;The support team was incredibly helpful when I needed assistance. Best car sharing platform I&apos;ve used!&quot;
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section id="get-started" className="py-16 md:py-24 bg-[#1a2b5e] text-white">
        <div className="container mx-auto px-6 text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to Join Autoop?</h2>
          <p className="text-lg mb-8 max-w-2xl mx-auto">
            Start your journey with us today and experience premium car sharing.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link
              href="/vehicles"
              className="bg-[#ff8c00] text-white px-6 py-3 rounded-md text-center hover:bg-[#e67e00] transition"
            >
              <span className="font-medium">List Your Car</span>
            </Link>
            <Link
              href="/vehicles"
              className="bg-white text-[#1a2b5e] px-6 py-3 rounded-md text-center hover:bg-gray-100 transition"
            >
              <span className="font-medium">Find a Ride</span>
            </Link>
          </div>
        </div>
      </section>


    </>
  );
}

function Hero() {
  return (
    <section className="w-full bg-[#1a2b5e] text-white">
      <div className="container mx-auto px-6 py-16 md:py-24 flex flex-col md:flex-row items-center">
        <div className="md:w-1/2 mb-10 md:mb-0">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4">Premium Car Sharing for Modern Drivers</h2>
          <p className="text-lg mb-8 text-gray-200">
            Connect with verified car owners and enjoy a seamless rental experience with full insurance coverage.
          </p>
          <div className="flex flex-col sm:flex-row gap-4">
            <Link
              href="/vehicles"
              className="bg-[#ff8c00] text-white px-6 py-3 rounded-md text-center hover:bg-[#e67e00] transition"
            >
              <span className="font-medium">List Your Car</span>
            </Link>
            <Link
              href="/vehicles"
              className="bg-white text-[#1a2b5e] px-6 py-3 rounded-md text-center hover:bg-gray-100 transition"
            >
              <span className="font-medium">Find a Ride</span>
            </Link>
          </div>
        </div>
        <div className="md:w-1/2 lg:pl-10">
          <Image
            src="/home/<USER>"
            alt="Luxury car for sharing"
            width={600}
            height={500}
            className="rounded-md object-cover "
            priority
          />
        </div>
      </div>
    </section>
  )
}
