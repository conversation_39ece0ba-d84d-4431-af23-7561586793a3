'use server';
import { cookies } from "next/headers";
import { getCookie, setCookie, deleteCookie } from "cookies-next/server";


export const getCookieServerByName = async ({
  name,
}: {
  name: string;
}) => {
  const cookie = getCookie(name, { cookies });
  return cookie;
};

export const setCookieServerByName = async ({
  name,
  value
}: {
  name: string;
  value: string;
}) => {
  console.log('setCookieServerByName', name, value);
  await setCookie(name, value, { cookies });
};

export const deleteCookieServerByName = async ({
  name
}: {
  name: string;
}) => {
  await deleteCookie(name, { cookies });
};