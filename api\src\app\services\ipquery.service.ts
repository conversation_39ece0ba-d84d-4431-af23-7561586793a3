// import { HttpException } from '@/exceptions/HttpExceptions';

// interface IpQueryResponse {
//   ip: string;
//   isp: {
//     asn: string;
//     org: string;
//     isp: string;
//   };
//   location: {
//     country: string;
//     country_code: string;
//     city: string;
//     state: string;
//     zipcode: string;
//     latitude: number;
//     longitude: number;
//     timezone: string;
//     localtime: string;
//   };
//   risk: {
//     is_mobile: boolean;
//     is_vpn: boolean;
//     is_tor: boolean;
//     is_proxy: boolean;
//     is_datacenter: boolean;
//     risk_score: number;
//   };
// }

// export class IpQueryService {
//   private readonly API_URL = 'https://api.ipquery.io';

//   async getIpInfo(ip?: string): Promise<IpQueryResponse> {
//     try {
//       const url = ip ? `${this.API_URL}/${ip}` : this.API_URL;

//       const response = await fetch(url, {
//         method: 'GET',
//         headers: {
//           'Accept': 'application/json'
//         }
//       });

//       if (!response.ok) {
//         throw new Error(`Error fetching IP info: ${response.statusText}`);
//       }

//       // Si no se proporciona una IP, la respuesta será solo la IP como texto
//       if (!ip) {
//         const ipText = await response.text();
//         return await this.getIpInfo(ipText);
//       }

//       return await response.json() as IpQueryResponse;
//     } catch (error) {
//       console.error('Error in IpQueryService:', error);
//       throw HttpException.InternalServerError('Failed to fetch IP information');
//     }
//   }
// }