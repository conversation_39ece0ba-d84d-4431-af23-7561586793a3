'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Star, ArrowLeft, Car, Calendar, CheckCircle } from 'lucide-react';
import { reviewsApi, type ReviewEligibility, type CreateReviewData } from '@/lib/api/reviews.api';
import { toast } from 'sonner';

// Las interfaces ya están definidas en reviews.api.ts

export default function ReviewPage() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const bookingId = params.bookingId as string;
  const token = searchParams.get('token');

  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [eligibility, setEligibility] = useState<ReviewEligibility | null>(null);
  const [rating, setRating] = useState<number | null>(null);
  const [comment, setComment] = useState('');
  const [submitted, setSubmitted] = useState(false);

  useEffect(() => {
    checkEligibility();
  }, [bookingId]);

  const checkEligibility = async () => {
    try {
      setLoading(true);
      const eligibilityData = await reviewsApi.client.checkEligibility(bookingId);
      setEligibility(eligibilityData);
    } catch (error) {
      console.error('Error checking eligibility:', error);
      toast.error('Error verificando elegibilidad');
      router.push('/dashboard/client/bookings');
    } finally {
      setLoading(false);
    }
  };

  const handleStarClick = (starRating: number) => {
    setRating(starRating);
  };

  const handleSubmit = async () => {
    if (!eligibility?.reservation) return;

    try {
      setSubmitting(true);

      const reviewData: CreateReviewData = {
        reservationId: bookingId,
        ...(rating !== null && { rating }),
        ...(comment.trim() && { comment: comment.trim() })
      };

      // Validar que al menos uno esté presente
      if (rating === null && !comment.trim()) {
        toast.error('Debes proporcionar al menos una calificación o un comentario');
        return;
      }

      await reviewsApi.client.create(reviewData);
      setSubmitted(true);
      toast.success('¡Reseña enviada exitosamente!');
    } catch (error) {
      console.error('Error submitting review:', error);
      toast.error('Error enviando reseña');
    } finally {
      setSubmitting(false);
    }
  };

  const handleSkip = () => {
    router.push('/dashboard/client/bookings');
  };

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardContent className="p-8">
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                <span className="ml-2">Verificando elegibilidad...</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (!eligibility?.eligible) {
    return (
      <div className="container mx-auto py-8">
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ArrowLeft className="h-5 w-5" />
                No es posible dejar una reseña
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Alert>
                <AlertDescription>
                  {eligibility?.reason || 'Esta reserva no es elegible para reseña.'}
                </AlertDescription>
              </Alert>
              <div className="mt-4">
                <Button onClick={() => router.push('/dashboard/client/bookings')}>
                  Volver a mis reservas
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (submitted) {
    return (
      <div className="container mx-auto py-8">
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardContent className="p-8 text-center">
              <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold mb-2">¡Reseña enviada!</h2>
              <p className="text-muted-foreground mb-6">
                Gracias por compartir tu experiencia. Tu reseña ayudará a otros usuarios.
              </p>
              <Button onClick={() => router.push('/dashboard/client/bookings')}>
                Volver a mis reservas
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  const reservation = eligibility.reservation!;

  return (
    <div className="container mx-auto py-8">
      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="h-5 w-5" />
              Comparte tu experiencia
            </CardTitle>
            <CardDescription>
              Tu opinión es valiosa para otros usuarios de Autoop
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Información de la reserva */}
            <div className="bg-muted/50 p-4 rounded-lg">
              <div className="flex items-center gap-3 mb-2">
                <Car className="h-5 w-5 text-primary" />
                <h3 className="font-semibold">
                  {reservation.vehicle.brand} {reservation.vehicle.model} {reservation.vehicle.year}
                </h3>
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Calendar className="h-4 w-4" />
                <span>
                  {new Date(reservation.startDate).toLocaleDateString()} - {' '}
                  {new Date(reservation.endDate).toLocaleDateString()}
                </span>
              </div>
            </div>

            {/* Calificación */}
            <div>
              <label className="text-sm font-medium mb-3 block">
                Calificación (opcional)
              </label>
              <div className="flex gap-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <button
                    key={star}
                    type="button"
                    onClick={() => handleStarClick(star)}
                    className="p-1 hover:scale-110 transition-transform"
                  >
                    <Star
                      className={`h-8 w-8 ${rating && star <= rating
                        ? 'fill-yellow-400 text-yellow-400'
                        : 'text-gray-300 hover:text-yellow-400'
                        }`}
                    />
                  </button>
                ))}
              </div>
              {rating && (
                <p className="text-sm text-muted-foreground mt-1">
                  {rating === 1 && 'Muy malo'}
                  {rating === 2 && 'Malo'}
                  {rating === 3 && 'Regular'}
                  {rating === 4 && 'Bueno'}
                  {rating === 5 && 'Excelente'}
                </p>
              )}
            </div>

            {/* Comentario */}
            <div>
              <label className="text-sm font-medium mb-3 block">
                Comentario (opcional)
              </label>
              <Textarea
                placeholder="Comparte tu experiencia con el vehículo y el anfitrión..."
                value={comment}
                onChange={(e) => setComment(e.target.value)}
                rows={4}
                maxLength={1000}
              />
              <p className="text-xs text-muted-foreground mt-1">
                {comment.length}/1000 caracteres
              </p>
            </div>

            {/* Botones */}
            <div className="flex gap-3 pt-4">
              <Button
                onClick={handleSubmit}
                disabled={submitting || (rating === null && !comment.trim())}
                className="flex-1"
              >
                {submitting ? 'Enviando...' : 'Enviar reseña'}
              </Button>
              <Button
                variant="outline"
                onClick={handleSkip}
                disabled={submitting}
              >
                Omitir
              </Button>
            </div>

            <p className="text-xs text-muted-foreground text-center">
              Tanto la calificación como el comentario son opcionales, pero al menos uno es requerido.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
