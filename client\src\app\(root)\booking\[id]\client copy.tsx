"use client"

import { useEffect } from "react"
// import { useRouter } from "next/navigation"
import { useBookingStore } from "@/lib/store/booking-store"
import BookingSteps from "@/components/booking/booking-steps"
import BookingDates from "@/components/booking/booking-dates"
import BookingCoverage from "@/components/booking/booking-coverage"
import BookingCustomize from "@/components/booking/booking-customize"
import BookingReview from "@/components/booking/booking-review"
import BookingConfirmation from "@/components/booking/booking-confirmation"

export default function BookingPageClient({ params }: { params: { id: string } }) {
  // const router = useRouter()
  const { vehicle, currentStep, setVehicle } = useBookingStore()

  useEffect(() => {
    // In a real app, you would fetch the vehicle data from an API
    // For now, we'll use mock data
    if (!vehicle) {
      setVehicle({
        id: params.id,
        make: "Mercedes-Benz",
        model: "S-Class",
        year: 2023,
        color: "Black",
        price: 299,
      })
    }
  }, [params.id, vehicle, setVehicle])

  if (!vehicle) {
    return <div className="container mx-auto py-8 min-h-screen flex flex-col justify-center items-center">Loading...</div>
  }

  return (
    <div className="min-h-screen flex flex-col">

      <div className="container mx-auto py-8 px-4">
        <BookingSteps currentStep={currentStep} />

        <div className="mt-8">
          {currentStep === 1 && <BookingDates vehicleId={params.id} />}
          {currentStep === 2 && <BookingCoverage />}
          {currentStep === 3 && <BookingCustomize />}
          {currentStep === 4 && <BookingReview />}
          {currentStep === 5 && <BookingConfirmation />}
        </div>
      </div>
    </div>
  )
}
