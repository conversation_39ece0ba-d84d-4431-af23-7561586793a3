"use client"

import { useState } from "react"
import { Search, Filter, MoreHorizontal, Eye, Download, DollarSign } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

// Mock data para pagos
const mockPayouts = [
  {
    id: "1",
    hostName: "<PERSON>",
    hostAvatar: "/placeholder.svg?height=32&width=32",
    amount: 3600,
    commission: 400,
    netAmount: 3200,
    status: "completed",
    method: "bank_transfer",
    reservationId: "RES-001",
    processedAt: "2024-01-20",
    createdAt: "2024-01-18",
  },
  {
    id: "2",
    hostName: "<PERSON>",
    hostAvatar: "/placeholder.svg?height=32&width=32",
    amount: 2250,
    commission: 250,
    netAmount: 2000,
    status: "pending",
    method: "paypal",
    reservationId: "RES-002",
    processedAt: null,
    createdAt: "2024-01-19",
  },
  {
    id: "3",
    hostName: "Carlos Ruiz",
    hostAvatar: "/placeholder.svg?height=32&width=32",
    amount: 1800,
    commission: 200,
    netAmount: 1600,
    status: "processing",
    method: "bank_transfer",
    reservationId: "RES-003",
    processedAt: null,
    createdAt: "2024-01-17",
  },
  {
    id: "4",
    hostName: "Ana López",
    hostAvatar: "/placeholder.svg?height=32&width=32",
    amount: 2700,
    commission: 300,
    netAmount: 2400,
    status: "failed",
    method: "bank_transfer",
    reservationId: "RES-004",
    processedAt: null,
    createdAt: "2024-01-16",
  },
]

const statusColors = {
  pending: "bg-yellow-100 text-yellow-800",
  processing: "bg-blue-100 text-blue-800",
  completed: "bg-green-100 text-green-800",
  failed: "bg-red-100 text-red-800",
}

const statusLabels = {
  pending: "Pendiente",
  processing: "Procesando",
  completed: "Completado",
  failed: "Fallido",
}

const methodLabels = {
  bank_transfer: "Transferencia",
  paypal: "PayPal",
  stripe: "Stripe",
}

export default function AdminPayoutsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [payouts] = useState(mockPayouts)

  const filteredPayouts = payouts.filter(
    (payout) =>
      payout.hostName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payout.reservationId.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const stats = {
    total: payouts.reduce((sum, p) => sum + p.amount, 0),
    commission: payouts.reduce((sum, p) => sum + p.commission, 0),
    pending: payouts.filter((p) => p.status === "pending").length,
    completed: payouts.filter((p) => p.status === "completed").length,
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Pagos</h1>
          <p className="text-muted-foreground">Gestiona los pagos y comisiones de la plataforma</p>
        </div>
        <Button>
          <Download className="mr-2 h-4 w-4" />
          Exportar
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Pagos</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${stats.total.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">+20% desde el mes pasado</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Comisiones</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${stats.commission.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              {Math.round((stats.commission / stats.total) * 100)}% del total
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pendientes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pending}</div>
            <p className="text-xs text-muted-foreground">Requieren procesamiento</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completados</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.completed}</div>
            <p className="text-xs text-muted-foreground">Pagos exitosos</p>
          </CardContent>
        </Card>
      </div>

      {/* Payouts Table */}
      <Card>
        <CardHeader>
          <CardTitle>Lista de Pagos</CardTitle>
          <CardDescription>Administra todos los pagos a anfitriones y comisiones</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Buscar pagos..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <Button variant="outline">
              <Filter className="mr-2 h-4 w-4" />
              Filtros
            </Button>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Anfitrión</TableHead>
                  <TableHead>Reserva</TableHead>
                  <TableHead>Método</TableHead>
                  <TableHead>Monto Bruto</TableHead>
                  <TableHead>Comisión</TableHead>
                  <TableHead>Monto Neto</TableHead>
                  <TableHead>Estado</TableHead>
                  <TableHead>Fecha</TableHead>
                  <TableHead className="text-right">Acciones</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredPayouts.map((payout) => (
                  <TableRow key={payout.id}>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <Avatar>
                          <AvatarImage src={payout.hostAvatar || "/placeholder.svg"} />
                          <AvatarFallback>
                            {payout.hostName
                              .split(" ")
                              .map((n) => n[0])
                              .join("")}
                          </AvatarFallback>
                        </Avatar>
                        <div className="font-medium">{payout.hostName}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-mono text-sm">{payout.reservationId}</div>
                    </TableCell>
                    <TableCell>{methodLabels[payout.method as keyof typeof methodLabels]}</TableCell>
                    <TableCell>${payout.amount.toLocaleString()}</TableCell>
                    <TableCell>${payout.commission.toLocaleString()}</TableCell>
                    <TableCell className="font-medium">${payout.netAmount.toLocaleString()}</TableCell>
                    <TableCell>
                      <Badge className={statusColors[payout.status as keyof typeof statusColors]}>
                        {statusLabels[payout.status as keyof typeof statusLabels]}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {payout.processedAt
                        ? new Date(payout.processedAt).toLocaleDateString()
                        : new Date(payout.createdAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Abrir menú</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>
                            <Eye className="mr-2 h-4 w-4" />
                            Ver detalles
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Download className="mr-2 h-4 w-4" />
                            Descargar recibo
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
