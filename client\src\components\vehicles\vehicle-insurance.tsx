import { Check } from "lucide-react"

export default function VehicleInsurance() {
  return (
    <div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="border rounded-lg p-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="font-semibold">Basic Coverage</h3>
            <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Included</span>
          </div>
          <ul className="space-y-3">
            <li className="flex items-start">
              <Check className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" />
              <span className="text-sm">Third party liability</span>
            </li>
            <li className="flex items-start">
              <Check className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" />
              <span className="text-sm">24/7 roadside assistance</span>
            </li>
          </ul>
        </div>

        <div className="border rounded-lg p-4 bg-blue-50">
          <div className="flex justify-between items-center mb-4">
            <h3 className="font-semibold">Premium Coverage</h3>
            <span className="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded">Recommended</span>
          </div>
          <ul className="space-y-3">
            <li className="flex items-start">
              <Check className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" />
              <span className="text-sm">All Basic Coverage benefits</span>
            </li>
            <li className="flex items-start">
              <Check className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" />
              <span className="text-sm">Collision damage waiver</span>
            </li>
            <li className="flex items-start">
              <Check className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" />
              <span className="text-sm">Personal accident insurance</span>
            </li>
          </ul>
        </div>

        <div className="border rounded-lg p-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="font-semibold">Full Coverage</h3>
            <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Ultimate</span>
          </div>
          <ul className="space-y-3">
            <li className="flex items-start">
              <Check className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" />
              <span className="text-sm">All Premium Coverage benefits</span>
            </li>
            <li className="flex items-start">
              <Check className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" />
              <span className="text-sm">Zero deductible</span>
            </li>
            <li className="flex items-start">
              <Check className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" />
              <span className="text-sm">Personal effects coverage</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  )
}
