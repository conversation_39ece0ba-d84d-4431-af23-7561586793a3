"use client"

import { useEffect, useState } from "react"
import { DateRang<PERSON>, type RangeKeyDict } from "react-date-range"
import { /* addDays, */ format } from "date-fns"
import { es } from "date-fns/locale"
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { CalendarIcon } from "lucide-react"
import "react-date-range/dist/styles.css"
import "react-date-range/dist/theme/default.css"
import { DialogTitle } from '@radix-ui/react-dialog'

interface DateRangeModalProps {
  unavailableDates?: string[] // ISO date strings
  onChange: (range: { startDate: Date; endDate: Date }) => void
  initialDateRange?: { startDate: Date | null; endDate: Date | null }
}

export default function DateRangeModal({ unavailableDates = [], onChange, initialDateRange }: DateRangeModalProps) {
  const [isOpen, setIsOpen] = useState(false)

  /** 
   * Check if today and tomorrow are unavailable dates.
   * If so, set the default date range to the next available date.
   * 
  */
  function findNextAvailableDateRange(unavailableDates: string[]): { startDate: Date; endDate: Date } {
    const unavailable = new Set(unavailableDates.map(d => new Date(d).toDateString()));
    const today = new Date();
    console.log('today: ', today);
    // Empieza desde mañana
    const start = new Date(today);
    console.log('start: ', start);
    start.setDate(start.getDate() + 1);
    console.log('start + 1: ', start);

    // Busca hasta 1 año adelante para evitar loops infinitos
    for (let i = 0; i < 365; i++) {
      const startStr = start.toDateString();
      const next = new Date(start);
      next.setDate(start.getDate() + 1);
      const nextStr = next.toDateString();

      if (!unavailable.has(startStr)) {
        // Si el siguiente día también está libre, selecciona ambos
        if (!unavailable.has(nextStr)) {
          return { startDate: start, endDate: next };
        } else {
          // Si solo hay un día libre, selecciona solo ese día
          return { startDate: start, endDate: start };
        }
      }
      // Avanza al siguiente día
      start.setDate(start.getDate() + 1);
    }
    // Si no encuentra nada, retorna mañana
    const fallback = new Date(today);
    fallback.setDate(today.getDate() + 1);
    return { startDate: fallback, endDate: fallback };
  }

  // ...en tu componente...

  const initialRange = initialDateRange && initialDateRange.startDate && initialDateRange.endDate
    ? { startDate: initialDateRange.startDate, endDate: initialDateRange.endDate }
    : findNextAvailableDateRange(unavailableDates);

  const [state, setState] = useState<RangeKeyDict['selection'][]>([
    {
      startDate: initialRange.startDate,
      endDate: initialRange.endDate,
      key: "selection",
    },
  ]);

  // Convert ISO strings to Date objects
  const disabledDates = unavailableDates.map((dateStr) => new Date(dateStr))

  // Function to check if a date is disabled
  const isDateDisabled = (date: Date) => {
    // Disable dates before today
    if (date < new Date()) {
      return true
    }

    // Disable specific unavailable dates
    return disabledDates.some(
      (disabledDate) =>
        date.getFullYear() === disabledDate.getFullYear() &&
        date.getMonth() === disabledDate.getMonth() &&
        date.getDate() === disabledDate.getDate(),
    )
  }

  const handleSelect = (ranges: RangeKeyDict) => {
    const selection = ranges.selection
    setState([selection])
  }

  const handleApply = () => {
    if (state[0].startDate && state[0].endDate) {
      onChange({
        startDate: state[0].startDate,
        endDate: state[0].endDate,
      })
    }
    setIsOpen(false)
  }
  const [direction, setDirection] = useState<'horizontal' | 'vertical'>('horizontal')

  // const direction = window.matchMedia("(max-width: 700px)").matches ? 'vertical' : 'horizontal'
  const handleResize = () => {
    setDirection(window.matchMedia("(max-width: 750px)").matches ? 'vertical' : 'horizontal')
  }

  useEffect(() => {
    handleResize()
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      {/* <DialogTrigger asChild>
        <Button
          variant="outline"
          className="w-full justify-start text-left font-normal h-auto py-2 px-3"
          onClick={() => setIsOpen(true)}
        >
          <CalendarIcon className="mr-2 h-4 w-4 text-gray-500" />
          <span>
            {state[0].startDate && state[0].endDate
              ? `${format(state[0].startDate, "MMM dd, yyyy")} - ${format(state[0].endDate, "MMM dd, yyyy")}`
              : "Selecciona las fechas"}
          </span>
        </Button>
      </DialogTrigger> */}
      <DialogTrigger asChild>
        <Button
          variant="outline"
          className="w-full justify-start text-left font-normal h-auto py-2 px-3"
          onClick={() => setIsOpen(true)}
        >
          <CalendarIcon className="mr-2 h-4 w-4 text-gray-500" />
          <span>
            {state[0].startDate && state[0].endDate
              ? `${format(state[0].startDate, "MMM dd, yyyy")} - ${format(state[0].endDate, "MMM dd, yyyy")}`
              : "Selecciona las fechas"}
          </span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[725px] p-0 h-[82%] overflow-auto rounded-lg">
        <DialogTitle asChild>
          <div className="p-4 border-b" >
            <h2 className="text-lg font-semibold">Selecciona el rango de fechas</h2>
            <p className="text-sm text-gray-500">Elige tus fechas de recogida y devolución</p>
          </div>
        </DialogTitle>
        <div className="p-4 flex justify-center">
          <DateRange
            // editableDateInputs={true}
            onChange={handleSelect}
            moveRangeOnFirstSelection={false}
            ranges={state}
            months={2}
            direction={direction}
            locale={es}
            disabledDay={isDateDisabled}
            minDate={new Date()}
            rangeColors={["#1a2b5e"]}
            className="border rounded-md"
          />
        </div>
        <div className="flex justify-end gap-2 p-4 border-t">
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Cancelar
          </Button>
          <Button className="bg-[#1a2b5e] hover:bg-[#152348]" onClick={handleApply}>
            <span className="font-medium">Aplicar</span>
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
