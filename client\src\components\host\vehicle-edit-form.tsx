import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { toast } from "react-hot-toast"
import { useMutation, useQuery } from "@tanstack/react-query"
import { vehiclesApi, VehicleFormData, VehicleDocuments } from "@/lib/api/vehicles.api"
import { statesApi } from "@/lib/api/states.api"
import { useRouter } from 'next/navigation'
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import FileUploadInput from "@/components/forms/file-upload-input"
import ImagePreview from "@/components/forms/image-preview"
import DocumentPreview from "@/components/forms/document-preview"
import { ScrollArea } from '../ui/scroll-area'

// Reutilizamos el mismo esquema del formulario de creación pero haciendo opcionales los archivos
const vehicleFormSchema = z.object({
  // Información básica
  make: z.string().min(1, { message: "La marca es requerida" }),
  model: z.string().min(1, { message: "El modelo es requerido" }),
  year: z.string().min(4, { message: "El año es requerido" }),
  color: z.string().min(1, { message: "El color es requerido" }),
  vin: z.string().min(17, { message: "El VIN debe tener 17 caracteres" }),
  plate: z.string().min(1, { message: "La placa es requerida" }),
  registrationNumber: z.string().min(1, { message: "El número de registro es requerido" }),
  insurancePolicy: z.string().min(1, { message: "La póliza de seguro es requerida" }),

  // Características técnicas
  transmission: z.string(),
  fuelType: z.string(),
  seats: z.string().min(1, { message: "El número de asientos es requerido" }),
  mileage: z.string().min(1, { message: "El kilometraje es requerido" }),
  engineSize: z.string(),
  trim: z.string(),
  bodyType: z.string(),

  // Precios
  dailyRate: z.string().min(1, { message: "La tarifa diaria es requerida" }),
  weeklyRate: z.string().min(1, { message: "La tarifa semanal es requerida" }),
  monthlyRate: z.string().min(1, { message: "La tarifa mensual es requerida" }),

  // Descripción y reglas
  description: z.string().min(20, { message: "La descripción debe tener al menos 20 caracteres" }).max(200, { message: "La descripción no puede tener más de 200 caracteres" }),
  rules: z.string().min(20, { message: "Las reglas deben tener al menos 20 caracteres" }),

  // Ubicación
  location: z.string().min(1, { message: "La ubicación es requerida" }),
  state_code: z.string().min(1, { message: "El estado es requerido" }),
  country_code: z.string().min(1, { message: "El país es requerido" }),
  amenities: z.array(z.string()).min(1, { message: "Selecciona al menos una comodidad" }),

  // Para imágenes, permitimos que sea opcional en edición
  images: z.array(z.custom<File>())
    .optional(),

  // Documentos opcionales en edición
  insurancePolicyDocument: z.array(z.custom<File>())
    .optional(),
  plateDocument: z.array(z.custom<File>())
    .optional(),
  vinDocument: z.array(z.custom<File>())
    .optional(),
  registrationDocument: z.array(z.custom<File>())
    .optional(),
})

type VehicleFormValues = z.infer<typeof vehicleFormSchema>

interface VehicleEditFormProps {
  vehicleId: string
}

export default function VehicleEditForm({ vehicleId }: VehicleEditFormProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [existingImages, setExistingImages] = useState<string[]>([])
  const [imagesToRemove, setImagesToRemove] = useState<string[]>([])
  const [vehicleDocuments, setVehicleDocuments] = useState<VehicleDocuments | null>(null)
  const [documentsToRemove, setDocumentsToRemove] = useState<{
    plateDocument?: boolean;
    vinDocument?: boolean;
    registrationDocument?: boolean;
    insurancePolicyDocument?: boolean;
  }>({});

  // Obtener datos del vehículo
  const { data: vehicle, isLoading: isLoadingVehicle } = useQuery({
    queryKey: ['vehicle', vehicleId],
    queryFn: () => vehiclesApi.host.getById(vehicleId),
  })

  // Obtener estados
  const { data: states } = useQuery({
    queryKey: ['states'],
    queryFn: statesApi.getAll,
  })

  // Cargar documentos del vehículo
  useEffect(() => {
    if (vehicleId) {
      const loadDocuments = async () => {
        try {
          const documents = await vehiclesApi.host.getDocuments(vehicleId)
          setVehicleDocuments(documents)
        } catch (error) {
          console.error("Error loading vehicle documents:", error)
          toast.error("Error al cargar los documentos del vehículo")
        }
      }

      loadDocuments()
    }
  }, [vehicleId])

  const form = useForm<VehicleFormValues>({
    resolver: zodResolver(vehicleFormSchema),
    defaultValues: {
      make: "",
      model: "",
      year: "",
      color: "",
      vin: "",
      plate: "",
      registrationNumber: "",
      insurancePolicy: "",
      transmission: "automatic",
      fuelType: "gasoline",
      seats: "",
      mileage: "",
      dailyRate: "",
      weeklyRate: "",
      monthlyRate: "",
      description: "",
      rules: "",
      location: "",
      engineSize: "",
      trim: "",
      bodyType: "sedan",
      amenities: [],
      state_code: "",
      country_code: "",
      images: [],
      vinDocument: [],
      plateDocument: [],
      registrationDocument: [],
      insurancePolicyDocument: [],
    }
  })

  // Cargar datos del vehículo cuando estén disponibles
  useEffect(() => {
    if (vehicle && states) {

      // Luego establecemos el resto de los valores
      form.reset({
        make: vehicle.make,
        model: vehicle.model,
        year: vehicle.year.toString(),
        color: vehicle.color,
        vin: vehicle.vin,
        plate: vehicle.plate,
        registrationNumber: vehicle.features?.registrationNumber,
        insurancePolicy: vehicle.features?.insurancePolicy,
        transmission: vehicle.transmission,
        fuelType: vehicle.features?.fuelType,
        seats: vehicle.features?.seats?.toString() || "",
        mileage: vehicle.features?.mileage?.toString() || "",
        dailyRate: vehicle.price?.toString() || "",
        weeklyRate: vehicle.features?.weeklyRate?.toString() || "",
        monthlyRate: vehicle.features?.monthlyRate?.toString() || "",
        description: vehicle.description || "",
        rules: vehicle.features?.rules || "",
        location: vehicle.features?.location || "",
        engineSize: vehicle.engineSize?.toString() || "",
        trim: vehicle.trim || "",
        bodyType: vehicle.bodyType || "sedan",
        amenities: vehicle.amenities || [],
        state_code: vehicle.state_code || "",
        country_code: vehicle.country_code || "mx",
        images: [],
        vinDocument: [],
        plateDocument: [],
        registrationDocument: [],
        insurancePolicyDocument: [],
      }, { keepValues: false });

      // Asegurarnos de que el estado se establezca correctamente después del reset
      setTimeout(() => {
        form.setValue('state_code', vehicle.state_code || "");
        form.setValue('country_code', vehicle.country_code || "mx");
      }, 0);

      // Guardar imágenes existentes
      if (vehicle.images && Array.isArray(vehicle.images)) {
        setExistingImages(vehicle.images);
      }
    }
  }, [vehicle, form, states]);

  // Mutación para actualizar información del vehículo
  const updateVehicleMutation = useMutation({
    mutationFn: (data: Partial<VehicleFormData>) => vehiclesApi.host.update(vehicleId, data),
    onSuccess: () => {
      toast.success("Información actualizada exitosamente")

      // Verificar si hay archivos para actualizar
      const hasFilesToUpdate = checkIfFilesChanged()

      if (hasFilesToUpdate) {
        updateFilesMutation.mutate()
      } else {
        setIsLoading(false)
        router.push(`/dashboard/host/vehicles/${vehicleId}`)
      }
    },
    onError: (error) => {
      toast.error(error.message || "Error al actualizar la información")
      setIsLoading(false)
    }
  })

  // Mutación para actualizar archivos
  const updateFilesMutation = useMutation({
    mutationFn: () => {
      const files = {
        images: form.getValues("images"),
        vinDocument: form.getValues("vinDocument"),
        plateDocument: form.getValues("plateDocument"),
        registrationDocument: form.getValues("registrationDocument"),
        insurancePolicyDocument: form.getValues("insurancePolicyDocument"),
      }

      // Incluir información de imágenes a eliminar
      return vehiclesApi.host.updateFiles(vehicleId, files, imagesToRemove)
    },
    onSuccess: () => {
      toast.success("Archivos actualizados exitosamente")
      router.push(`/dashboard/host/vehicles/${vehicleId}`)
    },
    onError: (error) => {
      toast.error(error.message || "Error al actualizar los archivos")
    },
    onSettled: () => {
      setIsLoading(false)
    }
  })

  // Verificar si hay cambios en los archivos
  const checkIfFilesChanged = () => {
    const images = form.getValues("images");
    const vinDocument = form.getValues("vinDocument");
    const plateDocument = form.getValues("plateDocument");
    const registrationDocument = form.getValues("registrationDocument");
    const insurancePolicyDocument = form.getValues("insurancePolicyDocument");

    // Verificar si hay imágenes nuevas o imágenes a eliminar
    const hasNewImages = images && images.length > 0;
    const hasImagesToRemove = imagesToRemove.length > 0;

    // Verificar si hay documentos nuevos
    const hasNewDocuments =
      (vinDocument && vinDocument.length > 0) ||
      (plateDocument && plateDocument.length > 0) ||
      (registrationDocument && registrationDocument.length > 0) ||
      (insurancePolicyDocument && insurancePolicyDocument.length > 0);

    return hasNewImages || hasImagesToRemove || hasNewDocuments;
  }

  const onSubmit = async (data: VehicleFormValues) => {
    setIsLoading(true)

    // Filtrar solo los campos de datos (no archivos)
    const formData = { ...data }
    delete formData.images
    delete formData.vinDocument
    delete formData.plateDocument
    delete formData.registrationDocument
    delete formData.insurancePolicyDocument

    // Convertir los tipos de datos según lo esperado por la API
    const vehicleData: Partial<VehicleFormData> = {
      make: formData.make,
      model: formData.model,
      year: parseInt(formData.year),
      color: formData.color,
      vin: formData.vin,
      plate: formData.plate,
      state_code: formData.state_code,
      country_code: formData.country_code,
      price: parseFloat(formData.dailyRate),
      description: formData.description,

      // Nuevos campos estructurados
      engineSize: parseFloat(formData.engineSize),
      transmission: formData.transmission,
      trim: formData.trim,
      bodyType: formData.bodyType,

      // Campos en features
      features: {
        fuelType: formData.fuelType,
        seats: parseInt(formData.seats),
        mileage: parseInt(formData.mileage),
        registrationNumber: formData.registrationNumber,
        insurancePolicy: formData.insurancePolicy,
        rules: formData.rules,
        location: formData.location,
        weeklyRate: parseFloat(formData.weeklyRate),
        monthlyRate: parseFloat(formData.monthlyRate),
      },

      amenities: formData.amenities,
    }

    // Actualizar información del vehículo
    updateVehicleMutation.mutate(vehicleData)
  }

  // Manejar eliminación de imágenes existentes
  const handleRemoveExistingImage = (index: number) => {
    const imageUrl = existingImages[index]
    setExistingImages(prev => prev.filter((_, i) => i !== index))
    setImagesToRemove(prev => [...prev, imageUrl])
  }

  // Manejar eliminación de documentos existentes
  const handleRemoveDocument = (documentType: keyof typeof documentsToRemove) => {
    setDocumentsToRemove(prev => ({
      ...prev,
      [documentType]: true
    }));
  };

  // Lista de comodidades disponibles
  const amenitiesList = [
    { id: "bluetooth", label: "Bluetooth" },
    { id: "usb", label: "Puertos USB" },
    { id: "aux", label: "Entrada AUX" },
    { id: "ac", label: "Aire acondicionado" },
    { id: "gps", label: "GPS" },
    { id: "backup_camera", label: "Cámara de reversa" },
    { id: "cruise_control", label: "Control de crucero" },
    { id: "child_seat", label: "Asiento para niños" },
    { id: "sunroof", label: "Quemacocos" },
    { id: "heated_seats", label: "Asientos calefactados" },
  ]

  if (isLoadingVehicle) {
    return <div>Cargando información del vehículo...</div>
  }

  return (
    <Form {...form}>
      <ScrollArea className="w-full h-full">
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          {/* Información Básica */}
          <Card>
            <CardHeader>
              <CardTitle>Información Básica</CardTitle>
              <CardDescription>Detalles principales de tu vehículo</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-start">
                <FormField
                  control={form.control}
                  name="make"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Marca</FormLabel>
                      <FormControl>
                        <Input placeholder="Toyota, BMW, Tesla..." {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="model"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Modelo</FormLabel>
                      <FormControl>
                        <Input placeholder="Corolla, X5, Model 3..." {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="year"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Año</FormLabel>
                      <FormControl>
                        <Input placeholder="2023" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="color"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Color</FormLabel>
                      <FormControl>
                        <Input placeholder="Rojo, Azul, Negro..." {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Características Técnicas */}
          <Card>
            <CardHeader>
              <CardTitle>Características Técnicas</CardTitle>
              <CardDescription>Detalles técnicos de tu vehículo</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="transmission"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Transmisión</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Selecciona el tipo de transmisión" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="automatic">Automática</SelectItem>
                          <SelectItem value="manual">Manual</SelectItem>
                          <SelectItem value="cvt">CVT</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="fuelType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tipo de Combustible</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Selecciona el tipo de combustible" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="gasoline">Gasolina</SelectItem>
                          <SelectItem value="diesel">Diésel</SelectItem>
                          <SelectItem value="electric">Eléctrico</SelectItem>
                          <SelectItem value="hybrid">Híbrido</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="seats"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Número de Asientos</FormLabel>
                      <FormControl>
                        <Input placeholder="5" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="mileage"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Kilometraje</FormLabel>
                      <FormControl>
                        <Input placeholder="15000" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="engineSize"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tamaño del Motor (L)</FormLabel>
                      <FormControl>
                        <Input placeholder="2.0" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="trim"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Versión (Trim)</FormLabel>
                      <FormControl>
                        <Input placeholder="LE, XLE, Sport..." {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="bodyType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tipo de Carrocería</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecciona el tipo de carrocería" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="sedan">Sedán</SelectItem>
                          <SelectItem value="suv">SUV</SelectItem>
                          <SelectItem value="hatchback">Hatchback</SelectItem>
                          <SelectItem value="coupe">Coupé</SelectItem>
                          <SelectItem value="pickup">Pickup</SelectItem>
                          <SelectItem value="van">Van</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Precios */}
          <Card id="pricing">
            <CardHeader>
              <CardTitle>Precios</CardTitle>
              <CardDescription>Establece las tarifas para tu vehículo</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="dailyRate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tarifa Diaria (MXN)</FormLabel>
                      <FormControl>
                        <Input placeholder="1000" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="weeklyRate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tarifa Semanal (MXN)</FormLabel>
                      <FormControl>
                        <Input placeholder="6000" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="monthlyRate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tarifa Mensual (MXN)</FormLabel>
                      <FormControl>
                        <Input placeholder="20000" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Descripción y Reglas */}
          <Card>
            <CardHeader>
              <CardTitle>Descripción y Reglas</CardTitle>
              <CardDescription>Información para los huéspedes</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Descripción</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe tu vehículo, sus características especiales, etc."
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Mínimo 20 caracteres, máximo 200
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="rules"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Reglas</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Establece las reglas para el uso de tu vehículo"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Mínimo 20 caracteres
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Ubicación */}
          <Card>
            <CardHeader>
              <CardTitle>Ubicación</CardTitle>
              <CardDescription>Dónde se encuentra tu vehículo</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Dirección</FormLabel>
                    <FormControl>
                      <Input placeholder="Calle, número, colonia..." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="state_code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Estado</FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <FormControl>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Selecciona un estado" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {states?.map((state) => (
                            <SelectItem key={state.code} value={state.code}>
                              {state.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="country_code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>País</FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <FormControl>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Selecciona un país" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="mx">México</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Comodidades */}
          <Card>
            <CardHeader>
              <CardTitle>Comodidades</CardTitle>
              <CardDescription>Características adicionales de tu vehículo</CardDescription>
            </CardHeader>
            <CardContent>
              <FormField
                control={form.control}
                name="amenities"
                render={() => (
                  <FormItem>
                    <div className="mb-4">
                      <FormLabel className="text-base">Selecciona las comodidades</FormLabel>
                      <FormDescription>
                        Marca todas las que apliquen a tu vehículo
                      </FormDescription>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                      {amenitiesList.map((amenity) => (
                        <FormField
                          key={amenity.id}
                          control={form.control}
                          name="amenities"
                          render={({ field }) => {
                            return (
                              <FormItem
                                key={amenity.id}
                                className="flex flex-row items-start space-x-3 space-y-0"
                              >
                                <FormControl>
                                  <Checkbox
                                    checked={field.value?.includes(amenity.id)}
                                    onCheckedChange={(checked) => {
                                      return checked
                                        ? field.onChange([...field.value, amenity.id])
                                        : field.onChange(
                                          field.value?.filter(
                                            (value) => value !== amenity.id
                                          )
                                        )
                                    }}
                                  />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  {amenity.label}
                                </FormLabel>
                              </FormItem>
                            )
                          }}
                        />
                      ))}
                    </div>
                    <div className="mt-2 flex flex-wrap gap-1">
                      {form.watch("amenities").map((amenity) => {
                        const amenityLabel = amenitiesList.find(a => a.id === amenity)?.label
                        return (
                          <Badge key={amenity} variant="secondary">
                            {amenityLabel}
                          </Badge>
                        )
                      })}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Documentación */}
          <Card>
            <CardHeader>
              <CardTitle>Documentación</CardTitle>
              <CardDescription>Información legal y de registro</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex flex-col gap-4">
                <FormField
                  control={form.control}
                  name="plate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Placas (sin espacios ni guiones)</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="ABC123 para (ABC-123)"
                          {...field}
                          onChange={(e) => {
                            const value = e.target.value.replace(/-/g, '').replace(/\s/g, '');
                            field.onChange(value.toUpperCase());
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Documento de placas */}
                <FileUploadInput
                  form={form}
                  name="plateDocument"
                  label="Imagen de placas"
                  description="Sube una foto clara de las placas del vehículo"
                  maxSize={2 * 1024 * 1024}
                  maxFiles={1}
                />

                <FormField
                  control={form.control}
                  name="vin"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>VIN (Número de serie)</FormLabel>
                      <FormControl>
                        <Input placeholder="1HGBH41JXMN109186" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Documento de VIN */}
                <FileUploadInput
                  form={form}
                  name="vinDocument"
                  label="Imagen del VIN"
                  accept="image/*,application/pdf"
                  description="Sube una foto clara del número de serie del vehículo"
                  maxSize={2 * 1024 * 1024}
                  maxFiles={1}
                />

                <FormField
                  control={form.control}
                  name="registrationNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Número de Registro</FormLabel>
                      <FormControl>
                        <Input placeholder="Número de tarjeta de circulación" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Documento de registro */}
                <FileUploadInput
                  form={form}
                  name="registrationDocument"
                  label="Tarjeta de Circulación"
                  accept="image/*,application/pdf"
                  description="Sube una foto de la tarjeta de circulación"
                  maxSize={2 * 1024 * 1024}
                  maxFiles={1}
                />

                <FormField
                  control={form.control}
                  name="insurancePolicy"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Número de Póliza de Seguro</FormLabel>
                      <FormControl>
                        <Input placeholder="12345678901234567890" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Documento de póliza de seguro */}
                <FileUploadInput
                  form={form}
                  name="insurancePolicyDocument"
                  label="Imagen de la Póliza de Seguro"
                  description="Sube una foto clara de la póliza de seguro"
                  maxSize={2 * 1024 * 1024}
                  maxFiles={1}
                />
              </div>
            </CardContent>
          </Card>

          {/* Sección de imágenes existentes */}
          <Card>
            <CardHeader>
              <CardTitle>Imágenes Actuales</CardTitle>
              <CardDescription>Puedes eliminar imágenes o agregar nuevas</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {existingImages.map((imageUrl, index) => (
                  <div key={index} className="relative">
                    <ImagePreview
                      url={imageUrl}
                      onRemove={() => handleRemoveExistingImage(index)}
                    />
                  </div>
                ))}
              </div>

              {/* Subir nuevas imágenes */}
              <div className="mt-4">
                <FileUploadInput
                  form={form}
                  name="images"
                  label="Agregar Nuevas Fotos"
                  description="Sube fotos adicionales de tu vehículo"
                  maxSize={2 * 1024 * 1024}
                  maxFiles={10}
                  multiple
                />
              </div>
            </CardContent>
          </Card>

          {/* Sección de documentos existentes */}
          <Card id="documents" className="mt-4">
            <CardHeader>
              <CardTitle>
                Documentos del Vehículo{' '}
                <span className="text-sm text-muted-foreground">
                  (Solo visibles para ti y para la aprobación de tu vehículo)
                </span>
              </CardTitle>
              <CardDescription>Documentos actuales y opciones para actualizarlos</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Documento de placas */}
                <div>
                  <h3 className="text-sm font-medium mb-2">Documento de placas</h3>
                  {vehicleDocuments && (
                    documentsToRemove.plateDocument ? (
                      <div className="mb-4">
                        <p className="text-sm text-muted-foreground">El documento será eliminado al guardar</p>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setDocumentsToRemove(prev => ({ ...prev, plateDocument: false }))}
                          className="mt-2"
                        >
                          Cancelar eliminación
                        </Button>
                      </div>
                    ) : (
                      <DocumentPreview
                        documentKey={vehicleDocuments.plateDocument}
                        label="Imagen de placas"
                        onRemove={() => handleRemoveDocument('plateDocument')}
                      />
                    )
                  )}
                  <div className="mt-4">
                    <FileUploadInput
                      form={form}
                      name="plateDocument"
                      label={vehicleDocuments?.plateDocument ? "Actualizar imagen de placas" : "Subir imagen de placas"}
                      description="Sube una foto clara de las placas del vehículo"
                      maxSize={2 * 1024 * 1024}
                      maxFiles={1}
                    />
                  </div>
                </div>

                {/* Documento de VIN */}
                <div>
                  <h3 className="text-sm font-medium mb-2">Documento de VIN</h3>
                  {vehicleDocuments && (
                    documentsToRemove.vinDocument ? (
                      <div className="mb-4">
                        <p className="text-sm text-muted-foreground">El documento será eliminado al guardar</p>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setDocumentsToRemove(prev => ({ ...prev, vinDocument: false }))}
                          className="mt-2"
                        >
                          Cancelar eliminación
                        </Button>
                      </div>
                    ) : (
                      <DocumentPreview
                        documentKey={vehicleDocuments.vinDocument}
                        label="Imagen del VIN"
                        onRemove={() => handleRemoveDocument('vinDocument')}
                      />
                    )
                  )}
                  <div className="mt-4">
                    <FileUploadInput
                      form={form}
                      name="vinDocument"
                      label={vehicleDocuments?.vinDocument ? "Actualizar imagen del VIN" : "Subir imagen del VIN"}
                      accept="image/*,application/pdf"
                      description="Sube una foto clara del número de serie del vehículo"
                      maxSize={2 * 1024 * 1024}
                      maxFiles={1}
                    />
                  </div>
                </div>

                {/* Documento de registro */}
                <div>
                  <h3 className="text-sm font-medium mb-2">Documento de registro</h3>
                  {vehicleDocuments && (
                    documentsToRemove.registrationDocument ? (
                      <div className="mb-4">
                        <p className="text-sm text-muted-foreground">El documento será eliminado al guardar</p>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setDocumentsToRemove(prev => ({ ...prev, registrationDocument: false }))}
                          className="mt-2"
                        >
                          Cancelar eliminación
                        </Button>
                      </div>
                    ) : (
                      <DocumentPreview
                        documentKey={vehicleDocuments.registrationDocument}
                        label="Documento de registro"
                        onRemove={() => handleRemoveDocument('registrationDocument')}
                      />
                    )
                  )}
                  <div className="mt-4">
                    <FileUploadInput
                      form={form}
                      name="registrationDocument"
                      label={vehicleDocuments?.registrationDocument ? "Actualizar documento de registro" : "Subir documento de registro"}
                      accept="image/*,application/pdf"
                      description="Sube una foto o PDF del documento de registro del vehículo"
                      maxSize={2 * 1024 * 1024}
                      maxFiles={1}
                    />
                  </div>
                </div>

                {/* Documento de póliza de seguro */}
                <div>
                  <h3 className="text-sm font-medium mb-2">Póliza de seguro</h3>
                  {vehicleDocuments && (
                    documentsToRemove.insurancePolicyDocument ? (
                      <div className="mb-4">
                        <p className="text-sm text-muted-foreground">El documento será eliminado al guardar</p>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setDocumentsToRemove(prev => ({ ...prev, insurancePolicyDocument: false }))}
                          className="mt-2"
                        >
                          Cancelar eliminación
                        </Button>
                      </div>
                    ) : (
                      <DocumentPreview
                        documentKey={vehicleDocuments.insurancePolicyDocument}
                        label="Póliza de seguro"
                        onRemove={() => handleRemoveDocument('insurancePolicyDocument')}
                      />
                    )
                  )}
                  <div className="mt-4">
                    <FileUploadInput
                      form={form}
                      name="insurancePolicyDocument"
                      label={vehicleDocuments?.insurancePolicyDocument ? "Actualizar póliza de seguro" : "Subir póliza de seguro"}
                      accept="image/*,application/pdf"
                      description="Sube una foto o PDF de la póliza de seguro del vehículo"
                      maxSize={2 * 1024 * 1024}
                      maxFiles={1}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end gap-4">
            <Button type="button" variant="outline" onClick={() => router.back()}>
              Cancelar
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Actualizando..." : "Guardar Cambios"}
            </Button>
          </div>
        </form>
      </ScrollArea>

    </Form>
  )
}






