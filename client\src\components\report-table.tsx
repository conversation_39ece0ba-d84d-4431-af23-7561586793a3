"use client"

import { Download, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

// Mock data
const reports = [
  {
    id: "RPT-1234",
    name: "Monthly Revenue Report",
    type: "Financial",
    date: "May 1, 2025",
    status: "Generated",
    size: "2.4 MB",
    format: "PDF",
  },
  {
    id: "RPT-1235",
    name: "User Growth Analysis",
    type: "Analytics",
    date: "April 30, 2025",
    status: "Generated",
    size: "1.8 MB",
    format: "XLSX",
  },
  {
    id: "RPT-1236",
    name: "Vehicle Performance Report",
    type: "Analytics",
    date: "April 15, 2025",
    status: "Generated",
    size: "3.2 MB",
    format: "PDF",
  },
  {
    id: "RPT-1237",
    name: "Host Earnings Summary",
    type: "Financial",
    date: "April 1, 2025",
    status: "Generated",
    size: "1.5 MB",
    format: "XLSX",
  },
]

export function ReportTable() {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Report ID</TableHead>
          <TableHead>Name</TableHead>
          <TableHead>Type</TableHead>
          <TableHead>Date</TableHead>
          <TableHead>Status</TableHead>
          <TableHead>Size</TableHead>
          <TableHead>Format</TableHead>
          <TableHead className="text-right">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {reports.map((report) => (
          <TableRow key={report.id}>
            <TableCell className="font-medium">{report.id}</TableCell>
            <TableCell>{report.name}</TableCell>
            <TableCell>
              <Badge
                variant="outline"
                className={
                  report.type === "Financial"
                    ? "bg-green-100 text-green-800 hover:bg-green-100"
                    : "bg-blue-100 text-blue-800 hover:bg-blue-100"
                }
              >
                {report.type}
              </Badge>
            </TableCell>
            <TableCell>{report.date}</TableCell>
            <TableCell>
              <Badge
                variant={report.status === "Generated" ? "outline" : "secondary"}
                className={
                  report.status === "Generated"
                    ? "bg-green-100 text-green-800 hover:bg-green-100"
                    : "bg-yellow-100 text-yellow-800 hover:bg-yellow-100"
                }
              >
                {report.status}
              </Badge>
            </TableCell>
            <TableCell>{report.size}</TableCell>
            <TableCell>{report.format}</TableCell>
            <TableCell className="text-right">
              <div className="flex justify-end gap-2">
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <Eye className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <Download className="h-4 w-4" />
                </Button>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem>
                      <Eye className="mr-2 h-4 w-4" />
                      <span>View Report</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Download className="mr-2 h-4 w-4" />
                      <span>Download</span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem>
                      <span>Share Report</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <span>Schedule Report</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}
