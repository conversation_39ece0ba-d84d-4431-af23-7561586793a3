import { Badge } from "@/components/ui/badge"

interface VehicleAmenitiesProps {
  amenities: string[]
}

export default function VehicleAmenities({ amenities }: VehicleAmenitiesProps) {
  // Mapeo de códigos de amenidades a nombres legibles
  const amenityLabels: Record<string, string> = {
    "bluetooth": "Bluetooth",
    "usb_ports": "Puertos USB",
    "heated_seats": "Asientos calefactados",
    "sunroof": "Techo solar",
    "gps": "GPS",
    "backup_camera": "Cámara de reversa",
    "cruise_control": "Control de crucero",
    "air_conditioning": "Aire acondicionado",
    "android_auto": "Android Auto",
    "apple_carplay": "Apple CarPlay",
    "blind_spot_monitoring": "Monitoreo de punto ciego",
    "keyless_entry": "Entrada sin llave",
    "push_button_start": "Encendido por botón",
    "third_row_seating": "Tercera fila de asientos",
    "wifi_hotspot": "Punto de acceso WiFi",
    "wireless_charging": "Carga inalámbrica",
  }

  if (!amenities || amenities.length === 0) {
    return (
      <div className="p-4 bg-gray-50 rounded-lg">
        <p className="text-gray-500 text-center">No hay comodidades registradas para este vehículo.</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <h3 className="text-xl font-semibold">Comodidades</h3>
      <div className="flex flex-wrap gap-2">
        {amenities.map((amenity) => (
          <Badge key={amenity} variant="secondary" className="px-3 py-1">
            {amenityLabels[amenity] || amenity}
          </Badge>
        ))}
      </div>
    </div>
  )
}