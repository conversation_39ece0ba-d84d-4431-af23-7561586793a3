"use client"

import { Eye, MoreHorizontal, MessageSquare, CheckCircle, AlertCircle } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

// Mock data
const tickets = [
  {
    id: "TKT-1234",
    subject: "Vehicle damage reported during rental",
    client: {
      name: "<PERSON>",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "EJ",
    },
    category: "Vehicle Damage",
    priority: "High",
    status: "Open",
    created: "May 15, 2025",
    lastUpdated: "2 hours ago",
    assignee: {
      name: "<PERSON>",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "AM",
    },
  },
  {
    id: "TKT-1235",
    subject: "Payment not received for completed rental",
    client: {
      name: "<PERSON>",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "DC",
    },
    category: "Payment Issue",
    priority: "Medium",
    status: "In Progress",
    created: "May 14, 2025",
    lastUpdated: "5 hours ago",
    assignee: {
      name: "Sarah Miller",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "SM",
    },
  },
  {
    id: "TKT-1236",
    subject: "Host not responding to booking request",
    client: {
      name: "Michael Thompson",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "MT",
    },
    category: "Host Issue",
    priority: "Medium",
    status: "Open",
    created: "May 14, 2025",
    lastUpdated: "1 day ago",
    assignee: {
      name: "Alex Morgan",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "AM",
    },
  },
  {
    id: "TKT-1237",
    subject: "Vehicle not as described in listing",
    client: {
      name: "Sophia Martinez",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "SM",
    },
    category: "Vehicle Issue",
    priority: "Low",
    status: "Resolved",
    created: "May 12, 2025",
    lastUpdated: "2 days ago",
    assignee: {
      name: "James Wilson",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "JW",
    },
  },
]

export function SupportTickets() {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Ticket ID</TableHead>
          <TableHead>Subject</TableHead>
          <TableHead>Client</TableHead>
          <TableHead>Category</TableHead>
          <TableHead>Priority</TableHead>
          <TableHead>Status</TableHead>
          <TableHead>Created</TableHead>
          <TableHead>Assignee</TableHead>
          <TableHead className="text-right">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {tickets.map((ticket) => (
          <TableRow key={ticket.id}>
            <TableCell className="font-medium">{ticket.id}</TableCell>
            <TableCell className="max-w-[200px] truncate">{ticket.subject}</TableCell>
            <TableCell>
              <div className="flex items-center gap-2">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={ticket.client.avatar || "/placeholder.svg"} alt={ticket.client.name} />
                  <AvatarFallback>{ticket.client.initials}</AvatarFallback>
                </Avatar>
                <span className="text-sm">{ticket.client.name}</span>
              </div>
            </TableCell>
            <TableCell>{ticket.category}</TableCell>
            <TableCell>
              <Badge
                variant={
                  ticket.priority === "High" || ticket.priority === "Urgent"
                    ? "destructive"
                    : ticket.priority === "Medium"
                      ? "secondary"
                      : "outline"
                }
                className={
                  ticket.priority === "High"
                    ? "bg-red-100 text-red-800 hover:bg-red-100"
                    : ticket.priority === "Medium"
                      ? "bg-yellow-100 text-yellow-800 hover:bg-yellow-100"
                      : "bg-gray-100 text-gray-800 hover:bg-gray-100"
                }
              >
                {ticket.priority}
              </Badge>
            </TableCell>
            <TableCell>
              <Badge
                variant={
                  ticket.status === "Open" ? "destructive" : ticket.status === "In Progress" ? "secondary" : "outline"
                }
                className={
                  ticket.status === "Open"
                    ? "bg-red-100 text-red-800 hover:bg-red-100"
                    : ticket.status === "In Progress"
                      ? "bg-blue-100 text-blue-800 hover:bg-blue-100"
                      : ticket.status === "Resolved"
                        ? "bg-green-100 text-green-800 hover:bg-green-100"
                        : "bg-gray-100 text-gray-800 hover:bg-gray-100"
                }
              >
                {ticket.status}
              </Badge>
            </TableCell>
            <TableCell>
              <div className="text-sm">
                {ticket.created}
                <div className="text-xs text-muted-foreground">Updated {ticket.lastUpdated}</div>
              </div>
            </TableCell>
            <TableCell>
              <div className="flex items-center gap-2">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={ticket.assignee.avatar || "/placeholder.svg"} alt={ticket.assignee.name} />
                  <AvatarFallback>{ticket.assignee.initials}</AvatarFallback>
                </Avatar>
                <span className="text-sm">{ticket.assignee.name}</span>
              </div>
            </TableCell>
            <TableCell className="text-right">
              <div className="flex justify-end gap-2">
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <Eye className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <MessageSquare className="h-4 w-4" />
                </Button>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem>
                      <Eye className="mr-2 h-4 w-4" />
                      <span>View Details</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <MessageSquare className="mr-2 h-4 w-4" />
                      <span>Reply</span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    {ticket.status !== "Resolved" && (
                      <DropdownMenuItem className="text-green-600">
                        <CheckCircle className="mr-2 h-4 w-4" />
                        <span>Mark as Resolved</span>
                      </DropdownMenuItem>
                    )}
                    {ticket.status !== "Open" && (
                      <DropdownMenuItem className="text-red-600">
                        <AlertCircle className="mr-2 h-4 w-4" />
                        <span>Reopen Ticket</span>
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuItem>
                      <span>Assign to...</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}
