
import * as React from 'react'
import { Tailwind, Section, Text, Link, But<PERSON> } from '@react-email/components'

// Crea un componente de React para el email de verificación el cual recibe el nombre de usuario y la url de verificación


export default function VerificationEmail({ name, url = 'http://localhost:3000' }: { name: string, url: string }) {
  return (
    <Tailwind>
      <Section className="flex justify-center items-center w-full min-h-screen font-sans ">
        <Section className="flex flex-col items-center w-76 rounded-2xl px-6 py-1 bg-gray-50 pb-8">
          <Text className="text-black text-2xl font-bold">
            Verificación de Email
          </Text>
          <Text className="text-gray-600 text-xs mb-8">
            Hola {name}, gracias por unirte a nuestra plataforma. Por favor, haz clic en el botón de abajo para verificar tu email.
          </Text>
          <Link href={url} className="text-white text-md bg-blue-500 hover:bg-blue-600 transition px-6 py-3 rounded-md text-center" >
            Verificar Email
          </Link>
        </Section>
      </Section>
    </Tailwind>
  )
}