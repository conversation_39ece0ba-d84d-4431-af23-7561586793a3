"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { User, <PERSON><PERSON><PERSON><PERSON><PERSON>, CheckCircle } from "lucide-react"
import { useUser } from '@/context/user-context'
import { userRolesApi } from '@/lib/api/user-roles.api'
import toast from 'react-hot-toast'

export function AdditionalRoleRequest() {
  const { user } = useUser()
  const [isLoading, setIsLoading] = useState(false)

  // Determinar qué rol puede solicitar
  const getAvailableRoleToRequest = () => {
    if (!user.availableUserTypes) return null

    const hasClient = user.availableUserTypes.includes('client')
    const hasHost = user.availableUserTypes.includes('host')

    if (!hasClient && user.userType !== 'client') return 'client'
    if (!hasHost && user.userType !== 'host') return 'host'

    return null // Ya tiene ambos roles
  }

  const roleToRequest = getAvailableRoleToRequest()

  // Si ya tiene ambos roles, no mostrar nada
  if (!roleToRequest) {
    return null
  }

  const handleRequestRole = async () => {
    if (!roleToRequest) return

    setIsLoading(true)
    try {
      const response = await userRolesApi.requestAdditionalRole(roleToRequest as 'client' | 'host')

      // Actualizar el contexto del usuario

      toast.success(response.message)

    } catch (error) {
      console.error('Error requesting additional role:', error)
      toast.error('Error al solicitar el rol adicional')
    } finally {
      setIsLoading(false)
    }
  }

  const getRoleInfo = (role: string) => {
    if (role === 'host') {
      return {
        title: 'Convertirse en Anfitrión',
        description: 'Renta tus vehículos y genera ingresos adicionales',
        icon: UserCheck,
        benefits: [
          'Publica tus vehículos en la plataforma',
          'Genera ingresos pasivos',
          'Controla tu disponibilidad',
          'Accede a herramientas de gestión'
        ]
      }
    } else {
      return {
        title: 'Convertirse en Cliente',
        description: 'Renta vehículos de otros usuarios',
        icon: User,
        benefits: [
          'Accede a miles de vehículos',
          'Reserva por horas o días',
          'Encuentra vehículos cerca de ti',
          'Califica tu experiencia'
        ]
      }
    }
  }

  const roleInfo = getRoleInfo(roleToRequest)
  const RoleIcon = roleInfo.icon

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-3">
          <RoleIcon className="h-6 w-6 text-primary" />
          <div>
            <CardTitle>{roleInfo.title}</CardTitle>
            <CardDescription>{roleInfo.description}</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Beneficios:</h4>
          <ul className="space-y-1">
            {roleInfo.benefits.map((benefit, index) => (
              <li key={index} className="flex items-center gap-2 text-sm text-muted-foreground">
                <CheckCircle className="h-4 w-4 text-green-500" />
                {benefit}
              </li>
            ))}
          </ul>
        </div>

        <div className="flex items-center justify-between pt-4 border-t">
          <div className="space-y-1">
            <p className="text-sm font-medium">Rol actual:</p>
            <Badge variant="outline" className="capitalize">
              {user.userType === 'host' ? 'Anfitrión' : 'Cliente'}
            </Badge>
          </div>

          <Button
            onClick={handleRequestRole}
            disabled={isLoading}
            className="min-w-[120px]"
          >
            {isLoading ? 'Solicitando...' : `Ser ${roleToRequest === 'host' ? 'Anfitrión' : 'Cliente'}`}
          </Button>
        </div>

        <div className="text-xs text-muted-foreground bg-muted/50 p-3 rounded-md">
          <strong>Nota:</strong> Al solicitar este rol adicional, podrás alternar entre ambos modos desde tu perfil.
          {roleToRequest === 'host' && ' Para acceder a todas las funciones de anfitrión, deberás completar el proceso de verificación.'}
        </div>
      </CardContent>
    </Card>
  )
}
