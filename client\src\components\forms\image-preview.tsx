import Image from "next/image"
import { X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"

interface ImagePreviewProps {
  url: string
  onRemove: () => void
}

export default function ImagePreview({ url, onRemove }: ImagePreviewProps) {
  return (
    <div className="relative group">
      <div className="relative h-40 w-full rounded-md overflow-hidden">
        <Image
          src={url}
          alt="Vehicle image"
          fill
          className="object-cover"
          sizes='
            (100vw - 4rem)
            (min-width: 640px) 50vw
            (min-width: 1024px) 33vw
            (min-width: 1280px) 25vw
          '
        />
      </div>
      <Button
        type="button"
        variant="destructive"
        size="icon"
        className="absolute top-2 right-2" // Quitado opacity-0 group-hover:opacity-100 transition-opacity
        onClick={onRemove}
      >
        <X className="h-4 w-4" />
      </Button>
    </div>
  )
}
