"use client"

import { useEffect, useState } from "react"
import { usePathname, useSearchParams } from "next/navigation"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis
} from "@/components/ui/pagination"

interface PaginationControlProps {
  currentPage: number
  totalPages: number
  baseUrl?: string
  className?: string
  onPageChange?: (page: number) => void
}

export function PaginationControl({
  currentPage,
  totalPages,
  baseUrl,
  className = "",
  onPageChange
}: PaginationControlProps) {
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const [windowWidth, setWindowWidth] = useState(
    typeof window !== 'undefined' ? window.innerWidth : 0
  )

  // Actualizar el ancho de la ventana cuando cambia el tamaño
  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth)
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', handleResize)
      return () => window.removeEventListener('resize', handleResize)
    }
  }, [])

  // Función para crear una nueva URL con parámetros de búsqueda actualizados
  const createPageUrl = (pageNumber: number) => {
    if (onPageChange) return "#" // Si se usa onPageChange, el href es solo un placeholder

    const params = new URLSearchParams(searchParams.toString())
    params.set('page', pageNumber.toString())

    if (baseUrl) {
      return `${baseUrl}?${params.toString()}`
    }

    return `${pathname}?${params.toString()}`
  }

  // Calcular cuántos números de página mostrar basado en el ancho de la ventana
  const getVisiblePageNumbers = () => {
    if (totalPages <= 1) return [1]

    let maxVisible = 1 // Mínimo siempre mostrar al menos 1

    if (windowWidth > 640) maxVisible = 3 // sm
    if (windowWidth > 768) maxVisible = 5 // md
    if (windowWidth > 1024) maxVisible = 7 // lg

    // Asegurarse de no mostrar más páginas de las que existen
    maxVisible = Math.min(maxVisible, totalPages)

    // Calcular el rango de páginas a mostrar
    let startPage = Math.max(1, currentPage - Math.floor(maxVisible / 2))
    const endPage = Math.min(totalPages, startPage + maxVisible - 1)

    // Ajustar si estamos cerca del final
    if (endPage - startPage + 1 < maxVisible) {
      startPage = Math.max(1, endPage - maxVisible + 1)
    }

    return Array.from({ length: endPage - startPage + 1 }, (_, i) => startPage + i)
  }

  const handleClick = (pageNumber: number, e: React.MouseEvent) => {
    if (onPageChange) {
      e.preventDefault()
      onPageChange(pageNumber)
    }
  }

  const visiblePages = getVisiblePageNumbers()
  const showLeftEllipsis = totalPages > 0 && visiblePages[0] > 1
  const showRightEllipsis = totalPages > 0 && visiblePages[visiblePages.length - 1] < totalPages

  if (totalPages <= 1) {
    return null
  }

  return (
    <Pagination className={className}>
      <PaginationContent>
        {currentPage > 1 && (
          <PaginationItem>
            <PaginationPrevious
              href={createPageUrl(currentPage - 1)}
              onClick={(e) => handleClick(currentPage - 1, e)}
            />
          </PaginationItem>
        )}

        {showLeftEllipsis && (
          <>
            <PaginationItem>
              <PaginationLink
                href={createPageUrl(1)}
                onClick={(e) => handleClick(1, e)}
              >
                1
              </PaginationLink>
            </PaginationItem>
            <PaginationItem>
              <PaginationEllipsis />
            </PaginationItem>
          </>
        )}

        {visiblePages.map((pageNumber) => (
          <PaginationItem key={pageNumber}>
            <PaginationLink
              href={createPageUrl(pageNumber)}
              isActive={pageNumber === currentPage}
              onClick={(e) => handleClick(pageNumber, e)}
            >
              {pageNumber}
            </PaginationLink>
          </PaginationItem>
        ))}

        {showRightEllipsis && (
          <>
            <PaginationItem>
              <PaginationEllipsis />
            </PaginationItem>
            <PaginationItem>
              <PaginationLink
                href={createPageUrl(totalPages)}
                onClick={(e) => handleClick(totalPages, e)}
              >
                {totalPages}
              </PaginationLink>
            </PaginationItem>
          </>
        )}

        {currentPage < totalPages && (
          <PaginationItem>
            <PaginationNext
              href={createPageUrl(currentPage + 1)}
              onClick={(e) => handleClick(currentPage + 1, e)}
            />
          </PaginationItem>
        )}
      </PaginationContent>
    </Pagination>
  )
}