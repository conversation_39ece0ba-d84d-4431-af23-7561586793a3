import { Elysia } from "elysia";
import { authMiddleware } from "@/app/middlewares/auth.middleware";
import { HttpException } from "@/exceptions/HttpExceptions";
import { getFileReferenceFromR2 } from '@/services/r2';
import etag from "etag";
import { fileCache } from '@/services/cache';

function generateETag(buffer: ArrayBuffer | Buffer): string {
  const buf = Buffer.isBuffer(buffer) ? buffer : Buffer.from(buffer);
  return etag(buf);
}

// Controlador para descargar archivos privados
export const filesController = new Elysia({ prefix: '/files' })
  .use(authMiddleware)
  .get('/download', async ({ query, user, request }) => {
    // Verificar que se proporcionó una key
    if (!query.key) {
      throw HttpException.BadRequest("File key is required");
    }

    const key = query.key as string;
    // console.log('Downloading file with key:', key);
    
    // Extraer el userId de la key (primer segmento)
    const pathParts = key.split('/');
    
    if (pathParts.length < 2) {
      throw HttpException.BadRequest("Invalid file key");
    }
    
    const keyUserId = pathParts[1];
    
    // Verificar si el usuario tiene permisos para acceder al archivo
    const isAdmin = user.role === 'admin';
    const isOwner = keyUserId === user.id;
    
    if (!isAdmin && !isOwner) {
      throw HttpException.Forbidden("You don't have permission to access this file");
    }

    // Crear una clave de caché única para este archivo
    const cacheKey = `file:${key}`;

    // Obtener headers de validación del cliente
    const ifNoneMatch = request.headers.get('If-None-Match');
    const ifModifiedSince = request.headers.get('If-Modified-Since');

    // Verificar si tenemos el archivo en caché
    const cachedFile = fileCache.get(cacheKey);

    if (cachedFile) {
      // console.log('File found in cache');

      // Verificar si el cliente ya tiene la versión más reciente
      if (ifNoneMatch && ifNoneMatch === cachedFile.etag) {
        // console.log('Client has current version (ETag match)');
        return new Response(null, {
          status: 304,
          headers: {
            'ETag': cachedFile.etag,
            'Cache-Control': 'public, max-age=31536000, immutable',
            'Last-Modified': cachedFile.lastModified
          }
        });
      }

      if (ifModifiedSince && new Date(ifModifiedSince) >= new Date(cachedFile.lastModified)) {
        console.log('Client has current version (Last-Modified match)');
        return new Response(null, {
          status: 304,
          headers: {
            'ETag': cachedFile.etag,
            'Cache-Control': 'public, max-age=31536000, immutable',
            'Last-Modified': cachedFile.lastModified
          }
        });
      }

      // El cliente no tiene la versión actual, devolver desde caché
      const fileName = key.split('/').pop() || 'download';
      const isImage = cachedFile.contentType.startsWith('image/');

      const headers: HeadersInit = {
        'Content-Type': cachedFile.contentType,
        'Content-Length': String(cachedFile.buffer.byteLength),
        'Cache-Control': 'public, max-age=31536000, immutable',
        'ETag': cachedFile.etag,
        'Last-Modified': cachedFile.lastModified,
        'Expires': new Date(Date.now() + 31536000000).toUTCString(), // 1 año en el futuro
        'Pragma': 'public'
      };

      if (!isImage) {
        headers['Content-Disposition'] = `attachment; filename="${fileName}"`;
      } else {
        headers['Content-Disposition'] = `inline; filename="${fileName}"`;
      }

      return new Response(cachedFile.buffer, {
        status: 200,
        headers
      });
    }

    // No está en caché, obtener de R2
    try {
      const fileRef = getFileReferenceFromR2({ path: key, isPublic: false });
      // console.log('Downloading file from R2', fileRef);
      const buffer = await fileRef.arrayBuffer();
      // console.log('File downloaded from R2', buffer.byteLength);

      // Extraer el nombre del archivo de la ruta
      const fileName = key.split('/').pop() || 'download';

      // Determinar el tipo de contenido
      const fileExtension = fileName.split('.').pop()?.toLowerCase();
      let contentType = 'application/octet-stream';
      let isImage = false;

      if (fileExtension) {
        switch (fileExtension) {
          case 'pdf':
            contentType = 'application/pdf';
            break;
          case 'jpg':
          case 'jpeg':
            contentType = 'image/jpeg';
            isImage = true;
            break;
          case 'png':
            contentType = 'image/png';
            isImage = true;
            break;
          case 'webp':
            contentType = 'image/webp';
            isImage = true;
            break;
          // Añadir más tipos según sea necesario
        }
      }

      // Generar ETag y Last-Modified
      const fileETag = generateETag(Buffer.from(buffer));
      const lastModified = new Date().toUTCString();

      // Guardar en caché
      fileCache.set(cacheKey, {
        buffer,
        etag: fileETag,
        contentType,
        lastModified
      });
      
      // Verificar si el cliente ya tiene la versión más reciente
      if (ifNoneMatch && ifNoneMatch === fileETag) {
        return new Response(null, {
          status: 304,
          headers: {
            'ETag': fileETag,
            'Cache-Control': 'public, max-age=31536000, immutable',
            'Last-Modified': lastModified
          }
        });
      }

      // Configurar headers
      const headers: HeadersInit = {
        'Content-Type': contentType,
        'Content-Length': String(buffer.byteLength),
        'Cache-Control': 'public, max-age=31536000, immutable',
        'ETag': fileETag,
        'Last-Modified': lastModified,
        'Expires': new Date(Date.now() + 31536000000).toUTCString(), // 1 año en el futuro
        'Pragma': 'public'
      };

      if (!isImage) {
        headers['Content-Disposition'] = `attachment; filename="${fileName}"`;
      } else {
        headers['Content-Disposition'] = `inline; filename="${fileName}"`;
      }

      return new Response(buffer, {
        status: 200,
        headers
      });
    } catch (error) {
      console.error('Error downloading file:', error);
      throw HttpException.InternalServerError("Error downloading file");
    }
  })
  // Endpoint para ver estadísticas del caché (solo para administradores)
  .get('/cache-stats', ({ user }) => {
    if (user.role !== 'admin') {
      throw HttpException.Forbidden("Only admins can view cache statistics");
    }

    const stats = fileCache.getStats();
    return {
      entries: stats.entries,
      totalSize: `${(stats.totalSize / 1024 / 1024).toFixed(2)} MB`,
      maxSize: `${(stats.maxSize / 1024 / 1024).toFixed(2)} MB`,
      usagePercentage: `${((stats.totalSize / stats.maxSize) * 100).toFixed(2)}%`
    };
  });

// // Controlador para administradores
// export const adminFilesController = new Elysia({ prefix: '/admin/files' })
//   .use(authMiddleware)
//   .derive(({ user }) => {
//     checkAdmin(user);
//   })
//   .get('/download', async ({ query }) => {
//     // Verificar que se proporcionó una key
//     if (!query.key) {
//       throw HttpException.BadRequest("File key is required");
//     }

//     const key = query.key as string;

//     try {
//       // Obtener la URL firmada del archivo de R2 (los administradores pueden acceder a cualquier archivo)
//       const fileUrl = await getFileUrlFromR2({ path: key, isPublic: false });

//       // Redirigir al cliente a la URL firmada
//       return new Response(null, {
//         status: 302,
//         headers: {
//           'Location': fileUrl
//         }
//       });
//     } catch (error) {
//       console.error('Error downloading file:', error);
//       throw HttpException.InternalServerError("Error downloading file");
//     }
//   });




