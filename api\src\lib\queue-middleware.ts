import { Queue } from 'bullmq';
import { scaleWorkersAccordingToQueue } from './auto-start-machines';

/**
 * Creates a Queue wrapper that automatically scales workers before adding jobs
 * @param queue The BullMQ queue to wrap
 * @returns A wrapped queue with auto-scaling functionality
 */
export function createAutoScalingQueue<
  DataType = any,
  ResultType = any,
  NameType extends string = string
>(queue: Queue<DataType, ResultType, NameType>): Queue<DataType, ResultType, NameType> {
  // Create a proxy object that intercepts the add method
  return new Proxy(queue, {
    get(target, prop) {
      if (prop === 'add') {
        return async function (
          name: any,
          data: any,
          opts?: any
        ) {
          // Call the original add method first
          const result = await target.add(name, data, opts);

          // Scale workers after adding the job
          await scaleWorkersAccordingToQueue(queue);

          return result;
        };
      }
      return target[prop as keyof typeof target];
    }
  });
}




