import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Star, MessageSquare, Download, RotateCcw } from "lucide-react"
import Image from "next/image"

const trips = [
  {
    id: "TRIP-001",
    vehicle: {
      name: "Toyota Camry 2023",
      image: "/placeholder.svg?height=60&width=80",
      type: "Sed<PERSON>",
    },
    host: "<PERSON>",
    startDate: "2023-12-15",
    endDate: "2023-12-18",
    duration: "3 días",
    total: "$240.00",
    rating: 5,
    location: "Centro, Ciudad",
  },
  {
    id: "TRIP-002",
    vehicle: {
      name: "Honda Civic 2022",
      image: "/placeholder.svg?height=60&width=80",
      type: "<PERSON><PERSON><PERSON>",
    },
    host: "<PERSON>",
    startDate: "2023-11-20",
    endDate: "2023-11-22",
    duration: "2 días",
    total: "$160.00",
    rating: 4,
    location: "Norte, Ciudad",
  },
  {
    id: "TRIP-003",
    vehicle: {
      name: "Mazda CX-5 2023",
      image: "/placeholder.svg?height=60&width=80",
      type: "SUV",
    },
    host: "Luis Garcia",
    startDate: "2023-10-10",
    endDate: "2023-10-15",
    duration: "5 días",
    total: "$325.00",
    rating: 5,
    location: "Sur, Ciudad",
  },
]

const renderStars = (rating: number) => {
  return (
    <div className="flex items-center gap-1">
      {[...Array(5)].map((_, i) => (
        <Star key={i} className={`h-4 w-4 ${i < rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"}`} />
      ))}
    </div>
  )
}

export function ClientHistoryTable() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Historial de Viajes</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Vehículo</TableHead>
              <TableHead>Anfitrión</TableHead>
              <TableHead>Fechas</TableHead>
              <TableHead>Duración</TableHead>
              <TableHead>Total</TableHead>
              <TableHead>Mi Calificación</TableHead>
              <TableHead className="text-right">Acciones</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {trips.map((trip) => (
              <TableRow key={trip.id}>
                <TableCell>
                  <div className="flex items-center gap-3">
                    <Image
                      src={trip.vehicle.image || "/placeholder.svg"}
                      alt={trip.vehicle.name}
                      width={80}
                      height={60}
                      className="rounded-md object-cover"
                    />
                    <div>
                      <p className="font-medium">{trip.vehicle.name}</p>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary" className="text-xs">
                          {trip.vehicle.type}
                        </Badge>
                        <span className="text-sm text-muted-foreground">{trip.location}</span>
                      </div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>{trip.host}</TableCell>
                <TableCell>
                  <div className="text-sm">
                    <div>{trip.startDate}</div>
                    <div className="text-muted-foreground">a {trip.endDate}</div>
                  </div>
                </TableCell>
                <TableCell>{trip.duration}</TableCell>
                <TableCell className="font-medium">{trip.total}</TableCell>
                <TableCell>{renderStars(trip.rating)}</TableCell>
                <TableCell className="text-right">
                  <div className="flex items-center justify-end gap-2">
                    <Button variant="ghost" size="sm" title="Contactar anfitrión">
                      <MessageSquare className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" title="Descargar factura">
                      <Download className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" title="Reservar de nuevo">
                      <RotateCcw className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}
