'use client';
import { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { apiService } from '@/services/api';
import { useUser } from '@/context/user-context';


export function UserTypeModal() {
  const [userType, setUserType] = useState<'client' | 'host'>('client');
  const { user } = useUser();
  const [loading, setLoading] = useState(false);

  const handleSetUserType = async (userType: 'client' | 'host') => {
    try {
      const response = await apiService.post('/user/set-user-type', { userType, userId: user?.id });
      window.location.reload();
      return response.data;
    } catch (error) {
      toast.error("Error al guardar el tipo de usuario");
      console.error(error);
    }
  };


  const handleSubmit = async () => {
    setLoading(true);
    try {
      // await onSubmit(userType);
      await handleSetUserType(userType);
      // No cerramos el modal aquí, se cerrará cuando el estado del usuario se actualice
    } catch (error) {
      toast.error("Error al guardar el tipo de usuario");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={!user?.userType} onOpenChange={() => { }}>
      <DialogContent className="sm:max-w-[425px]" onInteractOutside={(e) => e.preventDefault()}>
        <DialogHeader>
          <DialogTitle>¿Cómo deseas usar nuestra plataforma?</DialogTitle>
          <DialogDescription>
            Selecciona tu rol principal en la plataforma. Podrás cambiar esto más adelante.
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <RadioGroup value={userType} onValueChange={(value) => setUserType(value as 'client' | 'host')}>
            <div className="flex items-center space-x-2 mb-3">
              <RadioGroupItem value="client" id="client" />
              <Label htmlFor="client">Cliente (Quiero rentar autos)</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="host" id="host" />
              <Label htmlFor="host">Anfitrión (Quiero ofrecer mis autos)</Label>
            </div>
          </RadioGroup>
        </div>
        <DialogFooter>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading ? "Guardando..." : "Continuar"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
