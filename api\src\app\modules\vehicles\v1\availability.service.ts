import { prisma } from "@/lib/prisma";
import { HttpException } from "@/exceptions/HttpExceptions";

export class VehicleAvailabilityService {
  // Obtener la configuración de disponibilidad de un vehículo
  static async getByVehicleId(vehicleId: string) {
    // Verificar que el vehículo existe
    const vehicle = await prisma.vehicle.findUnique({
      where: { id: vehicleId }
    });

    if (!vehicle) {
      throw HttpException.NotFound("Vehículo no encontrado");
    }

    // Obtener o crear la configuración de disponibilidad
    let availability = await prisma.vehicleAvailability.findUnique({
      where: { vehicleId }
    });
    // console.log('Availability:', availability);

    if (!availability) {
      // Crear configuración por defecto si no existe
      availability = await prisma.vehicleAvailability.create({
        data: {
          vehicleId,
          defaultCheckInTime: "14:00",
          defaultCheckOutTime: "12:00",
          minimumRentalNights: 3,
          maximumRentalNights: 30,
          advanceBookingPeriod: 90,
          instantBooking: true,
          allowSameDayBooking: false,
          cancellationPolicy: "flexible",
          customAvailableDays: false,
          mondayAvailable: true,
          tuesdayAvailable: true,
          wednesdayAvailable: true,
          thursdayAvailable: true,
          fridayAvailable: true,
          saturdayAvailable: true,
          sundayAvailable: true,
        }
      });
    }

    // console.log('Availability 2:', availability);
    return availability;
  }

  // Actualizar la configuración de disponibilidad
  static async update(vehicleId: string, data: any) {
    // Verificar que el vehículo existe
    const vehicle = await prisma.vehicle.findUnique({
      where: { id: vehicleId }
    });

    if (!vehicle) {
      throw HttpException.NotFound("Vehículo no encontrado");
    }

    // Verificar si ya existe una configuración
    const existingConfig = await prisma.vehicleAvailability.findUnique({
      where: { vehicleId }
    });

    if (existingConfig) {
      // Actualizar configuración existente
      return await prisma.vehicleAvailability.update({
        where: { vehicleId },
        data
      });
    } else {
      // Crear nueva configuración
      return await prisma.vehicleAvailability.create({
        data: {
          vehicleId,
          ...data
        }
      });
    }
  }
}
