import { VehicleForm } from "@/components/host/vehicle-form"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function NewVehiclePage() {
  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <div className="flex flex-col space-y-2">
        <h1 className="text-2xl font-bold">Añadir Nuevo Vehículo</h1>
        <p className="text-muted-foreground">
          Completa la información de tu vehículo para comenzar a generar ingresos.
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Información del Vehículo</CardTitle>
          <CardDescription>
            Proporciona todos los detalles necesarios para que los clientes puedan encontrar y reservar tu vehículo.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <VehicleForm />
        </CardContent>
      </Card>
    </div>
  )
}
