import { BEARER_COOKIE_NAME } from '@/constants';
import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { setCookie } from 'cookies-next/server';

export const runtime = 'edge';

export async function POST(request: Request) {
  // console.log('request: ', request);
  const body = await request.json();
  console.log('body: ', body);
  const url = body.url;
  const token = body.token;
  if (token) {
    // const res = new Response();
    // res.headers.set('Set-Cookie', `better-auth.session_token=${token}; Path=/; HttpOnly; Secure; SameSite=None`);
    // // set the BEARER_COOKIE_NAME
    // res.headers.set('Set-Cookie', `bearer_token=${token}; Path=/; Secure; SameSite=None`);
    // return res;

    await setCookie(BEARER_COOKIE_NAME, token, { cookies, httpOnly: false, secure: true, sameSite: 'none', path: '/' });

    const newUrlWithUrlQuery = new URL(url);
    // newUrlWithUrlQuery.searchParams.set('url', url);
    newUrlWithUrlQuery.searchParams.set('token', token);
    newUrlWithUrlQuery.searchParams.set('tcl', token);
    const redirectUrl = newUrlWithUrlQuery.toString()
    console.log('redirectUrl: ', redirectUrl);
    // return NextResponse.redirect(redirectUrl, {
    //   headers: {
    //     'Set-Cookie': `${BEARER_COOKIE_NAME}=${token}`
    //   }
    // });

    // const enviorments = process.env;
    // console.log('enviorments: ', enviorments);

    await setCookie(BEARER_COOKIE_NAME, token, { cookies, httpOnly: false, secure: true, sameSite: 'none', path: '/' });
    console.log('BEARER_COOKIE_NAME: ', BEARER_COOKIE_NAME);
    // return NextResponse.json({ message: 'Token set' });
    // tcl



    return NextResponse.redirect(redirectUrl, {
      headers: {
        'Set-Cookie': `${BEARER_COOKIE_NAME}=${token}`
      }
    });

  }
  return new Response('No token provided', { status: 400 });
}