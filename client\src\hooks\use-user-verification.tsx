'use client';
import { useRouter } from 'next/navigation';
import { useUser } from '@/context/user-context';
import toast from 'react-hot-toast';

export function useUserVerification() {
  const router = useRouter();
  const { user } = useUser();

  // Verificar si el usuario está verificado directamente desde la sesión
  const isVerified = user?.isHostVerified || false;


  // Verificar si el usuario puede realizar acciones
  const checkVerification = ({ showToast = true }: { showToast?: boolean } = {}) => {

    if (isVerified) {
      return true;
    }

    if (showToast) {
      toast((t) => (
        <div className="flex flex-col space-y-2">
          <p className="font-bold">Verificación requerida</p>
          <p className="text-muted-foreground">Necesitas verificar tu identidad antes de poder registrar vehículos</p>
          <div>

            <button
              className="underline text-center"
              onClick={() => {
                router.push('/dashboard/host/verification');
                toast.dismiss(t.id);
              }}
            >
              Verificar ahora
            </button>
          </div>
        </div>
      ));
    }

    return false;
  };

  return {
    isVerified,
    // verificationDetails,
    // isLoading,
    checkVerification,
  };
}
