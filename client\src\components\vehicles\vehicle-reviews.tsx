import { useState } from "react"
import { useQuery } from "@tanstack/react-query"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Star } from "lucide-react"
import { formatDistanceToNow } from "date-fns"
import { es } from "date-fns/locale"
import { reviewsApi } from "@/lib/api/reviews.api"

interface VehicleReviewsProps {
  vehicleId: string
  averageRating: number
  totalReviews: number
}

export default function VehicleReviews({ vehicleId, averageRating, totalReviews }: VehicleReviewsProps) {
  const [page, setPage] = useState(1)
  const pageSize = 5

  // Obtener reseñas del vehículo
  const { data: reviews, isLoading } = useQuery({
    queryKey: ['vehicle-reviews', vehicleId, page],
    queryFn: () => reviewsApi.public.getVehicleReviews(vehicleId, {
      page,
      limit: pageSize
    }),
    enabled: totalReviews > 0
  })

  console.log('reviews', reviews)

  if (totalReviews === 0) {
    return (
      <div className="p-6 bg-gray-50 rounded-lg text-center">
        <h3 className="font-semibold mb-2">Sin reseñas aún</h3>
        <p className="text-gray-500">Este vehículo aún no tiene reseñas.</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <div className="flex items-center">
          <Star className="h-5 w-5 text-yellow-500 mr-1" />
          <span className="font-semibold">{averageRating.toFixed(1)}</span>
        </div>
        <span className="text-gray-400">•</span>
        <span>{totalReviews} {totalReviews === 1 ? 'reseña' : 'reseñas'}</span>
      </div>

      {isLoading ? (
        <div className="space-y-6">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="flex space-x-4">
              <Skeleton className="h-10 w-10 rounded-full" />
              <div className="space-y-2 flex-1">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-16 w-full" />
              </div>
            </div>
          ))}
        </div>
      ) : (
        <>
          <div className="space-y-6">
            {reviews?.data.map((review) => (
              <div key={review.id} className="space-y-2">
                <div className="flex items-center space-x-3">
                  <Avatar>
                    <AvatarImage src={review.user.image || undefined} />
                    <AvatarFallback>{review.user.name.substring(0, 2).toUpperCase()}</AvatarFallback>
                  </Avatar>
                  <div>
                    <h4 className="font-medium">{review.user.name}</h4>
                    <p className="text-sm text-gray-500">
                      {formatDistanceToNow(new Date(review.createdAt), { addSuffix: true, locale: es })}
                    </p>
                  </div>
                </div>
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-4 w-4 ${i < review.rating ? 'text-yellow-500' : 'text-gray-300'}`}
                      fill={i < review.rating ? 'currentColor' : 'none'}
                    />
                  ))}
                </div>
                {review.comment && (
                  <p className="text-gray-700">{review.comment}</p>
                )}
              </div>
            ))}
          </div>

            {reviews && reviews.pagination.totalPages > 1 && (
            <div className="flex justify-center mt-6">
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  disabled={page === 1}
                  onClick={() => setPage(p => Math.max(1, p - 1))}
                >
                  Anterior
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                    disabled={page === reviews.pagination.totalPages}
                    onClick={() => setPage(p => Math.min(reviews.pagination.totalPages, p + 1))}
                >
                  Siguiente
                </Button>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  )
}