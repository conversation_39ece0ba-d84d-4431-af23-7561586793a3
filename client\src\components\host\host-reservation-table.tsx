"use client"

import { useState } from "react"
import { useQuery } from "@tanstack/react-query"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { MoreHorizontal, Eye } from "lucide-react"
import { format } from "date-fns"
import { es } from "date-fns/locale"
import { reservationsApi } from "@/lib/api/reservations.api"
import { Skeleton } from "@/components/ui/skeleton"
import { useRouter } from "next/navigation"

export function HostReservationTable() {
  const router = useRouter()
  const [page, setPage] = useState(1)
  
  const { data: reservations, isLoading } = useQuery({
    queryKey: ['host-reservations'],
    queryFn: () => reservationsApi.host.getReservations({ page, limit: 10 }),
    staleTime: 60 * 1000, // 1 minuto
  })

  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return <Badge className="bg-green-500">Confirmada</Badge>
      case 'pending':
        return <Badge className="bg-blue-500">Pendiente</Badge>
      case 'completed':
        return <Badge className="bg-gray-500">Completada</Badge>
      case 'cancelled':
        return <Badge className="bg-red-500">Cancelada</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "d MMM yyyy", { locale: es })
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-96 w-full" />
      </div>
    )
  }

  if (!reservations || reservations.length === 0) {
    return (
      <div className="text-center p-8 border rounded-lg">
        <h3 className="text-lg font-medium">No hay reservas</h3>
        <p className="text-muted-foreground mt-2">Aún no tienes reservas para tus vehículos.</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Cliente</TableHead>
              <TableHead>Contacto</TableHead>
              <TableHead>Vehículo</TableHead>
              <TableHead>Fechas</TableHead>
              <TableHead>Estado</TableHead>
              <TableHead>Total</TableHead>
              <TableHead className="text-right">Acciones</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {reservations.map((reservation) => {
              console.log('reservation', reservation)
              return (
              <TableRow key={reservation.id}>
                <TableCell>
                  <div className="font-medium">{reservation.user.name}</div>
                    {/* <div className="text-sm text-muted-foreground">{reservation.user.email}</div> */}
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">{reservation.contactEmail}</div>
                    <div className="text-sm text-muted-foreground">{reservation.contactPhone}</div>
                </TableCell>
                <TableCell>
                  <div className="font-medium">{reservation.vehicle.make} {reservation.vehicle.model}</div>
                  <div className="text-sm text-muted-foreground">{reservation.vehicle.year}</div>
                </TableCell>
                <TableCell>
                    <div>{formatDate(reservation.startDate)} - {formatDate(reservation.endDate)}</div>
                </TableCell>
                <TableCell>
                  {getStatusBadge(reservation.status)}
                </TableCell>
                <TableCell>${reservation.totalPrice}</TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Abrir menú</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => router.push(`/dashboard/host/reservations/${reservation.id}`)}>
                        <Eye className="mr-2 h-4 w-4" />
                        Ver detalles
                      </DropdownMenuItem>
                      {reservation.status.toLowerCase() === 'pending' && (
                        <DropdownMenuItem onClick={() => {
                          // Implementar lógica para confirmar reserva
                            reservationsApi.host.updateReservationStatus(reservation.id, 'confirmed')
                            .then(() => router.refresh())
                        }}>
                          Confirmar
                        </DropdownMenuItem>
                      )}
                      {(reservation.status.toLowerCase() === 'pending' || reservation.status.toLowerCase() === 'confirmed') && (
                        <DropdownMenuItem onClick={() => {
                          // Implementar lógica para cancelar reserva
                            reservationsApi.host.cancelReservation(reservation.id)
                            .then(() => router.refresh())
                        }}>
                          Cancelar
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
              )
            })}
          </TableBody>
        </Table>
      </div>

      <div className="flex items-center justify-end space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setPage(page - 1)}
          disabled={page === 1}
        >
          Anterior
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setPage(page + 1)}
          disabled={reservations.length < 10} // Asumiendo 10 por página
        >
          Siguiente
        </Button>
      </div>
    </div>
  )
}
