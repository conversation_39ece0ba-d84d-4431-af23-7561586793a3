import { authMiddleware } from '@/app/middlewares/auth.middleware';
import { checkAdmin } from '@/lib/check-admin';
import Elysia, { t } from 'elysia';
import { UsersService } from './users.service';

// Controlador para administradores
export const adminUsersController = new Elysia({ prefix: '/admin/users' })
  .use(authMiddleware)
  .derive(({ user }) => {
    checkAdmin(user);
  })
  .get('/hosts', async ({ query }) => {
    return await UsersService.getAllHosts({
      query: {
        page: query.page!,
        limit: query.limit!
      }
    });
  }, {
    query: t.Optional(t.Object({
      page: t.Number({ default: 1 }),
      limit: t.Number({ default: 10, min: 1, max: 50 }),
    }))
  })
  .get('/clients', async ({ query }) => {
    return await UsersService.getAllClients({
      query: {
        page: query.page!,
        limit: query.limit!
      }
    });
  }, {
    query: t.Optional(t.Object({
      page: t.Number({ default: 1 }),
      limit: t.Number({ default: 10, min: 1, max: 50 }),
    }))
  })
  .patch('/:id/verify', async ({ params }) => {
    return await UsersService.verifyUser(params.id);
  })
// .patch('/:id/block', async ({ params }) => {
//   return await UsersService.blockUser(params.id);
// });