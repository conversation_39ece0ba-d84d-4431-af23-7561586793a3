"use client"

import { useState } from "react"
import { DateRange, type RangeKeyDict } from "react-date-range"
import { addDays, format, isBefore, isEqual } from "date-fns"
import { es } from "date-fns/locale"
import "react-date-range/dist/styles.css"
import "react-date-range/dist/theme/default.css"

interface DateRangePickerProps {
  className?: string
  unavailableDates?: string[] // ISO date strings
  onChange?: (range: { startDate: Date; endDate: Date }) => void
  initialDateRange?: { startDate: Date | null; endDate: Date | null }
}

export default function DateRangePicker({
  className,
  unavailableDates = [],
  onChange,
  initialDateRange,
}: DateRangePickerProps) {
  const [state, setState] = useState<RangeKeyDict['selection'][]>([
    {
      startDate: initialDateRange?.startDate || new Date(),
      endDate: initialDateRange?.endDate || addDays(new Date(), 7),
      key: "selection",
    },
  ])

  // Convert ISO strings to Date objects
  const disabledDates = unavailableDates.map((dateStr) => new Date(dateStr))

  // Function to check if a date is disabled
  const isDateDisabled = (date: Date) => {
    // Disable dates before today
    if (isBefore(date, new Date())) {
      return true
    }

    // Disable specific unavailable dates
    return disabledDates.some((disabledDate) => 
      isEqual(
        new Date(date.getFullYear(), date.getMonth(), date.getDate()),
        new Date(
          disabledDate.getFullYear(), 
          disabledDate.getMonth(), 
          disabledDate.getDate()
        )
      )
    )
  }

  const handleSelect = (ranges: RangeKeyDict) => {
    const selection = ranges.selection
    setState([selection])

    if (onChange && selection.startDate && selection.endDate) {
      onChange({
        startDate: selection.startDate,
        endDate: selection.endDate,
      })
    }
  }

  return (
    <div className={`${className} border rounded-md overflow-hidden`}>
      <div className="p-2 border-b flex items-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="mr-2 text-gray-500"
        >
          <rect width="18" height="18" x="3" y="4" rx="2" ry="2" />
          <line x1="16" x2="16" y1="2" y2="6" />
          <line x1="8" x2="8" y1="2" y2="6" />
          <line x1="3" x2="21" y1="10" y2="10" />
        </svg>
        <span>
          {state[0].startDate && state[0].endDate ? (
            `${format(state[0].startDate, "MMM dd, yyyy")} - ${format(state[0].endDate, "MMM dd, yyyy")}`
          ) : (
            "Select dates"
          )}
        </span>
      </div>
      <div className="hidden">
        <DateRange
          editableDateInputs={true}
          onChange={handleSelect}
          moveRangeOnFirstSelection={false}
          ranges={state}
          months={2}
          direction="horizontal"
          locale={es}
          disabledDay={isDateDisabled}
          minDate={new Date()}
          rangeColors={["#1a2b5e"]}
        />
      </div>
    </div>
  )
}
