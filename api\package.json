{"name": "autoop-api", "version": "1.0.50", "scripts": {"workers": "NODE_ENV=production ./worker", "workers:dev": "bun run --watch src/workers/index.ts", "test": "echo \"Error: no test specified\" && exit 1", "dev": "NODE_ENV=development bun run --watch src/index.ts", "prod": "NODE_ENV=production bun run src/index.ts", "wait-prisma": "wait-on ./node_modules/.prisma/client/query_engine-windows.dll.node", "dev2": "concurrently -P \"bun prisma-watch.js -- {@}\" \"wait-on ./node_modules/.prisma/client/query_engine-windows.dll.node&& bun --watch src/index.ts -- {@}\" --", "build": "NODE_ENV=production bun build --compile --minify-whitespace --minify-syntax --target bun --outfile server ./src/index.ts", "build:workers": "NODE_ENV=production bun build --compile --minify-whitespace --minify-syntax --target bun --outfile worker ./src/workers/index.ts", "build:prod": "NODE_ENV=production bun build --compile --minify-whitespace --minify-syntax --target bun --outfile server ./src/index.ts", "build:dev": "NODE_ENV=development bun build --compile --minify-whitespace --minify-syntax --target bun --outfile server ./src/index.ts", "build:js": "bun build src/index.ts --outdir ./build", "build:watch": "bun build src/index.ts --outdir ./build --watch", "server": "NODE_ENV=production ./server", "server:dev": "NODE_ENV=development ./server", "email": "email dev --dir src/emails --port 3001"}, "dependencies": {"@aws-sdk/client-cloudwatch-logs": "^3.797.0", "@aws-sdk/client-lambda": "^3.797.0", "@aws-sdk/client-s3": "^3.832.0", "@aws-sdk/client-scheduler": "^3.797.0", "@aws-sdk/client-sqs": "^3.797.0", "@aws-sdk/s3-request-presigner": "^3.832.0", "@elysiajs/cookie": "^0.8.0", "@elysiajs/cors": "^1.2.0", "@elysiajs/jwt": "^1.2.0", "@elysiajs/opentelemetry": "^1.2.0", "@elysiajs/swagger": "^1.2.2", "@opentelemetry/exporter-trace-otlp-proto": "0.45.0", "@opentelemetry/sdk-trace-node": "1.9.0", "@prisma/client": "^6.5.0", "@react-email/components": "^0.1.1", "aws-sdk": "^2.1692.0", "better-auth": "^1.2.9", "better-auth-nile": "^0.4.0", "bullmq": "^5.52.1", "chalk": "^5.4.1", "elysia": "^1.2.14", "etag": "^1.8.1", "ioredis": "^5.6.1", "luxon": "^3.6.1", "nodemailer": "^6.10.0", "prisma": "^6.5.0", "react": "^19.1.0", "react-dom": "^19.1.0", "resend": "^4.6.0", "stripe": "^18.2.1"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/etag": "^1.8.4", "@types/luxon": "^3.6.2", "@types/nodemailer": "^6.4.17", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "bun-types": "latest", "chokidar": "^4.0.3", "concurrently": "^9.1.2", "react-email": "^4.0.17", "wait-on": "^8.0.2"}, "module": "src/index.js", "optionalDependencies": {"sharp": "^0.34.2"}}