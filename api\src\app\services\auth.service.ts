import { prisma } from '@/lib/prisma';
import { IpQueryService } from './ipquery.service';

export class AuthService {
  private ipQueryService = new IpQueryService();
  
  // Método para registrar un nuevo usuario
  async register(userData: any, ip?: string) {
    // Crear el usuario básico
    const user = await prisma.user.create({
      data: {
        name: userData.name,
        email: userData.email,
        password: userData.hashedPassword,
        // Otros campos existentes
      }
    });
    
    // Obtener información de IP si está disponible
    if (ip) {
      try {
        const ipInfo = await this.ipQueryService.getIpInfo(ip);
        
        // Actualizar usuario con información de ubicación
        await prisma.user.update({
          where: { id: user.id },
          data: {
            country: ipInfo.location.country,
            state: ipInfo.location.state,
            timezone: ipInfo.location.timezone,
          }
        });
        
        // Crear registro de información detallada
        await prisma.userInfo.create({
          data: {
            userId: user.id,
            ip: ipInfo.ip,
            city: ipInfo.location.city,
            zipcode: ipInfo.location.zipcode,
            latitude: ipInfo.location.latitude,
            longitude: ipInfo.location.longitude,
            isMobile: ipInfo.risk.is_mobile,
            isVpn: ipInfo.risk.is_vpn,
            isTor: ipInfo.risk.is_tor,
            isProxy: ipInfo.risk.is_proxy,
            isDatacenter: ipInfo.risk.is_datacenter,
            riskScore: ipInfo.risk.risk_score,
          }
        });
      } catch (error) {
        console.error('Error getting IP info during registration:', error);
        // Continuar con el registro incluso si falla la obtención de IP
      }
    }
    
    return user;
  }
  
  // Método para iniciar sesión
  async login(credentials: any, ip?: string) {
    // Lógica de inicio de sesión existente
    
    // Si hay una IP disponible, actualizar la información de ubicación
    if (ip && user) {
      try {
        const ipInfo = await this.ipQueryService.getIpInfo(ip);
        
        // Actualizar usuario con información de ubicación
        await prisma.user.update({
          where: { id: user.id },
          data: {
            country: ipInfo.location.country,
            state: ipInfo.location.state,
            timezone: ipInfo.location.timezone,
          }
        });
        
        // Actualizar o crear registro de información detallada
        await prisma.userInfo.upsert({
          where: { userId: user.id },
          update: {
            ip: ipInfo.ip,
            city: ipInfo.location.city,
            zipcode: ipInfo.location.zipcode,
            latitude: ipInfo.location.latitude,
            longitude: ipInfo.location.longitude,
            isMobile: ipInfo.risk.is_mobile,
            isVpn: ipInfo.risk.is_vpn,
            isTor: ipInfo.risk.is_tor,
            isProxy: ipInfo.risk.is_proxy,
            isDatacenter: ipInfo.risk.is_datacenter,
            riskScore: ipInfo.risk.risk_score,
          },
          create: {
            userId: user.id,
            ip: ipInfo.ip,
            city: ipInfo.location.city,
            zipcode: ipInfo.location.zipcode,
            latitude: ipInfo.location.latitude,
            longitude: ipInfo.location.longitude,
            isMobile: ipInfo.risk.is_mobile,
            isVpn: ipInfo.risk.is_vpn,
            isTor: ipInfo.risk.is_tor,
            isProxy: ipInfo.risk.is_proxy,
            isDatacenter: ipInfo.risk.is_datacenter,
            riskScore: ipInfo.risk.risk_score,
          }
        });
      } catch (error) {
        console.error('Error getting IP info during login:', error);
        // Continuar con el inicio de sesión incluso si falla la obtención de IP
      }
    }
    
    return user;
  }
}