import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

const activities = [
  {
    id: 1,
    type: "reservation_confirmed",
    title: "Reserva confirmada",
    description: "Tesla Model 3 - 3 días",
    time: "Hace 2 horas",
    status: "success",
  },
  {
    id: 2,
    type: "payment_completed",
    title: "Pago procesado",
    description: "$360.00 - Reserva #1234",
    time: "Hace 1 día",
    status: "success",
  },
  {
    id: 3,
    type: "review_submitted",
    title: "Reseña enviada",
    description: "BMW X5 - 5 estrellas",
    time: "Hace 3 días",
    status: "info",
  },
  {
    id: 4,
    type: "reservation_completed",
    title: "Reserva completada",
    description: "Mercedes E-Class - 2 días",
    time: "Hace 1 semana",
    status: "completed",
  },
]

export function ClientRecentActivity() {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-md font-medium">Actividad Reciente</CardTitle>
        <Button variant="ghost" size="sm" asChild>
          <Link href="/dashboard/client/history">Ver Todo</Link>
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => (
            <div key={activity.id} className="flex items-start gap-4">
              <div className="h-2 w-2 rounded-full bg-primary mt-2" />
              <div className="flex-1 space-y-1">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium">{activity.title}</p>
                  <Badge
                    variant={
                      activity.status === "success" ? "default" : activity.status === "info" ? "secondary" : "outline"
                    }
                    className="text-xs"
                  >
                    {activity.status === "success" ? "Exitoso" : activity.status === "info" ? "Info" : "Completado"}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">{activity.description}</p>
                <p className="text-xs text-muted-foreground">{activity.time}</p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
