'use client';
import { Session } from 'better-auth/types';
import { createContext, useContext, useEffect, useState } from 'react';


interface UserContextType {
  user: User;
  setUser: React.Dispatch<React.SetStateAction<User>>;
  session: Session;
  setSession: React.Dispatch<React.SetStateAction<any>>;
}

export const UserContext = createContext<UserContextType | undefined>(undefined);

export function UserProvider({ children, session: sessionData }: { children: React.ReactNode; session: { user: User; session: Session } }) {

  // console.log('sessionData: ', sessionData.user);
  const [session, setSession] = useState(sessionData?.session);
  const [user, setUser] = useState(sessionData?.user);

  useEffect(() => {
    if (sessionData) {
      setUser(sessionData.user);
      setSession(sessionData.session);
    }
  }, [sessionData]);

  return (
    <UserContext.Provider value={{ user, setUser, session, setSession }}>
      {children}
    </UserContext.Provider>
  );
}


export function useUser() {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
}