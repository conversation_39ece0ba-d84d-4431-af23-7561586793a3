import { Worker, Job } from 'bullmq';
import { ReviewNotificationService, ReviewNotificationData } from '../services/review-notification.service';
import { sendReviewNotificationEmail } from '../emails/review-notification.email';

// Configuración de Redis para el worker
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  maxRetriesPerRequest: null, // Requerido por BullMQ
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  maxLoadingTimeout: 1000,
};

// Worker para procesar notificaciones de reseñas
export const reviewNotificationWorker = new Worker(
  'review-notifications',
  async (job: Job) => {
    console.log(`🔄 Procesando job: ${job.name} (ID: ${job.id})`);

    try {
      switch (job.name) {
        case 'daily-reservation-completion':
          await processDailyReservationCompletion(job);
          break;

        case 'daily-review-check':
          await processDailyReviewCheck(job);
          break;

        case 'send-review-notification':
          await processSendReviewNotification(job);
          break;

        default:
          console.warn(`⚠️ Tipo de job desconocido: ${job.name}`);
          throw new Error(`Tipo de job desconocido: ${job.name}`);
      }

      console.log(`✅ Job completado: ${job.name} (ID: ${job.id})`);
    } catch (error) {
      console.error(`❌ Error procesando job ${job.name} (ID: ${job.id}):`, error);
      throw error;
    }
  },
  {
    connection: redisConfig,
    concurrency: 5, // Procesar hasta 5 jobs simultáneamente
    removeOnComplete: { age: 3600, count: 100 }, // Remove jobs older than 1 hour or keep max 100
    removeOnFail: { age: 3600, count: 50 },      // Remove failed jobs older than 1 hour or keep max 50
  }
);

/**
 * Procesa el job diario de finalización de reservas
 */
async function processDailyReservationCompletion(job: Job): Promise<void> {
  console.log('📅 Iniciando finalización diaria de reservas...');

  try {
    // Buscar reservas que deben completarse
    const completedReservations = await ReviewNotificationService.completeExpiredReservations();

    // Actualizar progreso del job
    await job.updateProgress(100);

    console.log(`✅ Finalización diaria completada. ${completedReservations} reservas marcadas como completadas.`);
  } catch (error) {
    console.error('❌ Error en finalización diaria:', error);
    throw error;
  }
}

/**
 * Procesa el job diario de verificación de reseñas
 */
async function processDailyReviewCheck(job: Job): Promise<void> {
  console.log('📅 Iniciando verificación diaria de reseñas...');

  try {
    // Buscar reservas elegibles
    const eligibleReservations = await ReviewNotificationService.findEligibleReservations();

    if (eligibleReservations.length === 0) {
      console.log('ℹ️ No se encontraron reservas elegibles para notificación');
      return;
    }

    // Programar notificaciones individuales
    await ReviewNotificationService.scheduleNotifications(eligibleReservations);

    // Actualizar progreso del job
    await job.updateProgress(100);

    console.log(`✅ Verificación diaria completada. ${eligibleReservations.length} notificaciones programadas.`);
  } catch (error) {
    console.error('❌ Error en verificación diaria:', error);
    throw error;
  }
}

/**
 * Procesa el envío de una notificación individual
 */
async function processSendReviewNotification(job: Job<ReviewNotificationData>): Promise<void> {
  const data = job.data;
  console.log(`📧 Enviando notificación de reseña para reserva ${data.reservationId}`);

  try {
    // Verificar que la reserva siga siendo elegible
    const eligibility = await ReviewNotificationService.isReservationEligibleForReview(
      data.reservationId,
      data.userId
    );

    if (!eligibility.eligible) {
      console.log(`⚠️ Reserva ${data.reservationId} ya no es elegible: ${eligibility.reason}`);
      return;
    }

    // Generar token para el link de reseña
    const reviewToken = ReviewNotificationService.generateReviewToken(
      data.reservationId,
      data.userId
    );

    // Enviar email
    await sendReviewNotificationEmail({
      to: data.userEmail,
      userName: data.userName,
      vehicleMake: data.vehicleMake,
      vehicleModel: data.vehicleModel,
      reservationId: data.reservationId,
      reviewToken,
    });

    // Actualizar progreso
    await job.updateProgress(100);

    console.log(`✅ Notificación enviada exitosamente para reserva ${data.reservationId}`);
  } catch (error) {
    console.error(`❌ Error enviando notificación para reserva ${data.reservationId}:`, error);
    throw error;
  }
}

// Event listeners para el worker
reviewNotificationWorker.on('completed', (job) => {
  console.log(`🎉 Job completado: ${job.name} (ID: ${job.id})`);
});

reviewNotificationWorker.on('failed', (job, err) => {
  console.error(`💥 Job falló: ${job?.name} (ID: ${job?.id})`, err);
});

reviewNotificationWorker.on('error', (err) => {
  console.error('💥 Error en worker:', err);
});

reviewNotificationWorker.on('ready', () => {
  console.log('🚀 Review notification worker está listo');
});

// Función para inicializar el worker
export async function initializeReviewWorker() {
  try {
    console.log('🔧 Inicializando review notification worker...');
    // El worker se inicializa automáticamente al importarse
    console.log('✅ Review notification worker inicializado');
  } catch (error) {
    console.error('❌ Error inicializando review worker:', error);
    throw error;
  }
}

// Función para cerrar el worker
export async function closeReviewWorker() {
  try {
    await reviewNotificationWorker.close();
    console.log('✅ Review notification worker cerrado');
  } catch (error) {
    console.error('❌ Error cerrando review worker:', error);
  }
}
