import Link from "next/link"
import { Button } from "@/components/ui/button"
import GoDashboard from './go-dashboard'

export default function Navbar() {
  return (
    <nav className="w-full py-4 px-6 md:px-12 flex items-center justify-between border-b">
      <div className="flex items-center">
        <Link href="/" className="text-2xl font-bold text-[#1a2b5e]">
          Auto<span className="text-[#ff8c00]">op</span>
        </Link>
      </div>
      <div className="hidden md:flex items-center space-x-8">
        <Link href="/vehicles" className="text-gray-700 hover:text-[#1a2b5e]">
          Vehículos
        </Link>
        <Link href="#how-it-works" className="text-gray-700 hover:text-[#1a2b5e]">
          Cómo Funciona
        </Link>
        <Link href="#features" className="text-gray-700 hover:text-[#1a2b5e]">
          Características
        </Link>
        <Link href="#testimonials" className="text-gray-700 hover:text-[#1a2b5e]">
          Testimonios
        </Link>
        <GoDashboard />
      </div>
      <div className="md:hidden">
        <Button variant="ghost" size="icon">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <line x1="4" x2="20" y1="12" y2="12" />
            <line x1="4" x2="20" y1="6" y2="6" />
            <line x1="4" x2="20" y1="18" y2="18" />
          </svg>
        </Button>
      </div>
    </nav>
  )
}
