import { <PERSON><PERSON><PERSON>, Arrow<PERSON><PERSON>, Clock, CheckCircle, AlertCircle, MessageSquare } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"

export function SupportStats() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Open Tickets</p>
              <p className="text-2xl font-bold">24</p>
            </div>
            <div className="h-12 w-12 rounded-full bg-yellow-100 flex items-center justify-center">
              <Clock className="h-6 w-6 text-yellow-500" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-xs text-muted-foreground">
            <ArrowDown className="mr-1 h-3 w-3 text-green-500" />
            <span className="text-green-500 font-medium">12%</span>
            <span className="ml-1">from last week</span>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Resolved Today</p>
              <p className="text-2xl font-bold">18</p>
            </div>
            <div className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center">
              <CheckCircle className="h-6 w-6 text-green-500" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-xs text-muted-foreground">
            <ArrowUp className="mr-1 h-3 w-3 text-green-500" />
            <span className="text-green-500 font-medium">8%</span>
            <span className="ml-1">from yesterday</span>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Urgent Issues</p>
              <p className="text-2xl font-bold">5</p>
            </div>
            <div className="h-12 w-12 rounded-full bg-red-100 flex items-center justify-center">
              <AlertCircle className="h-6 w-6 text-red-500" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-xs text-muted-foreground">
            <ArrowUp className="mr-1 h-3 w-3 text-red-500" />
            <span className="text-red-500 font-medium">2</span>
            <span className="ml-1">new since yesterday</span>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Avg. Response Time</p>
              <p className="text-2xl font-bold">1.8h</p>
            </div>
            <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
              <MessageSquare className="h-6 w-6 text-blue-500" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-xs text-muted-foreground">
            <ArrowDown className="mr-1 h-3 w-3 text-green-500" />
            <span className="text-green-500 font-medium">15%</span>
            <span className="ml-1">improvement</span>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
