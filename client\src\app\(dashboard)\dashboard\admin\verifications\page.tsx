'use client';

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { UserVerification, verificationApi } from '@/lib/api/user-verification.api';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import Image from 'next/image';
import Swal from 'sweetalert2';
import { VerificationDataTable } from '@/components/admin/verification-data-table';

export default function AdminVerificationsPage() {
  const queryClient = useQueryClient();
  const [selectedVerification, setSelectedVerification] = useState<UserVerification | null>(null);
  const [rejectReason, setRejectReason] = useState('');
  const [showRejectDialog, setShowRejectDialog] = useState(false);
  const [showDocumentDialog, setShowDocumentDialog] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<{ url: string; title: string } | null>(null);

  // Obtener verificaciones pendientes
  const { data, isLoading } = useQuery({
    queryKey: ['adminVerifications'],
    queryFn: verificationApi.admin.getPendingVerifications,
    staleTime: 60 * 1000, // 1 minuto
  });
  const verifications = data?.data || [];

  // Aprobar verificación
  const approveMutation = useMutation({
    mutationFn: (userId: string) => verificationApi.admin.approveVerification(userId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['adminVerifications'] });
      toast.success('Verificación aprobada correctamente');
    },
    onError: (error) => {
      toast.error(`Error al aprobar la verificación: ${error.message}`);
    }
  });

  // Rechazar verificación
  const rejectMutation = useMutation({
    mutationFn: ({ userId, notes }: { userId: string; notes: string }) =>
      verificationApi.admin.rejectVerification(userId, notes),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['adminVerifications'] });
      setShowRejectDialog(false);
      setRejectReason('');
      toast.success('Verificación rechazada correctamente');
    },
    onError: (error) => {
      toast.error(`Error al rechazar la verificación: ${error.message}`);
    }
  });

  // Manejar aprobación con confirmación
  const handleApprove = (userId: string) => {
    const verification = verifications.find(v => v.userId === userId);
    if (!verification) return;

    Swal.fire({
      title: '¿Estás seguro?',
      text: `¿Deseas aprobar la verificación de ${verification.user.name}?`,
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#10b981', // Color verde
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Sí, aprobar',
      cancelButtonText: 'Cancelar'
    }).then((result) => {
      if (result.isConfirmed) {
        approveMutation.mutate(userId);
      }
    });
  };

  // Manejar rechazo (abrir diálogo)
  const handleReject = (verification: UserVerification) => {
    setSelectedVerification(verification);
    setRejectReason('');
    setShowRejectDialog(true);
  };

  // Confirmar rechazo
  const confirmReject = () => {
    if (!selectedVerification) return;

    rejectMutation.mutate({
      userId: selectedVerification.userId,
      notes: rejectReason
    });
  };

  // Ver documento
  const handleViewDocument = (url: string, title: string) => {
    const proxyUrl = `/api/proxy/v1/files/download?key=${url}`;
    setSelectedDocument({ url: proxyUrl, title });
    setShowDocumentDialog(true);
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Verificaciones de Usuarios</h1>
          <p className="text-muted-foreground">
            Revisa y aprueba las verificaciones de identidad de los usuarios
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Verificaciones pendientes</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <VerificationDataTable
              data={verifications}
              onApprove={handleApprove}
              onReject={handleReject}
              onViewDocument={handleViewDocument}
            />
          )}
        </CardContent>
      </Card>

      {/* Diálogo de rechazo */}
      <Dialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Rechazar verificación</DialogTitle>
            <DialogDescription>
              Por favor, proporciona un motivo para el rechazo. Esta información será enviada al usuario.
            </DialogDescription>
          </DialogHeader>

          <Textarea
            placeholder="Motivo del rechazo"
            value={rejectReason}
            onChange={(e) => setRejectReason(e.target.value)}
            className="min-h-[100px]"
          />

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowRejectDialog(false)}>
              Cancelar
            </Button>
            <Button
              variant="destructive"
              onClick={confirmReject}
              disabled={!rejectReason.trim() || rejectMutation.isPending}
            >
              {rejectMutation.isPending ? 'Rechazando...' : 'Rechazar verificación'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo para ver documentos */}
      <Dialog open={showDocumentDialog} onOpenChange={setShowDocumentDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{selectedDocument?.title}</DialogTitle>
          </DialogHeader>

          <div className="flex justify-center">
            {selectedDocument && (
              <div className="relative w-full h-[400px]">
                <Image
                  src={selectedDocument.url}
                  alt={selectedDocument.title}
                  fill
                  className="object-contain"
                  unoptimized
                />
              </div>
            )}
          </div>

          <DialogFooter>
            <Button onClick={() => setShowDocumentDialog(false)}>
              Cerrar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
