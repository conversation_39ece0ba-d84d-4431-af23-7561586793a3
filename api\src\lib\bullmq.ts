import { Queue, Worker, QueueEvents } from 'bullmq';
import IORedis from 'ioredis';

// Configuración de Redis
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  maxRetriesPerRequest: null, // Requerido por BullMQ
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  maxLoadingTimeout: 1000,
};

// Crear conexión Redis
export const redis = new IORedis(redisConfig);

// Configuración base para BullMQ
const bullmqConfig = {
  connection: redisConfig,
  defaultJobOptions: {
    removeOnComplete: 100, // Mantener solo los últimos 100 jobs completados
    removeOnFail: 50,      // Mantener solo los últimos 50 jobs fallidos
    attempts: 3,           // Reintentar hasta 3 veces
    backoff: {
      type: 'exponential' as const,
      delay: 2000,
    },
  },
};

// Queue para notificaciones de reseñas
export const reviewNotificationQueue = new Queue('review-notifications', bullmqConfig);

// Queue Events para monitoreo
export const reviewQueueEvents = new QueueEvents('review-notifications', {
  connection: redisConfig
});

// Función para inicializar BullMQ
export async function initializeBullMQ() {
  try {
    // Verificar conexión Redis
    await redis.ping();
    console.log('✅ Redis connection established');

    // Configurar job recurrente para completar reservas
    await reviewNotificationQueue.add(
      'daily-reservation-completion',
      {},
      {
        repeat: {
          tz: 'America/Mexico_City',
          pattern: '0 9 * * *', // Todos los días a las 9:00 AM
        },
        jobId: 'daily-reservation-completion', // ID único para evitar duplicados
      }
    );

    // Configurar job recurrente para verificar reseñas pendientes
    await reviewNotificationQueue.add(
      'daily-review-check',
      {},
      {
        repeat: {
          tz: 'America/Mexico_City', // Zona horaria de México
          pattern: '0 10 * * *', // Todos los días a las 10:00 AM
        },
        jobId: 'daily-review-check', // ID único para evitar duplicados
      }
    );

    console.log('✅ BullMQ initialized successfully');
    console.log('📅 Daily reservation completion scheduled for 9:00 AM');
    console.log('📅 Daily review check scheduled for 10:00 AM');
  } catch (error) {
    console.error('❌ Error initializing BullMQ:', error);
    throw error;
  }
}

// Función para limpiar recursos
export async function closeBullMQ() {
  try {
    await reviewNotificationQueue.close();
    await reviewQueueEvents.close();
    await redis.quit();
    console.log('✅ BullMQ resources closed');
  } catch (error) {
    console.error('❌ Error closing BullMQ:', error);
  }
}

// Función para obtener estadísticas de la queue
export async function getQueueStats() {
  try {
    const waiting = await reviewNotificationQueue.getWaiting();
    const active = await reviewNotificationQueue.getActive();
    const completed = await reviewNotificationQueue.getCompleted();
    const failed = await reviewNotificationQueue.getFailed();

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
    };
  } catch (error) {
    console.error('Error getting queue stats:', error);
    return null;
  }
}
