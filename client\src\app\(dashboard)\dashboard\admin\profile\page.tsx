"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Camera, Save } from "lucide-react"

export default function AdminProfilePage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Perfil</h1>
        <p className="text-muted-foreground">Administra tu información personal y configuración de cuenta</p>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {/* Profile Info */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Información Personal</CardTitle>
            <CardDescription>Actualiza tu información personal y datos de contacto</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Avatar Section */}
            <div className="flex items-center space-x-4">
              <Avatar className="h-20 w-20">
                <AvatarImage src="/placeholder.jpg" />
                <AvatarFallback>AM</AvatarFallback>
              </Avatar>
              <div>
                <Button variant="outline" size="sm">
                  <Camera className="mr-2 h-4 w-4" />
                  Cambiar foto
                </Button>
                <p className="text-sm text-muted-foreground mt-1">JPG, GIF o PNG. Máximo 1MB.</p>
              </div>
            </div>

            <Separator />

            {/* Form Fields */}
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="firstName">Nombre</Label>
                <Input id="firstName" defaultValue="Alex" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName">Apellido</Label>
                <Input id="lastName" defaultValue="Morgan" />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">Correo Electrónico</Label>
              <Input id="email" type="email" defaultValue="<EMAIL>" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone">Teléfono</Label>
              <Input id="phone" defaultValue="+52 55 1234 5678" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="bio">Biografía</Label>
              <Textarea
                id="bio"
                placeholder="Cuéntanos sobre ti..."
                defaultValue="Administrador de la plataforma Autoop con experiencia en gestión de sistemas y atención al cliente."
              />
            </div>

            <Button>
              <Save className="mr-2 h-4 w-4" />
              Guardar Cambios
            </Button>
          </CardContent>
        </Card>

        {/* Account Status */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Estado de Cuenta</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm">Rol</span>
                <Badge>Administrador</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Estado</span>
                <Badge className="bg-green-100 text-green-800">Activo</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Verificado</span>
                <Badge className="bg-green-100 text-green-800">Sí</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Miembro desde</span>
                <span className="text-sm text-muted-foreground">Enero 2024</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Estadísticas</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm">Tickets resueltos</span>
                <span className="text-sm font-medium">127</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Verificaciones aprobadas</span>
                <span className="text-sm font-medium">89</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Tiempo de respuesta promedio</span>
                <span className="text-sm font-medium">2.3h</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
