import { apiService } from '@/services/api';
import { Vehicle } from './vehicles.api';

export interface FavoriteResponse {
  success: boolean;
  message?: string;
  data?: Vehicle[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface FavoriteStatusResponse {
  success: boolean;
  message?: string;
  isFavorite: boolean;
}

export interface MultipleFavoriteStatusResponse {
  success: boolean;
  message?: string;
  data: Record<string, boolean>;
}

export interface AddFavoriteResponse {
  success: boolean;
  message?: string;
  data?: any;
}

export const favoritesApi = {
  // Obtener favoritos del usuario con paginación
  getFavorites: async (params: { page: number; limit: number }) => {
    const result = await apiService.get<FavoriteResponse>('/client/favorites', { params });
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.error);
    }
  },

  // Agregar vehículo a favoritos
  addToFavorites: async (vehicleId: string) => {
    const result = await apiService.post<AddFavoriteResponse>(`/client/favorites/${vehicleId}`);
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.error);
    }
  },

  // Remover vehículo de favoritos
  removeFromFavorites: async (vehicleId: string) => {
    const result = await apiService.delete<{ success: boolean; message?: string }>(`/client/favorites/${vehicleId}`);
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.error);
    }
  },

  // Verificar si un vehículo está en favoritos
  isFavorite: async (vehicleId: string) => {
    const result = await apiService.get<FavoriteStatusResponse>(`/client/favorites/${vehicleId}/status`);
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.error);
    }
  },

  // Obtener estados de favoritos para múltiples vehículos
  getFavoritesStatus: async (vehicleIds: string[]) => {
    const result = await apiService.post<MultipleFavoriteStatusResponse>('/client/favorites/status', {
      vehicleIds
    });
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.error);
    }
  },

  // Toggle favorito (agregar si no está, remover si está)
  toggleFavorite: async (vehicleId: string) => {
    try {
      // Primero verificar el estado actual
      const statusResult = await favoritesApi.isFavorite(vehicleId);
      
      if (statusResult.isFavorite) {
        // Si está en favoritos, remover
        return await favoritesApi.removeFromFavorites(vehicleId);
      } else {
        // Si no está en favoritos, agregar
        return await favoritesApi.addToFavorites(vehicleId);
      }
    } catch (error: any) {
      throw new Error(error.message || 'Error al cambiar estado de favorito');
    }
  }
};
