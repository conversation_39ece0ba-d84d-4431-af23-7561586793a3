import { apiService } from "@/services/api";

// Interfaces para reseñas
export interface VehicleReview {
  id: string;
  rating: number;
  comment: string | null;
  createdAt: string;
  updatedAt: string;
  user: {
    id: string;
    name: string;
    image: string | null;
  };
  vehicle: {
    id: string;
    make: string;
    model: string;
    year: number;
  };
  reservation: {
    id: string;
    startDate: string;
    endDate: string;
  };
}

export interface CreateVehicleReviewData {
  reservationId: string;
  rating: number;
  comment?: string;
}

export interface ReviewEligibility {
  eligible: boolean;
  reason?: string;
  reservation?: {
    id: string;
    startDate: string;
    endDate: string;
    vehicle: {
      id: string;
      make: string;
      model: string;
      year: number;
    };
  };
}

export interface VehicleReviewStats {
  averageRating: number;
  totalReviews: number;
  ratingDistribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
}

// Tipos para paginación
interface PaginationParams {
  page?: number;
  limit?: number;
}

interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Tipos para filtros
interface ReviewFilters extends PaginationParams {
  rating?: number;
  hasComment?: boolean;
  startDate?: string;
  endDate?: string;
}

export const reviewsApi = {
  // APIs públicas
  public: {
    // Obtener reseñas de un vehículo
    getVehicleReviews: async (vehicleId: string, params: PaginationParams = {}) => {
      const { page = 1, limit = 10 } = params;
      const result = await apiService.get<PaginatedResponse<VehicleReview>>(
        `/public/reviews/vehicle/${vehicleId}?page=${page}&limit=${limit}`
      );
      console.log('Result of [getVehicleReviews]:', result);
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },

    // Obtener estadísticas de reseñas de un vehículo
    getVehicleStats: async (vehicleId: string) => {
      const result = await apiService.get<VehicleReviewStats>(
        `/public/reviews/vehicle/${vehicleId}/stats`
      );
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },
  },

  // APIs de usuario (cliente)
  client: {
    // Crear una reseña
    create: async (reviewData: CreateVehicleReviewData) => {
      const result = await apiService.post<VehicleReview>('/user/reviews', reviewData);
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },

    // Verificar elegibilidad para reseña
    checkEligibility: async (reservationId: string) => {
      const result = await apiService.get<ReviewEligibility>(
        `/user/reviews/eligibility/${reservationId}`
      );
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },

    // Obtener mis reseñas
    getMyReviews: async (params: PaginationParams = {}) => {
      const { page = 1, limit = 10 } = params;
      const result = await apiService.get<PaginatedResponse<VehicleReview>>(
        `/user/reviews?page=${page}&limit=${limit}`
      );
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },

    // Actualizar una reseña
    update: async (reviewId: string, reviewData: Partial<CreateVehicleReviewData>) => {
      const result = await apiService.put<VehicleReview>(`/user/reviews/${reviewId}`, reviewData);
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },

    // Eliminar una reseña
    delete: async (reviewId: string) => {
      const result = await apiService.delete(`/user/reviews/${reviewId}`);
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },
  },

  // APIs de host
  host: {
    // Obtener reseñas de mis vehículos
    getMyVehicleReviews: async (params: ReviewFilters = {}) => {
      const { page = 1, limit = 10, ...filters } = params;
      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...Object.fromEntries(
          Object.entries(filters).filter(([, value]) => value !== undefined)
        ),
      });

      const result = await apiService.get<PaginatedResponse<VehicleReview>>(
        `/host/reviews?${queryParams}`
      );
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },

    // Obtener reseñas de un vehículo específico (host)
    getVehicleReviews: async (vehicleId: string, params: PaginationParams = {}) => {
      const { page = 1, limit = 10 } = params;
      const result = await apiService.get<PaginatedResponse<VehicleReview>>(
        `/host/reviews/vehicle/${vehicleId}?page=${page}&limit=${limit}`
      );
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },

    // Responder a una reseña (si se implementa en el futuro)
    respond: async (reviewId: string, response: string) => {
      const result = await apiService.post(`/host/reviews/${reviewId}/respond`, {
        response,
      });
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },
  },

  // APIs de administrador
  admin: {
    // Obtener todas las reseñas
    getAll: async (params: ReviewFilters = {}) => {
      const { page = 1, limit = 10, ...filters } = params;
      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...Object.fromEntries(
          Object.entries(filters).filter(([, value]) => value !== undefined)
        ),
      });

      const result = await apiService.get<PaginatedResponse<VehicleReview>>(
        `/admin/reviews?${queryParams}`
      );
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },

    // Obtener una reseña específica
    getById: async (reviewId: string) => {
      const result = await apiService.get<VehicleReview>(`/admin/reviews/${reviewId}`);
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },

    // Eliminar una reseña
    delete: async (reviewId: string) => {
      const result = await apiService.delete(`/admin/reviews/${reviewId}`);
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },

    // Obtener estadísticas generales
    getStats: async () => {
      const result = await apiService.get('/admin/reviews/stats');
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },
  },
};
