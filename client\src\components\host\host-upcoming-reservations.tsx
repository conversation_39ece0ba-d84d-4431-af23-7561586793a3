import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"

const upcomingReservations = [
  {
    id: 1,
    client: {
      name: "<PERSON>",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "EJ",
    },
    vehicle: {
      name: "Tesla Model 3",
      image: "/placeholder.svg?height=40&width=40",
    },
    dates: {
      start: "15 Mar",
      end: "20 Mar",
      days: 5,
    },
    amount: "$550.00",
    status: "confirmed",
  },
  {
    id: 2,
    client: {
      name: "<PERSON>",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "DC",
    },
    vehicle: {
      name: "BMW X5",
      image: "/placeholder.svg?height=40&width=40",
    },
    dates: {
      start: "18 Mar",
      end: "21 Mar",
      days: 3,
    },
    amount: "$720.00",
    status: "pending",
  },
]

export function HostUpcomingReservations() {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-md font-medium">Próximas Reservas</CardTitle>
        <Button variant="ghost" size="sm" asChild>
          <Link href="/dashboard/host/reservations">Ver Todas</Link>
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {upcomingReservations.map((reservation) => (
            <div key={reservation.id} className="flex items-center gap-4 p-4 border rounded-lg">
              <Avatar>
                <AvatarImage src={reservation.client.avatar || "/placeholder.svg"} alt={reservation.client.name} />
                <AvatarFallback>{reservation.client.initials}</AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <p className="text-sm font-medium">{reservation.client.name}</p>
                    <Badge variant={reservation.status === "confirmed" ? "default" : "secondary"} className="text-xs">
                      {reservation.status === "confirmed" ? "Confirmado" : "Pendiente"}
                    </Badge>
                  </div>
                  <span className="text-sm font-medium">{reservation.amount}</span>
                </div>
                <div className="flex items-center gap-2 mb-2">
                  <div className="h-6 w-6 rounded overflow-hidden">
                    <Image
                      src={reservation.vehicle.image || "/placeholder.svg"}
                      alt={reservation.vehicle.name}
                      width={24}
                      height={24}
                      className="h-full w-full object-cover"
                    />
                  </div>
                  <span className="text-sm text-muted-foreground">{reservation.vehicle.name}</span>
                </div>
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <span>
                    {reservation.dates.start} - {reservation.dates.end} ({reservation.dates.days} días)
                  </span>
                  {reservation.status === "pending" && (
                    <div className="flex gap-2">
                      <Button size="sm" variant="outline" className="h-6 text-xs">
                        Rechazar
                      </Button>
                      <Button size="sm" className="h-6 text-xs">
                        Aceptar
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
