"use client"

import Link from "next/link"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { Checkbox } from "@/components/ui/checkbox"
import VehicleCard from "@/components/vehicles/vehicle-card"
import { vehiclesApi } from "@/lib/api/vehicles.api"
import { useQuery } from "@tanstack/react-query"
import { useSearchParams } from 'next/navigation'
import { PaginationControl } from "@/components/ui/pagination-control"

export default function VehiclesPage() {
  const searchParams = useSearchParams()
  const page = Number(searchParams.get('page') || 1)
  const limit = Number(searchParams.get('limit') || 10)

  const { data, isLoading, error } = useQuery({
    queryKey: ['vehicles', page, limit],
    queryFn: () => vehiclesApi.getAll({ page, limit }),
    staleTime: 60 * 1000, // 1 minuto
  })

  const vehicles = data?.data
  const pagination = data?.pagination
  console.log('pagination', pagination)
  console.log('vehicles', vehicles)

  return (
    <div className="bg-background">
      <div className="container mx-auto py-10">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {/* Filtros */}
          <div className="space-y-6">
            <div>
              <h2 className="text-xl font-semibold mb-4">Filtros</h2>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="location">Ubicación</Label>
                  <Input id="location" placeholder="Ciudad, estado o código postal" />
                </div>

                <div>
                  <Label>Rango de Precio</Label>
                  <div className="pt-2">
                    <Slider defaultValue={[0, 500]} max={1000} step={10} />
                  </div>
                  <div className="flex justify-between text-xs text-muted-foreground mt-1">
                    <span>$0</span>
                    <span>$1000+</span>
                  </div>
                </div>

                <div>
                  <Label className="mb-2 block">Tipo de Vehículo</Label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox id="sedan" />
                      <label htmlFor="sedan" className="text-sm">Sedán</label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="suv" />
                      <label htmlFor="suv" className="text-sm">SUV</label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="luxury" />
                      <label htmlFor="luxury" className="text-sm">Lujo</label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="hatchback" />
                      <label htmlFor="hatchback" className="text-sm">Hatchback</label>
                    </div>
                  </div>
                </div>

                <div>
                  <Label className="mb-2 block">Características</Label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox id="airConditioning" />
                      <label htmlFor="airConditioning" className="text-sm">Aire Acondicionado</label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="sunroof" />
                      <label htmlFor="sunroof" className="text-sm">Techo Corredizo</label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="gps" />
                      <label htmlFor="gps" className="text-sm">GPS</label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="navigation" />
                      <label htmlFor="navigation" className="text-sm">Navegación</label>
                    </div>
                  </div>
                </div>

                <div>
                  <Label className="mb-2 block">Disponibilidad</Label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox id="available" />
                      <label htmlFor="available" className="text-sm">Disponible</label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="reserved" />
                      <label htmlFor="reserved" className="text-sm">Reservado</label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Lista de Vehículos */}
          <div className="md:col-span-3">
            {isLoading ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-primary"></div>
              </div>
            ) : error ? (
                <div className="text-center text-red-500">No se pudieron cargar los vehículos. Por favor, intenta de nuevo más tarde.</div>
              ) : !vehicles || vehicles.length === 0 ? (
              <div className="text-center">No se encontraron vehículos.</div>
            ) : (
                    <>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {vehicles.map((vehicle) => (
                          <Link key={vehicle.id} href={`/vehicles/${vehicle.id}`} prefetch={false}>
                            <VehicleCard
                              id={vehicle.id}
                              make={vehicle.make}
                              model={vehicle.model}
                              price={vehicle.price}
                              // rating={vehicle.averageRating || 0}
                              // reviews={vehicle.totalReviews || 0}
                              // mock rating and reviews using math random
                              rating={Math.floor(Math.random() * 5) + 1}
                              reviews={Math.floor(Math.random() * 100)}
                              images={vehicle.images}
                              features={vehicle.features}
                              year={vehicle.year}
                              engineSize={vehicle.engineSize}
                              transmission={vehicle.transmission}
                              trim={vehicle.trim}
                              bodyType={vehicle.bodyType}
                              createdAt={vehicle.createdAt}
                            />
                          </Link>
                        ))}
                      </div>

                      {pagination && (
                        <PaginationControl
                          currentPage={page}
                          totalPages={pagination.totalPages}
                          className="mt-8"
                        />
                      )}
                    </>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}



