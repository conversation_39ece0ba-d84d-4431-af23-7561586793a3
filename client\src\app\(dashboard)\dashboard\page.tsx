"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"

export default function DashboardRedirect() {
  const router = useRouter()

  useEffect(() => {
    // Mock user role - en producción vendría del contexto de autenticación
    const userRole = "admin" // Cambiar según el usuario autenticado
    router.push(`/dashboard/${userRole}`)
  }, [router])

  return (
    <div className="flex items-center justify-center h-full">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
        <p className="mt-2 text-muted-foreground">Redirigiendo...</p>
      </div>
    </div>
  )
}
