// Do not import or export anything in this file

// If you modify the user type here, you need to change it in next-auth.d.ts as well
type User = {
  id: string;
  email: string;
  name: string;
  image?: string;
  isHostVerified: boolean;
  userType: "client" | "host" | null;
  availableUserTypes: string[];
  role: "admin" | "user";
    accessToken: string;
    refreshToken: string;
    expiresAt: number;
  };
  
// interface NavItem {
//   title: string;
//   href: string;
//   icon: LucideIcon;
//   prefetch?: boolean;
// }
// interface NavAccordionItem {
//   title: string;
//   icon: LucideIcon;
//   items: {
//     title: string;
//     href: string;
//   }[];
// }
  
  interface PaginationState {
    pageIndex: number;
    pageSize: number;
  }
  
  interface PaginatedResponse<T> {
    // items: T[];
    data: T[];
    pagination: {
      total: number;
      currentPage: number;
      totalPages: number;
      from: number;
      to: number;
    }
  }
  
  interface ApiFilters extends PaginationState {
    search?: string;
  }
  

  declare module '*.mp3' {
    const src: string;
    export default src;
    // const content: string;
    // export default content;
  }


  interface Pagination {
    total: number;
    currentPage: number;
    totalPages: number;
    from: number;
    to: number;
  }
  