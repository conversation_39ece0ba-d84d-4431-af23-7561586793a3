// client/src/middleware.ts
import { NextResponse, NextRequest } from "next/server";
import { BEARER_COOKIE_NAME } from "./constants";

export const runtime = "experimental-edge";

export default function middleware(request: NextRequest) {
  const url = request.nextUrl.clone();
  const token = url.searchParams.get("token");
  const tcl = url.searchParams.get("tcl");

  if (token && !tcl) {
    url.searchParams.delete("token");

    const res = NextResponse.redirect(url);
    res.cookies.set(BEARER_COOKIE_NAME, token, {
      httpOnly: false,
      secure: true, // Siempre true para sameSite: "none"
      sameSite: "none",
      path: "/",
    });
    return res;
  }

  return NextResponse.next();
}

export const config = {
  matcher: ["/((?!api|_next).*)"],
};
