import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export function HostEarningsChart() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Ganancias Mensuales</CardTitle>
        <CardDescription>Tus ingresos por renta de vehículos en los últimos 6 meses</CardDescription>
      </CardHeader>
      <CardContent className="h-[300px] flex items-center justify-center">
        <div className="text-center text-muted-foreground">
          <p>Gráfico de ganancias</p>
          <p className="text-sm">(Se implementaría con Recharts o similar)</p>
          <div className="mt-4 space-y-2">
            <div className="flex justify-between text-sm">
              <span>Enero:</span>
              <span className="font-medium">$3,200</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Febrero:</span>
              <span className="font-medium">$3,800</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Marzo:</span>
              <span className="font-medium">$4,250</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
