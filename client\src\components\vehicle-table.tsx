"use client"
import Image from "next/image"
import { Eye, MoreHorizontal, Edit, Trash, ShieldCheck, ShieldAlert } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

// Mock data
const vehicles = [
  {
    id: 1,
    image: "/placeholder.svg?height=40&width=40",
    name: "Mercedes-Benz E-Class",
    year: 2022,
    type: "Sedan • Luxury",
    host: {
      name: "<PERSON>",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "RS",
    },
    license: "CA • ABC-1234",
    status: [
      { type: "insurance", label: "Insurance" },
      { type: "registration", label: "Registration" },
    ],
    statusType: "active",
  },
  {
    id: 2,
    image: "/placeholder.svg?height=40&width=40",
    name: "Tesla Model 3",
    year: 2023,
    type: "Electric • Sedan",
    host: {
      name: "Lisa Chan",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "LC",
    },
    license: "NY • XYZ-5678",
    status: [{ type: "insurance-missing", label: "Insurance Missing" }],
    statusType: "warning",
  },
]

export function VehicleTable() {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead className="w-[30px]">
            <Checkbox />
          </TableHead>
          <TableHead>Vehicle</TableHead>
          <TableHead>Host</TableHead>
          <TableHead>Details</TableHead>
          <TableHead>Documents</TableHead>
          <TableHead>Status</TableHead>
          <TableHead className="text-right">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {vehicles.map((vehicle) => (
          <TableRow key={vehicle.id}>
            <TableCell>
              <Checkbox />
            </TableCell>
            <TableCell>
              <div className="flex items-center gap-3">
                <div className="h-10 w-10 rounded overflow-hidden">
                  <Image
                    src={vehicle.image || "/placeholder.svg"}
                    alt={vehicle.name}
                    width={40}
                    height={40}
                    className="h-full w-full object-cover"
                  />
                </div>
                <div>
                  <div className="font-medium">{vehicle.name}</div>
                  <div className="text-xs text-muted-foreground">
                    {vehicle.year} • {vehicle.type}
                  </div>
                </div>
              </div>
            </TableCell>
            <TableCell>
              <div className="flex items-center gap-2">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={vehicle.host.avatar || "/placeholder.svg"} alt={vehicle.host.name} />
                  <AvatarFallback>{vehicle.host.initials}</AvatarFallback>
                </Avatar>
                <span className="text-sm">{vehicle.host.name}</span>
              </div>
            </TableCell>
            <TableCell>
              <div className="text-sm">{vehicle.license}</div>
            </TableCell>
            <TableCell>
              <div className="flex flex-wrap gap-1">
                {vehicle.status.map((status, index) => (
                  <Badge
                    key={index}
                    variant={status.type.includes("missing") ? "destructive" : "outline"}
                    className={
                      status.type === "insurance"
                        ? "bg-green-100 text-green-800 hover:bg-green-100"
                        : status.type === "registration"
                          ? "bg-blue-100 text-blue-800 hover:bg-blue-100"
                          : status.type === "insurance-missing"
                            ? "bg-red-100 text-red-800 hover:bg-red-100"
                            : ""
                    }
                  >
                    {status.label}
                  </Badge>
                ))}
              </div>
            </TableCell>
            <TableCell>
              <Badge
                variant={
                  vehicle.statusType === "active"
                    ? "success"
                    : vehicle.statusType === "warning"
                      ? "warning"
                      : "destructive"
                }
                className={
                  vehicle.statusType === "active"
                    ? "bg-green-100 text-green-800 hover:bg-green-100"
                    : vehicle.statusType === "warning"
                      ? "bg-yellow-100 text-yellow-800 hover:bg-yellow-100"
                      : "bg-red-100 text-red-800 hover:bg-red-100"
                }
              >
                {vehicle.statusType === "active"
                  ? "Active"
                  : vehicle.statusType === "warning"
                    ? "Warning"
                    : "Suspended"}
              </Badge>
            </TableCell>
            <TableCell className="text-right">
              <div className="flex justify-end gap-2">
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <Eye className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <Edit className="h-4 w-4" />
                </Button>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem>
                      <Eye className="mr-2 h-4 w-4" />
                      <span>View Details</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Edit className="mr-2 h-4 w-4" />
                      <span>Edit Vehicle</span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    {vehicle.statusType === "active" ? (
                      <DropdownMenuItem className="text-yellow-600">
                        <ShieldAlert className="mr-2 h-4 w-4" />
                        <span>Flag for Review</span>
                      </DropdownMenuItem>
                    ) : (
                      <DropdownMenuItem className="text-green-600">
                        <ShieldCheck className="mr-2 h-4 w-4" />
                        <span>Approve Vehicle</span>
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuItem className="text-red-600">
                      <Trash className="mr-2 h-4 w-4" />
                      <span>Delete Vehicle</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}
