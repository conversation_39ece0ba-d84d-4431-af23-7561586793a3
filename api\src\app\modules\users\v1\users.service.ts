
import { prisma } from "@/lib/prisma";
import { HttpException } from "@/exceptions/HttpExceptions";

export class UsersService {
  // Obtener todos los usuarios (admin)
  static async getAllUsers() {
    return await prisma.user.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    });
  }

  // Obtener un usuario por ID (admin)
  static async getUserById(id: string) {
    const user = await prisma.user.findUnique({
      where: { id }
    });

    if (!user) {
      throw HttpException.NotFound("User not found");
    }

    return user;
  }

  static async getAllHosts({ query }: { query: { page: number; limit: number } }) {
    const skip = (query.page - 1) * query.limit;

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where: {
          mainUserType: 'host',  // Usar mainUserType en lugar de userType
          NOT: {
            role: 'admin'
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: query.limit,
      }),
      prisma.user.count({
        where: {
          mainUserType: 'host',
          NOT: {
            role: 'admin'
          }
        }
      })
    ]);

    return {
      data: users,
      pagination: {
        page: query.page,
        limit: query.limit,
        total,
        totalPages: Math.ceil(total / query.limit),
      }
    };
  }

  static async getAllClients({ query }: { query: { page: number; limit: number } }) {
    const skip = (query.page - 1) * query.limit;

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where: {
          mainUserType: 'client',  // Usar mainUserType en lugar de userType
          NOT: {
            role: 'admin'
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: query.limit,
      }),
      prisma.user.count({
        where: {
          mainUserType: 'client',
          NOT: {
            role: 'admin'
          }
        }
      })
    ]);

    return {
      data: users,
      pagination: {
        page: query.page,
        limit: query.limit,
        total,
        totalPages: Math.ceil(total / query.limit),
      }
    };
  }

  static async verifyUser(id: string) {
    return await prisma.user.update({
      where: { id },
      data: { emailVerified: true }
    });
  }

}