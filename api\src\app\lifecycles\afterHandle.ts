import { type Context } from 'elysia';
import { colorizeRequest } from '../utils/colorizeLogRequest';

export const afterHandle = (ctx: Context) => {

  // log response like afterHandle but only for responses using chalk
  ctx.set.status = 200;
  const method = ctx.request.method;
  const path = ctx.path;
  const status = (ctx.response as any).status || 200;

  const start = (ctx.store as any).start as number;
  const end = performance.now();

  const time = end - start;

  const timeTreeDecimals = +time.toFixed(3);

  colorizeRequest(method, path, status, timeTreeDecimals);

}