"use client"

import { useState } from "react"
import { ChevronDown, Search } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

export function ClientFilters() {
  const [searchQuery, setSearchQuery] = useState("")

  return (
    <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
      <div className="relative flex-1 max-w-sm">
        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          type="search"
          placeholder="Search clients..."
          className="pl-8"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>
      <div className="flex flex-wrap items-center gap-2">
        <Button variant="outline" size="sm" className="h-8 gap-1">
          All Statuses
          <ChevronDown className="h-4 w-4" />
        </Button>
        <Button variant="outline" size="sm" className="h-8 gap-1">
          All Ratings
          <ChevronDown className="h-4 w-4" />
        </Button>
        <Button variant="outline" size="sm" className="h-8 gap-1">
          Booking Frequency
          <ChevronDown className="h-4 w-4" />
        </Button>
        <Button variant="outline" size="sm" className="h-8 gap-1">
          Payment Status
          <ChevronDown className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" className="h-8">
          More Filters
        </Button>
      </div>
    </div>
  )
}
