import * as React from 'react'
import { renderToStaticMarkup } from 'react-dom/server';
import WelcomeEmail from '@/emails/Welcome';
import VerificationEmail from '@/emails/Send-verification';
import ResetPasswordEmail from '@/emails/Reset-password';
import { sendEmailWith<PERSON>odemailer } from '@/lib/sendVerificationEmail';


export async function sendWelcomeEmail(to: string) {

  // send the email with the templates
  const { data, error } = await sendEmailWithNodemailer({
    to: to,
    subject: 'Welcome to Autoop!',
    text: 'Welcome to Autoop!',
    html: renderToStaticMarkup(React.createElement(WelcomeEmail)),
  });

  if (error) {
    console.error('Error sending welcome email:', error);
    return;
  }

  console.log('Welcome email sent:', data);
}

export async function sendVerificationEmail({
  to,
  url,
  name,
}: {
  to: string;
  url: string;
  name: string;
}) {

  const { data, error } = await sendEmailWithNodemailer({
    to: to,
    subject: 'Verify your email',
    text: 'Verify your email',
    html: renderToStaticMarkup(React.createElement(VerificationEmail, { name, url })),
  });

  if (error) {
    console.error('Error sending verification email:', error);
    return;
  }

  console.log('Verification email sent:', data);
}

export async function sendResetPasswordEmail({
  to,
  url,
  name,
}: {
  to: string;
  url: string;
  name: string;
}) {

  const { data, error } = await sendEmailWithNodemailer({
    to: to,
    subject: 'Restablecer tu contraseña',
    text: `Hola ${name}, haz clic en el siguiente enlace para restablecer tu contraseña: ${url}`,
    html: renderToStaticMarkup(React.createElement(ResetPasswordEmail, { name, url })),
  });

  if (error) {
    console.error('Error sending reset password email:', error);
    return;
  }

  console.log('Reset password email sent:', data);
}