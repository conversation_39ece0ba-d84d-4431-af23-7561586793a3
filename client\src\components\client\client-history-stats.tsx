import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Car, MapPin, Clock, Star } from "lucide-react"

const stats = [
  {
    title: "Total Viajes",
    value: "24",
    change: "+3 este mes",
    icon: Car,
    color: "text-blue-600",
  },
  {
    title: "Kilómetros Recorridos",
    value: "2,450",
    change: "+180 km este mes",
    icon: MapPin,
    color: "text-green-600",
  },
  {
    title: "Tiempo Total",
    value: "156h",
    change: "+12h este mes",
    icon: Clock,
    color: "text-purple-600",
  },
  {
    title: "Calificación Promedio",
    value: "4.8",
    change: "Excelente historial",
    icon: Star,
    color: "text-yellow-600",
  },
]

export function ClientHistoryStats() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {stats.map((stat, index) => (
        <Card key={index}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
            <stat.icon className={`h-4 w-4 ${stat.color}`} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
            <p className="text-xs text-muted-foreground">{stat.change}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
