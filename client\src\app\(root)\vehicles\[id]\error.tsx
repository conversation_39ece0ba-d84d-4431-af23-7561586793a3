'use client';
import React from 'react'
import { Button } from '@/components/ui/button'
import { useRouter } from 'next/navigation';

export default function ErrorPage() {
  const router = useRouter();
  return (
    <div className="container mx-auto py-8 min-h-screen flex flex-col justify-center items-center">
      <h2 className="text-2xl font-bold">Error</h2>
      <p className="text-muted-foreground">No se pudo cargar la información del vehículo.</p>
      <Button onClick={async () => {
        router.push("/vehicles")
      }} className="mt-4">
        Volver a vehículos
      </Button>
    </div>
  )
}
