"use client"

import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { useQuery } from "@tanstack/react-query"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { format } from "date-fns"
import { es } from "date-fns/locale"
import { Calendar, Car, Clock, /* MapPin, */ User, Phone, Mail, ArrowLeft } from "lucide-react"
import Image from "next/image"
import { reservationsApi } from "@/lib/api/reservations.api"
import { Skeleton } from "@/components/ui/skeleton"
import { differenceInDays } from "date-fns"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { useState } from "react"
import toast from "react-hot-toast"

export default function ReservationDetailPage() {
  const params = useParams<{ id: string }>()
  const router = useRouter()
  const [isCancelling, setIsCancelling] = useState(false)

  const { data: reservation, isLoading, error } = useQuery({
    queryKey: ['reservation', params.id],
    queryFn: () => reservationsApi.client.getReservationById(params.id),
    staleTime: 60 * 1000, // 1 minuto
  })

  const handleCancelReservation = async () => {
    try {
      setIsCancelling(true)
      await reservationsApi.client.cancelReservation(params.id)
      toast.success("Reserva cancelada exitosamente.")
      router.refresh()
    } catch (error) {
      console.error("Error al cancelar la reserva:", error)
      
      toast.error("No se pudo cancelar la reserva. Intenta de nuevo más tarde.")
    } finally {
      setIsCancelling(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return <Badge className="bg-green-500">Activa</Badge>
      case 'confirmed':
        return <Badge className="bg-green-500">Confirmada</Badge>
      case 'pending':
        return <Badge className="bg-blue-500">Pendiente</Badge>
      case 'upcoming':
        return <Badge className="bg-blue-500">Próxima</Badge>
      case 'completed':
        return <Badge className="bg-gray-500">Completada</Badge>
      case 'cancelled':
        return <Badge className="bg-red-500">Cancelada</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-2">
          <Skeleton className="h-10 w-24" />
          <Skeleton className="h-6 w-32" />
        </div>
        <Skeleton className="h-8 w-64" />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Skeleton className="h-64" />
          <Skeleton className="h-64" />
        </div>
      </div>
    )
  }

  if (error || !reservation) {
    return (
      <div className="flex flex-col items-center justify-center h-96 space-y-4">
        <h2 className="text-2xl font-bold">Error al cargar la reserva</h2>
        <p className="text-muted-foreground">No se pudo cargar la información de la reserva.</p>
        <Button onClick={() => router.push("/dashboard/client/reservations")}>
          Volver a mis reservas
        </Button>
      </div>
    )
  }

  const startDate = new Date(reservation.startDate)
  const endDate = new Date(reservation.endDate)
  const days = differenceInDays(endDate, startDate)
  const canCancel = ['pending', 'confirmed'].includes(reservation.status.toLowerCase())
  const vehicle = reservation.vehicle

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm" onClick={() => router.push("/dashboard/client/reservations")}>
            <ArrowLeft className="h-4 w-4 mr-2" /> Volver
          </Button>
          {getStatusBadge(reservation.status)}
        </div>
        {canCancel && (
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="destructive">Cancelar reserva</Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>¿Estás seguro?</AlertDialogTitle>
                <AlertDialogDescription>
                  Esta acción no se puede deshacer. Cancelar la reserva podría estar sujeto a políticas de cancelación.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancelar</AlertDialogCancel>
                <AlertDialogAction onClick={handleCancelReservation} disabled={isCancelling}>
                  {isCancelling ? "Cancelando..." : "Confirmar cancelación"}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        )}
      </div>

      <h1 className="text-3xl font-bold">Reserva #{params.id.slice(0, 8)}</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Detalles del vehículo */}
        <Card>
          <CardHeader>
            <CardTitle>Detalles del vehículo</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="relative h-48 rounded-md overflow-hidden">
              <Image
                src={vehicle.images && vehicle.images.length > 0 ? vehicle.images[0] : "/placeholder.svg?height=300&width=500"}
                alt={`${vehicle.make} ${vehicle.model}`}
                fill
                className="object-cover"
              />
            </div>
            
            <h3 className="text-xl font-bold">{vehicle.make} {vehicle.model} {vehicle.year}</h3>
            
            {vehicle.host && (
              <div className="flex items-center space-x-2">
                <User className="h-4 w-4" />
                <span>Anfitrión: {vehicle.host.name}</span>
              </div>
            )}
            
            <div className="flex items-center space-x-2">
              <Car className="h-4 w-4" />
              <span>Precio por día: ${vehicle.price}</span>
            </div>
          </CardContent>
        </Card>

        {/* Detalles de la reserva */}
        <Card>
          <CardHeader>
            <CardTitle>Detalles de la reserva</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4" />
              <div>
                <div>Desde: {format(startDate, "PPP", { locale: es })}</div>
                <div>Hasta: {format(endDate, "PPP", { locale: es })}</div>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4" />
              <div>
                <div>Hora de recogida: {format(startDate, "HH:mm")}</div>
                <div>Hora de entrega: {format(endDate, "HH:mm")}</div>
              </div>
            </div>
            
            <div>
              <p>Duración: {days} {days === 1 ? 'día' : 'días'}</p>
            </div>
            
            <Separator />
            
            {reservation.contactName && (
              <div className="flex items-center space-x-2">
                <User className="h-4 w-4" />
                <span>Contacto: {reservation.contactName}</span>
              </div>
            )}
            
            {reservation.contactEmail && (
              <div className="flex items-center space-x-2">
                <Mail className="h-4 w-4" />
                <span>Email: {reservation.contactEmail}</span>
              </div>
            )}
            
            {reservation.contactPhone && (
              <div className="flex items-center space-x-2">
                <Phone className="h-4 w-4" />
                <span>Teléfono: {reservation.contactPhone}</span>
              </div>
            )}
            
            <Separator />
            
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Subtotal ({days} {days === 1 ? 'día' : 'días'})</span>
                <span>${vehicle.price * days}</span>
              </div>
              
              <Separator />
              
              <div className="flex justify-between font-bold">
                <span>Total</span>
                <span>${reservation.totalPrice}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}