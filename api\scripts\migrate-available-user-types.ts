import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function migrateAvailableUserTypes() {
  try {
    console.log('🚀 Starting migration of availableUserTypes...')

    // Obtener todos los usuarios que no son admin y tienen availableUserTypes vacío
    const usersToUpdate = await prisma.user.findMany({
      where: {
        AND: [
          { role: { not: "admin" } }, // No son admin
          { userType: { not: null } }, // Tienen un userType definido
          {
            OR: [
              { availableUserTypes: { equals: [] } }, // Array vacío
              // { availableUserTypes: [] as any } // O null
            ]
          }
        ]
      },
      select: {
        id: true,
        userType: true,
        availableUserTypes: true,
        email: true,
        role: true
      }
    })

    console.log(`📊 Found ${usersToUpdate.length} users to migrate`)

    if (usersToUpdate.length === 0) {
      console.log('✅ No users need migration')
      return
    }

    // Mostrar usuarios que se van a actualizar
    console.log('\n👥 Users to be updated:')
    usersToUpdate.forEach((user, index) => {
      console.log(`${index + 1}. ${user.email} (${user.role}) - userType: ${user.userType} -> availableUserTypes: [${user.userType}]`)
    })

    // Actualizar cada usuario
    console.log('\n🔄 Updating users...')
    const updatePromises = usersToUpdate.map(async (user) => {
      if (!user.userType) return null

      const updated = await prisma.user.update({
        where: { id: user.id },
        data: {
          availableUserTypes: [user.userType]
        }
      })

      console.log(`✅ Updated ${user.email}: availableUserTypes = [${user.userType}]`)
      return updated
    })

    const results = await Promise.all(updatePromises)
    const successfulUpdates = results.filter(result => result !== null)

    console.log(`\n🎉 Migration completed successfully!`)
    console.log(`📈 Total users found: ${usersToUpdate.length}`)
    console.log(`✅ Total users updated: ${successfulUpdates.length}`)

  } catch (error) {
    console.error('❌ Error during migration:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Ejecutar la migración
migrateAvailableUserTypes()
  .then(() => {
    console.log('\n🏁 Migration script finished')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n💥 Migration failed:', error)
    process.exit(1)
  })
