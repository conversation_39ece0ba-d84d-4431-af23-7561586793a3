{"name": "autoop-client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 4000", "build": "next build", "build:pages": "npx @cloudflare/next-on-pages@1", "start": "next start --port 4000", "lint": "next lint"}, "dependencies": {"@better-fetch/fetch": "^1.1.15", "@hookform/resolvers": "^4.1.3", "@orpc/client": "^0.52.0", "@orpc/server": "^0.52.0", "@preact-signals/safe-react": "^0.8.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.8", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "@tanstack/react-query": "^5.72.2", "@tanstack/react-query-next-experimental": "^5.79.0", "@tanstack/react-table": "^8.21.2", "add": "^2.0.6", "autoprefixer": "^10.4.20", "axios": "^1.8.3", "babel-plugin-react-compiler": "^19.0.0-beta-bafa41b-20250307", "better-auth": "^1.2.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cookies-next": "^5.1.0", "date-fns": "latest", "embla-carousel-react": "8.5.1", "immer": "latest", "input-otp": "1.4.1", "lucide-react": "^0.518.0", "luxon": "^3.6.1", "next": "15.3", "next-themes": "^0.4.6", "react": "^19.0.0", "react-date-range": "^2.0.1", "react-day-picker": "latest", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-resizable-panels": "^2.1.7", "react-scan": "^0.2.14", "recharts": "latest", "sonner": "^1.7.1", "sweetalert2": "latest", "sweetalert2-react-content": "latest", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "use-sync-external-store": "latest", "vaul": "^0.9.6", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/luxon": "^3.6.2", "@types/node": "^20", "@types/react": "^19", "@types/react-date-range": "^1.4.10", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.2", "typescript": "^5"}}