import { z } from 'zod';

export const envSchema = z.object({
  NEXT_PUBLIC_API_URL: z.string().url(),
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  IS_DEV: z.string().optional().transform((val) => val === 'true'),
});

export type Env = z.infer<typeof envSchema>;

// create the same object but with process.env
const env = envSchema.parse({
  NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
  NODE_ENV: process.env.NODE_ENV,
  IS_DEV: process.env.IS_DEV,
});

export default env;
