import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, Clock, XCircle } from "lucide-react"

const payouts = [
  {
    id: "PAY-001",
    amount: "$1,250.00",
    date: "2024-01-01",
    status: "completed",
    method: "Transferencia bancaria",
  },
  {
    id: "PAY-002",
    amount: "$980.00",
    date: "2024-01-15",
    status: "pending",
    method: "PayPal",
  },
  {
    id: "PAY-003",
    amount: "$1,100.00",
    date: "2023-12-15",
    status: "completed",
    method: "Transferencia bancaria",
  },
  {
    id: "PAY-004",
    amount: "$750.00",
    date: "2023-12-01",
    status: "failed",
    method: "PayPal",
  },
]

const getStatusIcon = (status: string) => {
  switch (status) {
    case "completed":
      return <CheckCircle className="h-4 w-4 text-green-600" />
    case "pending":
      return <Clock className="h-4 w-4 text-yellow-600" />
    case "failed":
      return <XCircle className="h-4 w-4 text-red-600" />
    default:
      return null
  }
}

const getStatusBadge = (status: string) => {
  switch (status) {
    case "completed":
      return <Badge className="bg-green-100 text-green-800">Completado</Badge>
    case "pending":
      return <Badge className="bg-yellow-100 text-yellow-800">Pendiente</Badge>
    case "failed":
      return <Badge className="bg-red-100 text-red-800">Fallido</Badge>
    default:
      return <Badge variant="secondary">{status}</Badge>
  }
}

export function HostPayoutHistory() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Historial de Pagos</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {payouts.map((payout) => (
            <div key={payout.id} className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-3">
                {getStatusIcon(payout.status)}
                <div>
                  <p className="font-medium">{payout.amount}</p>
                  <p className="text-sm text-muted-foreground">{payout.method}</p>
                </div>
              </div>
              <div className="text-right">
                {getStatusBadge(payout.status)}
                <p className="text-sm text-muted-foreground mt-1">{payout.date}</p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
