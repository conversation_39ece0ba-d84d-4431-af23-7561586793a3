"use client"

import { useEffect, useState } from "react"
import { DateRange, type RangeKeyDict } from "react-date-range"
import { addDays, format } from "date-fns"
import { es } from "date-fns/locale"
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Info } from "lucide-react"
import "react-date-range/dist/styles.css"
import "react-date-range/dist/theme/default.css"
import { DialogTitle } from '@radix-ui/react-dialog'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

interface DateRangeModalProps {
  unavailableDates?: string[] // ISO date strings
  onChange: (range: { startDate: Date; endDate: Date }) => void
  initialDateRange?: { startDate: Date | null; endDate: Date | null }
}

export default function DateRangeModal({ unavailableDates = [], onChange, initialDateRange }: DateRangeModalProps) {
  const [isOpen, setIsOpen] = useState(false)

  // Validar initialDateRange antes de usarlo
  const validInitialDateRange = initialDateRange &&
    initialDateRange.startDate instanceof Date &&
    initialDateRange.endDate instanceof Date &&
    !isNaN(initialDateRange.startDate.getTime()) &&
    !isNaN(initialDateRange.endDate.getTime())
    ? initialDateRange
    : null;

  // Usar el rango de fechas inicial validado o encontrar el próximo disponible
  const [state, setState] = useState<RangeKeyDict['selection'][]>([
    {
      startDate: validInitialDateRange?.startDate || new Date(),
      endDate: validInitialDateRange?.endDate || addDays(new Date(), 1),
      key: "selection",
    },
  ])

  // Convert ISO strings to Date objects
  const disabledDates = unavailableDates.map((dateStr) => new Date(dateStr))

  // Function to check if a date is disabled
  const isDateDisabled = (date: Date) => {
    // Disable dates before today
    if (date < new Date()) {
      return true
    }

    // Disable specific unavailable dates
    return disabledDates.some(
      (disabledDate) =>
        date.getFullYear() === disabledDate.getFullYear() &&
        date.getMonth() === disabledDate.getMonth() &&
        date.getDate() === disabledDate.getDate(),
    )
  }

  const handleSelect = (ranges: RangeKeyDict) => {
    const selection = ranges.selection
    setState([selection])
  }

  const handleApply = () => {
    if (state[0].startDate && state[0].endDate) {
      onChange({
        startDate: state[0].startDate,
        endDate: state[0].endDate,
      })
    }
    setIsOpen(false)
  }

  // Responsive direction
  const [direction, setDirection] = useState<"horizontal" | "vertical">("horizontal")

  useEffect(() => {
    const handleResize = () => {
      setDirection(window.innerWidth < 768 ? "vertical" : "horizontal")
    }

    handleResize()
    window.addEventListener("resize", handleResize)
    return () => window.removeEventListener("resize", handleResize)
  }, [])

  // Calcular días
  const nights = state[0].startDate && state[0].endDate
    ? Math.ceil((state[0].endDate.getTime() - state[0].startDate.getTime()) / (1000 * 60 * 60 * 24))
    : 0

  const horaRecogida = "9:00 am"
  const horaDevolucion = "12:00 pm"
  const tolerancia = "1 hora"

  const fechaRecogida = state[0].startDate ? format(state[0].startDate, "EEEE dd 'de' MMMM", { locale: es }) : ""
  const fechaDevolucion = state[0].endDate ? format(state[0].endDate, "EEEE dd 'de' MMMM", { locale: es }) : ""

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {/* ...existing code... */}
      </DialogTrigger>
      <DialogContent
        className="sm:max-w-[725px] p-0 h-[82%] overflow-visible overflow-y-auto rounded-lg" // Cambia overflow-auto por overflow-visible
        style={{ maxHeight: "90vh" }} // Opcional, para evitar scroll innecesario
        >
        <DialogTitle asChild>
          <div className="p-4 border-b">
            <div className="flex items-center">
              <h2 className="text-lg font-semibold">Selecciona el rango de fechas</h2>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <Info className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="top" align="center" className="max-w-xs">
                    <p>
                      La fecha de recogida es cuando recibes el vehículo.
                      <br />
                      La fecha de devolución es cuando lo entregas.
                      <br />
                      El vehículo no estará disponible para otros usuarios durante estas fechas.
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <div className="flex flex-col gap-1 mt-2">
              <div className="flex justify-between text-sm text-gray-500">
                <span>Fecha de recogida</span>
                <span>Fecha de devolución</span>
              </div>
              {/* Texto explicativo de horarios */}
              {state[0].startDate && state[0].endDate && (
                <div className="text-xs text-gray-600 mt-1 bg-gray-100 rounded p-2">
                  <span>
                    Puedes recoger el vehículo el <b>{fechaRecogida}</b> a las <b>{horaRecogida}</b>.<br />
                    Debes devolverlo el <b>{fechaDevolucion}</b> a más tardar a las <b>{horaDevolucion}</b> (con tolerancia de {tolerancia}).
                  </span>
                  <br />
                  <span>
                    Aunque el rango seleccionado abarca {nights + 1} días, la reserva corresponde a <b>{nights} {nights === 1 ? 'día' : 'días'}</b> de uso efectivo.
                  </span>
                </div>
              )}
            </div>
            <div className="flex justify-between items-center mt-4">
              <div className="flex items-center">
                <span className="font-medium">Duración de la reserva:</span>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-6 w-6">
                        <Info className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="right" align="center" className="max-w-xs">
                      <p>El vehículo estará a tu disposición desde la hora de recogida del primer día hasta la hora de devolución del último día.</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <span className="font-bold">{nights} {nights === 1 ? 'día' : 'días'}</span>
            </div>


          </div>
        </DialogTitle>
        <div className="px-4 pb-2 flex justify-center">
          <DateRange
            onChange={handleSelect}
            moveRangeOnFirstSelection={false}
            ranges={state}
            months={2}
            direction={direction}
            locale={es}
            disabledDay={isDateDisabled}
            minDate={new Date()}
            rangeColors={["#1a2b5e"]}
            className="border rounded-md"
          />
        </div>
        <div className="flex justify-end gap-2 p-4 border-t">
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Cancelar
          </Button>
          <Button className="bg-[#1a2b5e] hover:bg-[#152348]" onClick={handleApply}>
            <span className="font-medium">Aplicar</span>
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
