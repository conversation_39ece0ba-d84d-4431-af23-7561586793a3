import { createAccessControl } from "better-auth/plugins/access";
import { defaultStatements } from 'better-auth/plugins/organization/access';
/**
 * Statements are the permissions that can be granted to a role.
 */
const statement = {
  ...defaultStatements,
  organization: ["view", "create", "update", "delete", "manage_members"],
  project: ["view", "create", "update", "delete", "manage_settings"],
  members: ["create", "view", "invite", "remove", "update_roles"],
  settings: ["view", "update"]
} as const;

const ac = createAccessControl(statement);

const member = ac.newRole({
  organization: ["view"],
  project: ["view"],
  members: ["view"],
  settings: ["view"]
})

const projectManager = ac.newRole({
  organization: ["view"],
  project: ["view", "create", "update", "manage_settings"],
  members: ["view", "invite"],
  invitation: [...statement.invitation],
  settings: ["view", "update"]
})

const admin = ac.newRole({
  organization: ["view", "update", "manage_members"],
  project: [...statement.project],
  members: [...statement.members],
  invitation: [...statement.invitation],
  settings: [...statement.settings]
})

const owner = ac.newRole({
  organization: [...statement.organization],
  project: [...statement.project],
  members: [...statement.members],
  invitation: [...statement.invitation],
  settings: [...statement.settings]
})



const roles = {
  member,
  projectManager,
  admin,
  owner
}

export { ac, roles }

