interface VehicleFeaturesProps {
  features: Array<{
    icon: string
    label: string
  }>
  amenities: string[]
}

export default function VehicleFeatures({ features, amenities }: VehicleFeaturesProps) {
  return (
    <div>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
        {features.map((feature, index) => (
          <div key={index} className="flex flex-col items-center text-center">
            <div className="w-12 h-12 flex items-center justify-center bg-gray-100 rounded-full mb-2">
              {feature.icon === "automatic" && (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <circle cx="12" cy="12" r="10" />
                  <path d="M8 12h8" />
                </svg>
              )}
              {feature.icon === "hybrid" && (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M3 17h10v-2H3zm0-4h18v-2H3zm0-4h14V7H3z" />
                </svg>
              )}
              {feature.icon === "seats" && (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M4 18v3h16v-3" />
                  <path d="M4 14.5V12a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v2.5" />
                  <path d="M12 12V6a2 2 0 1 0-4 0v0" />
                  <path d="M6 12V6a2 2 0 1 1 4 0v0" />
                  <path d="M18 12V6a2 2 0 1 0-4 0v6" />
                </svg>
              )}
              {feature.icon === "hp" && (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M14 3v4a1 1 0 0 0 1 1h4" />
                  <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z" />
                  <path d="M9 17h6" />
                  <path d="M9 13h6" />
                </svg>
              )}
            </div>
            <span className="text-sm font-medium">{feature.label}</span>
          </div>
        ))}
      </div>

      <h3 className="font-semibold text-lg mb-4">Vehicle Features</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-2 mb-6">
        {amenities.map((amenity, index) => (
          <div key={index} className="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-green-500 mr-2"
            >
              <polyline points="20 6 9 17 4 12" />
            </svg>
            <span>{amenity}</span>
          </div>
        ))}
      </div>
    </div>
  )
}
