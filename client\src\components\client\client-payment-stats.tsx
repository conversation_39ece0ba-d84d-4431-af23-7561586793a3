import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { DollarSign, CreditCard, Calendar, TrendingDown } from "lucide-react"

const stats = [
  {
    title: "Total Gastado",
    value: "$2,450",
    change: "+$240 este mes",
    icon: DollarSign,
    color: "text-green-600",
  },
  {
    title: "Este Mes",
    value: "$240",
    change: "2 transacciones",
    icon: Calendar,
    color: "text-blue-600",
  },
  {
    title: "Métodos de Pago",
    value: "3",
    change: "2 tarjetas, 1 PayPal",
    icon: CreditCard,
    color: "text-purple-600",
  },
  {
    title: "Ahorro Promedio",
    value: "15%",
    change: "vs precios tradicionales",
    icon: TrendingDown,
    color: "text-orange-600",
  },
]

export function ClientPaymentStats() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {stats.map((stat, index) => (
        <Card key={index}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
            <stat.icon className={`h-4 w-4 ${stat.color}`} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
            <p className="text-xs text-muted-foreground">{stat.change}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
