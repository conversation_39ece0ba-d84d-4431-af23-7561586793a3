"use client"
import { useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { useBookingStore } from "@/lib/store/booking-store"
import { useSearchParams } from "next/navigation"
import DateRangeModal from "@/components/vehicles/date-range-modal"
import Image from "next/image"
import { useQuery } from "@tanstack/react-query"
import { vehiclesApi } from "@/lib/api/vehicles.api"
import { Skeleton } from "@/components/ui/skeleton"
import { Info } from "lucide-react"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

export default function BookingDates({ vehicleId }: { vehicleId: string }) {
  const { dateRange, setDateRange, setVehicle, nextStep, calculateTotal } = useBookingStore()
  const searchParams = useSearchParams()

  // Obtener información del vehículo mediante API
  const { data: vehicleData, isLoading, error } = useQuery({
    queryKey: ['vehicle', vehicleId],
    queryFn: () => vehiclesApi.getById(vehicleId),
    staleTime: 60 * 60 * 1000, // Caché por 1 hora
    enabled: !!vehicleId
  })

  // Obtener fechas no disponibles
  const { data: unavailableDatesData } = useQuery({
    queryKey: ['vehicle-unavailable-dates', vehicleId],
    queryFn: () => vehiclesApi.getUnavailableDates(vehicleId),
    staleTime: 60 * 60 * 1000, // Caché por 1 hora
    enabled: !!vehicleId
  })

  // Corregir esto - unavailableDatesData ya es el array de fechas
  const unavailableDates = unavailableDatesData || []

  // Set vehicle data in store
  useEffect(() => {
    if (vehicleData) {
      setVehicle({
        id: vehicleData.id,
        make: vehicleData.make,
        model: vehicleData.model,
        year: vehicleData.year,
        color: vehicleData.color,
        price: vehicleData.price
      })
      // Recalcular el total cuando se establece el vehículo
      calculateTotal()
    }
  }, [vehicleData, setVehicle, calculateTotal])

  // Get dates from URL and set them in the store
  useEffect(() => {
    const fromParam = searchParams.get('from')
    const toParam = searchParams.get('to')

    if (fromParam && toParam) {
      try {
        const startDate = new Date(fromParam)
        const endDate = new Date(toParam)

        // Verificar que las fechas son válidas
        if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
          setDateRange({
            startDate,
            endDate
          })
          // Recalcular el total cuando se establecen las fechas
          calculateTotal()
        }
      } catch (error) {
        console.error("Error parsing dates from URL:", error)
      }
    }
  }, [searchParams, setDateRange, calculateTotal])

  const handleDateChange = (range: { startDate: Date; endDate: Date }) => {
    setDateRange({
      startDate: range.startDate,
      endDate: range.endDate,
    })
    // Recalcular el total cuando cambian las fechas
    calculateTotal()
  }

  const onSubmit = () => {
    if (!dateRange.startDate || !dateRange.endDate) {
      return
    }

    nextStep()
  }

  // Calculate number of days - con validación para evitar errores
  const days = dateRange.startDate instanceof Date && dateRange.endDate instanceof Date
    ? Math.ceil((dateRange.endDate.getTime() - dateRange.startDate.getTime()) / (1000 * 60 * 60 * 24))
    : 0

  if (isLoading) {
    return (
      <div className="max-w-3xl mx-auto">
        <div className="mb-6 p-4 border rounded-lg flex items-center">
          <Skeleton className="w-24 h-24 rounded-md mr-4" />
          <div className="space-y-2">
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-4 w-24" />
          </div>
        </div>
        <Skeleton className="h-8 w-64 mb-4" />
        <Skeleton className="h-40 w-full mb-6" />
        <div className="flex justify-end">
          <Skeleton className="h-10 w-32" />
        </div>
      </div>
    )
  }

  if (error || !vehicleData) {
    return (
      <div className="max-w-3xl mx-auto">
        <div className="p-4 border border-red-300 bg-red-50 rounded-lg text-red-800">
          <h3 className="font-bold">Error al cargar la información</h3>
          <p>No se pudo cargar la información del vehículo. Por favor, intenta de nuevo más tarde.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-3xl mx-auto">
      <div className="mb-6 p-4 border rounded-lg flex items-center">
        <div className="w-24 h-24 relative mr-4">
          <Image
            src={vehicleData.images?.[0] || "/placeholder.svg?height=300&width=500"}
            alt={`${vehicleData.make} ${vehicleData.model}`}
            fill
            className="object-cover rounded-md"
          />
        </div>
        <div>
          <h3 className="font-bold text-lg">{vehicleData.make} {vehicleData.model} {vehicleData.year}</h3>
          <p className="text-gray-600">{vehicleData.features?.location || "Ubicación no disponible"}</p>
          <p className="font-medium">${vehicleData.price} / día</p>
        </div>
      </div>

      <div className="mb-6">
        <h2 className="text-xl font-bold mb-4">Confirma tus fechas de reserva</h2>
        <div className="mb-4">
          <label className="block mb-2 font-medium">Selecciona el rango de fechas</label>
          <DateRangeModal
            unavailableDates={unavailableDates}
            onChange={handleDateChange}
            initialDateRange={
              dateRange.startDate! instanceof Date && dateRange.endDate! instanceof Date
                ? { startDate: dateRange.startDate!, endDate: dateRange.endDate! }
                : undefined
            }
          />
          {(!dateRange.startDate || !dateRange.endDate) && (
            <p className="text-sm text-red-500 mt-1">Por favor selecciona un rango de fechas</p>
          )}
        </div>

        {dateRange.startDate instanceof Date && dateRange.endDate instanceof Date && days > 0 && (
          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
            <div className="flex justify-between mb-2">
              <div className="flex items-center">
                <span>Duración:</span>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-6 w-6 ml-1">
                        <Info className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>La duración se calcula desde el día de recogida hasta el día de devolución. El vehículo estará a tu disposición durante este período.</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <span className="font-medium">{days} {days === 1 ? 'día' : 'días'}</span>
            </div>
            <div className="flex justify-between mb-2">
              <span>Precio por día:</span>
              <span className="font-medium">${vehicleData.price}</span>
            </div>
            <div className="flex justify-between font-bold">
              <span>Total:</span>
              <span>${vehicleData.price * days} / {days} {days === 1 ? 'día' : 'días'}</span>
            </div>
            <p className="text-xs text-gray-500 mt-2">
              El precio total incluye el uso del vehículo desde la hora de recogida del primer día hasta la hora de devolución del último día.
            </p>
          </div>
        )}
      </div>

      <div className="flex justify-between">
        <Button variant="outline" onClick={() => {
          window.location.href = `/vehicles/${vehicleId}`
        }}>
          <span className="font-medium">Ir al detalle del vehículo</span>
        </Button>
        <Button 
          onClick={onSubmit}
          disabled={!dateRange.startDate || !dateRange.endDate}
          className="bg-[#1a2b5e] hover:bg-[#152348]"
        >
          Continuar
        </Button>
      </div>
    </div>
  )
}
