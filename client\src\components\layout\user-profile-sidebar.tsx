"use client"

// import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { authClient } from "@/auth-client"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  User,
  Settings,
  // CreditCard,
  Bell,
  LogOut,
  ChevronUp,
  Star,
} from "lucide-react"
import { useUser } from '@/context/user-context'
import { deleteCookie } from 'cookies-next/client'
import { BEARER_COOKIE_NAME } from '@/constants'
import { RoleSwitch } from './role-switch'

export function UserProfileSidebar() {
  // const { data: session } = authClient.useSession()
  const { user } = useUser()
  const router = useRouter()

  const handleLogout = async () => {
    await authClient.signOut();
    deleteCookie(BEARER_COOKIE_NAME);
    router.push("/")
  }

  // Determinar la ruta del perfil según el tipo de usuario
  const getProfilePath = () => {
    if (user.role === "admin") {
      return "/dashboard/admin/profile"
    } else if (user.userType === "host") {
      return "/dashboard/host/profile"
    } else if (user.userType === "client") {
      return "/dashboard/client/profile"
    }
    return "/dashboard/profile"
  }

  return (
    <div className="border-t border-white/10">
      {/* Role Switch Component */}
      <RoleSwitch />

      {/* User Profile Dropdown */}
      <div className="p-4">
        <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="w-full justify-start p-2 h-auto text-left hover:bg-white/5">
            <div className="flex items-center w-full">
              <Image
                src={user.image || "/placeholder.svg"}
                alt={user.name || "Usuario"}
                width={40}
                height={40}
                className="rounded-full mr-3"
              />
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-white truncate">{user.name || "Usuario"}</p>
                <p className="text-xs text-gray-400 truncate">{user.email || ""}</p>
              </div>
              <ChevronUp className="h-4 w-4 text-gray-400" />
            </div>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent side="top" align="start" className="w-56 mb-2">
          {/* Opcional: mostrar plan premium solo si es relevante */}
          <DropdownMenuItem>
            <Star className="mr-2 h-4 w-4" />
            <span>Upgrade to Pro</span>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem asChild>
            <Link href={getProfilePath()}>
              <User className="mr-2 h-4 w-4" />
              <span>Mi Perfil</span>
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <Link href={`${getProfilePath()}/settings`}>
              <Settings className="mr-2 h-4 w-4" />
              <span>Configuración</span>
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem>
            <Bell className="mr-2 h-4 w-4" />
            <span>Notificaciones</span>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={handleLogout} className="text-red-600">
            <LogOut className="mr-2 h-4 w-4" />
            <span>Cerrar sesión</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      </div>
    </div>
  )
}