"use client"

import { useEffect, useState, useMemo } from "react"
import { DateRange, type RangeKeyDict } from "react-date-range"
import { /* addDays, */ differenceInDays, format } from "date-fns"
import { es } from "date-fns/locale"
import { <PERSON><PERSON>, DialogContent, DialogTrigger, <PERSON><PERSON>Title, DialogFooter } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { CalendarIcon, Info } from "lucide-react"
import "react-date-range/dist/styles.css"
import "react-date-range/dist/theme/default.css"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import toast from "react-hot-toast"
import { normalizeDates, isDateDisabled } from "@/lib/utils"

interface DateRangeModalProps {
  unavailableDates?: string[] // ISO date strings
  onChange: (range: { startDate: Date; endDate: Date }) => void
  initialDateRange?: { startDate: Date; endDate: Date }
  minimumRentalNights?: number
  maximumRentalNights?: number
}

export default function DateRangeModal({
  unavailableDates = [],
  onChange,
  initialDateRange,
  // minimumRentalNights = 1,
  // maximumRentalDays = 30
  minimumRentalNights = 1,
  maximumRentalNights = 30
}: DateRangeModalProps) {
  const [isOpen, setIsOpen] = useState(false)
  const unavailableDates2 = useMemo(() => {
    if (!unavailableDates) return [];
    return normalizeDates(unavailableDates);
  }, [unavailableDates]);
  // Validar initialDateRange antes de usarlo
  const validInitialDateRange = initialDateRange &&
    initialDateRange.startDate instanceof Date &&
    initialDateRange.endDate instanceof Date &&
    !isNaN(initialDateRange.startDate.getTime()) &&
    !isNaN(initialDateRange.endDate.getTime())
    ? initialDateRange
    : null;

  // Función para encontrar el próximo rango disponible
  const findNextAvailableRange = () => {
    // Comenzar desde mañana
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
    tomorrow.setHours(0, 0, 0, 0)

    let startDate = new Date(tomorrow)

    // Buscar el próximo día disponible
    while (isDateDisabledHandler(startDate)) {
      startDate.setDate(startDate.getDate() + 1)
    }

    // Para representar N días, necesitamos N+1 días
    // Por ejemplo, para 3 días: check-in, día 1, día 2, día 3, check-out
    let endDate = new Date(startDate)
    let consecutiveDays = 0

    // Necesitamos minimumRentalNights + 1 días para representar minimumRentalNights días
    while (consecutiveDays < minimumRentalNights) {
      endDate.setDate(endDate.getDate() + 1)

      if (!isDateDisabledHandler(endDate)) {
        consecutiveDays++
      } else {
        // Si encontramos un día no disponible, reiniciamos la búsqueda
        startDate = new Date(endDate)
        startDate.setDate(startDate.getDate() + 1)
        endDate = new Date(startDate)
        consecutiveDays = 0
      }
    }

    return { startDate, endDate }
  }

  // Convert ISO strings to Date objects
  // const disabledDates = useMemo(() => {
  //   return normalizeDates(unavailableDates);
  // }, [unavailableDates]);


  // Función para verificar si una fecha está deshabilitada
  const isDateDisabledHandler = (date: Date) => {
    return isDateDisabled(date, unavailableDates2);
  };

  // Usar el rango de fechas inicial validado o encontrar el próximo disponible
  const [state, setState] = useState<RangeKeyDict['selection'][]>(() => {
    if (validInitialDateRange) {
      // Verificar que el rango inicial no incluye el día actual
      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)
      tomorrow.setHours(0, 0, 0, 0)

      if (validInitialDateRange.startDate.getTime() <= tomorrow.getTime() - 1) {
        // Si incluye el día actual, buscar el próximo rango disponible
        const { startDate, endDate } = findNextAvailableRange()
        return [{
          startDate,
          endDate,
          key: "selection",
        }]
      }

      return [{
        startDate: validInitialDateRange.startDate,
        endDate: validInitialDateRange.endDate,
        key: "selection",
      }]
    } else {
      const { startDate, endDate } = findNextAvailableRange()
      return [{
        startDate,
        endDate,
        key: "selection",
      }]
    }
  })

  const handleSelect = (ranges: RangeKeyDict) => {
    const selection = ranges.selection

    // Solo validar cuando ambas fechas estén seleccionadas y sean diferentes
    if (selection.startDate &&
      selection.endDate &&
      selection.startDate.getTime() !== selection.endDate.getTime()) {

      // Para N días, necesitamos N+1 días
      // Por ejemplo, check-in el día 1, check-out el día 4 = 3 días
      const diffDays = differenceInDays(selection.endDate, selection.startDate)

      if (diffDays < minimumRentalNights) {
        toast.error(`La reserva debe ser de al menos ${minimumRentalNights} días`)
        // Mantener la selección anterior
        return
      }

      if (diffDays > maximumRentalNights) {
        toast.error(`La reserva no puede exceder ${maximumRentalNights} días`)
        // Mantener la selección anterior
        return
      }
    }

    setState([selection])
  }

  const handleApply = () => {
    if (state[0].startDate && state[0].endDate) {
      // Verificar nuevamente antes de aplicar
      const diffDays = differenceInDays(state[0].endDate, state[0].startDate)

      if (diffDays < minimumRentalNights) {
        toast.error(`La reserva debe ser de al menos ${minimumRentalNights} días`)
        return
      }

      if (diffDays > maximumRentalNights) {
        toast.error(`La reserva no puede exceder ${maximumRentalNights} días`)
        return
      }

      onChange({
        startDate: state[0].startDate,
        endDate: state[0].endDate,
      })
    }
    setIsOpen(false)
  }

  // Responsive direction
  const [direction, setDirection] = useState<"horizontal" | "vertical">("horizontal")

  useEffect(() => {
    const handleResize = () => {
      setDirection(window.innerWidth < 768 ? "vertical" : "horizontal")
    }

    handleResize()
    window.addEventListener("resize", handleResize)
    return () => window.removeEventListener("resize", handleResize)
  }, [])

  // Calcular días
  const nights = state[0].startDate && state[0].endDate
    ? differenceInDays(state[0].endDate, state[0].startDate)
    : 0

  // Formatear fechas para mostrar
  const fechaRecogida = state[0].startDate ? format(state[0].startDate, "dd 'de' MMMM, yyyy", { locale: es }) : ""
  const fechaDevolucion = state[0].endDate ? format(state[0].endDate, "dd 'de' MMMM, yyyy", { locale: es }) : ""

  // Horarios por defecto
  const horaRecogida = "14:00"
  const horaDevolucion = "12:00"
  const tolerancia = "30 minutos"

  // Asegurar que el rango inicial cumpla con el mínimo de días
  useEffect(() => {
    if (validInitialDateRange) {
      const diffDays = differenceInDays(validInitialDateRange.endDate, validInitialDateRange.startDate) + 1
      if (diffDays < minimumRentalNights) {
        // Si el rango inicial no cumple con el mínimo, ajustarlo
        const { startDate, endDate } = findNextAvailableRange()
        setState([{
          startDate,
          endDate,
          key: "selection"
        }])
      }
    }
  }, [validInitialDateRange, minimumRentalNights, unavailableDates])

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          className="w-full justify-start text-left font-normal h-auto py-2 px-3"
          onClick={() => setIsOpen(true)}
        >
          <CalendarIcon className="mr-2 h-4 w-4 text-gray-500" />
          <span>
            {state[0].startDate && state[0].endDate
              ? `${format(state[0].startDate, "MMM dd, yyyy")} - ${format(state[0].endDate, "MMM dd, yyyy")}`
              : "Selecciona las fechas"}
          </span>
        </Button>
      </DialogTrigger>

      <DialogContent
        className="sm:max-w-[725px] p-0 h-fit max-h-[90vh] overflow-y-auto"
      >
        <DialogTitle asChild>
          <div className="p-4 border-b">
            <div className="flex items-center">
              <h2 className="text-lg font-semibold">Selecciona el rango de fechas</h2>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="icon" className="ml-2">
                      <Info className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="top" align="center" className="max-w-xs">
                    <p>
                      {minimumRentalNights > 1
                        ? `La reserva debe ser de al menos ${minimumRentalNights} días.`
                        : "Selecciona las fechas de tu reserva."}
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>

            <div className="flex flex-col gap-1 mt-2">
              <div className="flex justify-between text-sm text-gray-500">
                <span>Fecha de recogida</span>
                <span>Fecha de devolución</span>
              </div>

              {state[0].startDate && state[0].endDate && (
                <div className="text-xs text-gray-600 mt-1 bg-gray-100 rounded p-2">
                  <span>
                    Puedes recoger el vehículo el <b>{fechaRecogida}</b> a las <b>{horaRecogida}</b>.<br />
                    Debes devolverlo el <b>{fechaDevolucion}</b> a más tardar a las <b>{horaDevolucion}</b> (con tolerancia de {tolerancia}).
                  </span>
                  <br />
                  <span>
                    Aunque el rango seleccionado abarca {nights + 1} días, la reserva corresponde a <b>{nights} {nights === 1 ? 'día' : 'días'}</b> de uso efectivo.
                  </span>
                </div>
              )}
            </div>
          </div>
        </DialogTitle>

        <div
          id="date-range-container"
          className="p-4 w-full overflow-x-auto flex justify-center"
          style={{ overflowY: "visible", whiteSpace: "nowrap" }}
        >
          <div className="inline-block">
            <DateRange
              onChange={handleSelect}
              moveRangeOnFirstSelection={false}
              ranges={state}
              months={2}
              direction={direction}
              locale={es}
              disabledDay={isDateDisabledHandler}
              minDate={new Date()}
              rangeColors={['#1a2b5e']}
              className="border rounded-md"
            />
          </div>
        </div>

        <DialogFooter className="p-4 bg-gray-50 border-t">
          <div className="flex justify-between items-center w-full">
            <div className="flex items-center">
              <span className="font-medium">Duración de la reserva:</span>
            </div>
            <span className="font-bold">{nights} {nights === 1 ? 'día' : 'días'}</span>
          </div>
          <div className="flex justify-end gap-2 mt-4 w-full">
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Cancelar
            </Button>
            <Button className="bg-[#1a2b5e] hover:bg-[#152348]" onClick={handleApply}>
              <span className="font-medium">Aplicar</span>
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
