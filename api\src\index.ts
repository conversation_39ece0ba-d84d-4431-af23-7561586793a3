import { <PERSON><PERSON> } from "elysia";
import { cors } from '@elysiajs/cors';
import { swagger } from '@elysiajs/swagger';
import betterAuthView from '@/app/utils/auth-view';
import { onError } from '@/app/lifecycles/onError';
import { afterHandle } from '@/app/lifecycles/afterHandle';
import appModule from '@/app';
import { authCache } from "./app/middlewares/auth.middleware";
import telemetry from "./telemetry/telemetry";
import { onRequest } from './app/lifecycles/requests';
import { initializeBullMQ, closeBullMQ } from '@/lib/bullmq';
import { initializeReviewWorker, closeReviewWorker } from '@/app/modules/reviews/workers/review-notification.worker';

const app = new Elysia()
  .use(telemetry())
  .use(
    cors({
      credentials: true, // Permite cookies
      origin: true,
    }),
  )
  .use(swagger())
  .state({
    start: 0,
    requestId: ''
  })
  .on('request', onRequest)
  .onError(onError)
  .onAfterHandle(afterHandle)
  .onAfterResponse((c) => {
    const cache = authCache.get(c.store.requestId);
    if (cache) {
      authCache.delete(c.store.requestId);
    }
  })
  .get("/", {
    message: "Hello Vexo API",
  })
  // .get('/ip', ({ server, request }) => {
  //   return server?.requestIP(request)
  // })
  .get("/api/get-headers", (c) => c.headers)
  .all("/api/auth/*", betterAuthView)
  .use(appModule)


const PORT = process.env.PORT || 3000;

// Función para inicializar servicios
async function initializeServices() {
  try {
    console.log('🔧 Inicializando servicios...');

    // Inicializar BullMQ
    await initializeBullMQ();

    // Inicializar workers
    await initializeReviewWorker();

    console.log('✅ Todos los servicios inicializados correctamente');
  } catch (error) {
    console.error('❌ Error inicializando servicios:', error);
    process.exit(1);
  }
}

// Función para cerrar servicios
async function closeServices() {
  try {
    console.log('🔄 Cerrando servicios...');

    await closeReviewWorker();
    await closeBullMQ();

    console.log('✅ Servicios cerrados correctamente');
  } catch (error) {
    console.error('❌ Error cerrando servicios:', error);
  }
}

// Manejar cierre graceful
process.on('SIGINT', async () => {
  console.log('\n🛑 Recibida señal SIGINT, cerrando servidor...');
  await closeServices();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Recibida señal SIGTERM, cerrando servidor...');
  await closeServices();
  process.exit(0);
});

app.listen(PORT, async () => {
  console.log(`🦊 Elysia is running at http://${app.server?.hostname}:${app.server?.port}`);

  // Inicializar servicios después de que el servidor esté corriendo
  await initializeServices();
});
