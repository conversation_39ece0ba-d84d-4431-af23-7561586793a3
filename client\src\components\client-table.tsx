"use client"

import { <PERSON>, MessageSquare, MoreHorizontal, UserCog, ShieldCheck, ShieldAlert } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { But<PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

// Mock data
const clients = [
  {
    id: 1,
    name: "<PERSON>",
    avatar: "/placeholder.svg?height=40&width=40",
    initials: "<PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "+****************",
    totalBookings: 12,
    rating: 4.5,
    status: "Active",
    joinDate: "April 2023",
  },
  {
    id: 2,
    name: "<PERSON>",
    avatar: "/placeholder.svg?height=40&width=40",
    initials: "<PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "+****************",
    totalBookings: 8,
    rating: 5.0,
    status: "Active",
    joinDate: "Jan 2023",
  },
  {
    id: 3,
    name: "Sophia Martinez",
    avatar: "/placeholder.svg?height=40&width=40",
    initials: "SM",
    email: "<EMAIL>",
    phone: "+****************",
    totalBookings: 3,
    rating: 4.0,
    status: "Active",
    joinDate: "Mar 2023",
  },
  {
    id: 4,
    name: "David Chen",
    avatar: "/placeholder.svg?height=40&width=40",
    initials: "DC",
    email: "<EMAIL>",
    phone: "+****************",
    totalBookings: 6,
    rating: 3.0,
    status: "Active",
    joinDate: "Feb 2023",
  },
]

export function ClientTable() {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead className="w-[250px]">Name</TableHead>
          <TableHead>Email</TableHead>
          <TableHead>Phone</TableHead>
          <TableHead className="text-center">Total Bookings</TableHead>
          <TableHead className="text-center">Rating</TableHead>
          <TableHead>Status</TableHead>
          <TableHead className="text-right">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {clients.map((client) => (
          <TableRow key={client.id}>
            <TableCell className="font-medium">
              <div className="flex items-center gap-2">
                <Avatar>
                  <AvatarImage src={client.avatar || "/placeholder.svg"} alt={client.name} />
                  <AvatarFallback>{client.initials}</AvatarFallback>
                </Avatar>
                <div>
                  <div className="font-medium">{client.name}</div>
                  <div className="text-xs text-muted-foreground">Joined {client.joinDate}</div>
                </div>
              </div>
            </TableCell>
            <TableCell>{client.email}</TableCell>
            <TableCell>{client.phone}</TableCell>
            <TableCell className="text-center">{client.totalBookings}</TableCell>
            <TableCell className="text-center">
              <div className="flex items-center justify-center">
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <svg
                      key={i}
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 24 24"
                      fill={i < Math.floor(client.rating) ? "currentColor" : "none"}
                      stroke="currentColor"
                      className={`w-4 h-4 ${i < Math.floor(client.rating) ? "text-yellow-500" : "text-gray-300"}`}
                    >
                      <path
                        fillRule="evenodd"
                        d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z"
                        clipRule="evenodd"
                      />
                    </svg>
                  ))}
                </div>
                <span className="ml-2">{client.rating}</span>
              </div>
            </TableCell>
            <TableCell>
              <Badge
                variant={client.status === "Active" ? "success" : "destructive"}
                className={
                  client.status === "Active"
                    ? "bg-green-100 text-green-800 hover:bg-green-100"
                    : "bg-red-100 text-red-800 hover:bg-red-100"
                }
              >
                {client.status}
              </Badge>
            </TableCell>
            <TableCell className="text-right">
              <div className="flex justify-end gap-2">
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <Eye className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <MessageSquare className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <UserCog className="h-4 w-4" />
                </Button>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem>
                      <Eye className="mr-2 h-4 w-4" />
                      <span>View Details</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <MessageSquare className="mr-2 h-4 w-4" />
                      <span>Send Message</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <UserCog className="mr-2 h-4 w-4" />
                      <span>Edit Profile</span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    {client.status === "Active" ? (
                      <DropdownMenuItem className="text-red-600">
                        <ShieldAlert className="mr-2 h-4 w-4" />
                        <span>Suspend Client</span>
                      </DropdownMenuItem>
                    ) : (
                      <DropdownMenuItem className="text-green-600">
                        <ShieldCheck className="mr-2 h-4 w-4" />
                        <span>Reactivate Client</span>
                      </DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}
