import { Card, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, Download } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

const payments = [
  {
    id: "PAY-001",
    amount: "$240.00",
    date: "2024-01-15",
    description: "Toyota Camry 2023 - 3 días",
    status: "completed",
    method: "Visa ****4242",
  },
  {
    id: "PAY-002",
    amount: "$160.00",
    date: "2024-01-01",
    description: "Honda Civic 2022 - 2 días",
    status: "completed",
    method: "PayPal",
  },
  {
    id: "PAY-003",
    amount: "$325.00",
    date: "2023-12-20",
    description: "Mazda CX-5 2023 - 5 días",
    status: "completed",
    method: "Mastercard ****8888",
  },
  {
    id: "PAY-004",
    amount: "$180.00",
    date: "2023-12-10",
    description: "Nissan Sentra 2023 - 3 días",
    status: "completed",
    method: "Visa ****4242",
  },
]

export function ClientPaymentHistory() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Historial de Pagos</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {payments.map((payment) => (
            <div key={payment.id} className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-3">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <div>
                  <p className="font-medium">{payment.amount}</p>
                  <p className="text-sm text-muted-foreground">{payment.description}</p>
                  <p className="text-xs text-muted-foreground">{payment.method}</p>
                </div>
              </div>
              <div className="text-right">
                <Badge className="bg-green-100 text-green-800 mb-2">Completado</Badge>
                <p className="text-sm text-muted-foreground">{payment.date}</p>
                <Button variant="ghost" size="sm" className="mt-1">
                  <Download className="h-4 w-4 mr-1" />
                  Factura
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
