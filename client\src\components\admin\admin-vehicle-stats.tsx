"use client"

import { useQuery } from "@tanstack/react-query"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Car } from "lucide-react"
import { Skeleton } from "@/components/ui/skeleton"
import { vehiclesApi } from "@/lib/api/vehicles.api"

export function AdminVehicleStats() {
  // Consulta independiente para las estadísticas
  const { data, isLoading } = useQuery({
    queryKey: ['admin-vehicles-stats'],
    queryFn: () => vehiclesApi.admin.getStats(),
    // Mantener los datos frescos por más tiempo para evitar cargas frecuentes
    staleTime: 5 * 60 * 1000, // 5 minutos
  })

  const stats = data?.stats || {
    total: 0,
    active: 0,
    rented: 0,
    maintenance: 0,
    pending: 0
  }

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-32 w-full" />
      </div>
    )
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Vehículos</CardTitle>
          <Car className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.total}</div>
          <p className="text-xs text-muted-foreground">+8% desde el mes pasado</p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Activos</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.active}</div>
          <p className="text-xs text-muted-foreground">Disponibles para renta</p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Rentados</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.rented}</div>
          <p className="text-xs text-muted-foreground">Actualmente en uso</p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Mantenimiento</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.maintenance}</div>
          <p className="text-xs text-muted-foreground">Fuera de servicio</p>
        </CardContent>
      </Card>
    </div>
  )
}