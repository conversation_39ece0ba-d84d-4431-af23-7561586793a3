import { Elysia, t } from 'elysia';
import { authMiddleware } from '@/app/middlewares/auth.middleware';
import { prisma } from '@/lib/prisma';
import { HttpException } from '@/exceptions/HttpExceptions';

const VALID_USER_TYPES = ['client', 'host'] as const;
type UserType = typeof VALID_USER_TYPES[number];

export const requestAdditionalRoleController = new Elysia({ prefix: '/user' })
  .use(authMiddleware)
  .post('/request-additional-role', async ({ body, user }) => {
    const { userType } = body as { userType: UserType };

    // Validar que el userType sea válido
    if (!VALID_USER_TYPES.includes(userType)) {
      throw HttpException.BadRequest('Invalid user type. Must be "client" or "host"');
    }

    // Obtener el usuario actual
    const currentUser = await prisma.user.findUnique({
      where: { id: user.id },
      select: {
        id: true,
        userType: true,
        availableUserTypes: true,
        isHostVerified: true,
      }
    });

    if (!currentUser) {
      throw HttpException.NotFound('User not found');
    }

    // Verificar que no tenga ya este tipo disponible
    if (currentUser.availableUserTypes.includes(userType)) {
      return {
        success: true,
        message: `You already have access to ${userType} role`,
        userType: currentUser.userType,
        availableUserTypes: currentUser.availableUserTypes,
        isHostVerified: currentUser.isHostVerified,
      };
    }

    // Agregar el nuevo tipo a los tipos disponibles
    const updatedAvailableTypes = [...currentUser.availableUserTypes, userType];

    // Actualizar el usuario con el nuevo tipo disponible
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: { 
        availableUserTypes: updatedAvailableTypes 
      },
      select: {
        id: true,
        userType: true,
        availableUserTypes: true,
        isHostVerified: true,
      }
    });

    return {
      success: true,
      message: `Successfully added ${userType} role to your account. ${userType === 'host' ? 'You can now complete host verification to access host features.' : 'You can now switch to client mode anytime.'}`,
      userType: updatedUser.userType,
      availableUserTypes: updatedUser.availableUserTypes,
      isHostVerified: updatedUser.isHostVerified,
      newRoleAdded: userType,
    };
  }, {
    body: t.Object({
      userType: t.String()
    }),
    detail: {
      tags: ['User'],
      summary: 'Request additional user role',
      description: 'Request access to an additional user role (client/host). Automatically approved.'
    }
  });
