import { context, trace } from '@opentelemetry/api';

export default function requestIdHandler({ response, set, ...rest }: {
    response: any;
    set: any;
}) {
    const span = trace.getSpan(context.active());
    const requestId = span?.spanContext().traceId || '';
    set.headers['X-Request-Id'] = requestId;

    // TO DO: save the response body and request body on database 
    // url, method, request body, response body, ip

    return response as any;
}