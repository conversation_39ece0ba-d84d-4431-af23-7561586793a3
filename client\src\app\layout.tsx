import "./globals.css";
import { cn } from "@/lib/utils";
import type { Metadata } from "next";
import { ReactScan } from "./react-scan";
import { Toaster } from "react-hot-toast";
import { Providers } from "@/providers/Providers";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Autoop",
  description: "Autoop",
};

export const runtime = 'edge';

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className='hydrated' suppressHydrationWarning>
      <body
        className={cn(
          `${geistSans.variable} ${geistMono.variable} antialiased`,
          "relative min-h-screen bg-background text-foreground flex flex-col",
        )}
      >
        <Toaster />
        <Providers>
          <ReactScan />
          {children}
        </Providers>
      </body>
    </html>
  );
}
