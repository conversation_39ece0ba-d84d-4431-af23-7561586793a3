import { prisma } from '@/lib/prisma';
import { ReviewNotificationService } from '../services/review-notification.service';

export interface CreateReviewData {
  reservationId: string;
  userId: string;
  rating: number;
  comment?: string;
}

export interface GetReviewsQuery {
  vehicleId?: string;
  userId?: string;
  page?: number;
  limit?: number;
  sortBy?: 'newest' | 'oldest' | 'rating_high' | 'rating_low';
}

export class ReviewsService {
  /**
   * Crear una nueva reseña
   */
  static async createReview(data: CreateReviewData) {
    try {
      // Validar elegibilidad
      const eligibility = await ReviewNotificationService.isReservationEligibleForReview(
        data.reservationId,
        data.userId
      );

      if (!eligibility.eligible) {
        return {
          success: false,
          error: eligibility.reason,
          status: 400
        };
      }

      const reservation = eligibility.reservation;

      // Validar rating si se proporciona
      if (data.rating !== undefined && (data.rating < 1 || data.rating > 5)) {
        return {
          success: false,
          error: 'La calificación debe estar entre 1 y 5 estrellas',
          status: 400
        };
      }

      // Crear la reseña
      const vehicleReview = await prisma.vehicleReview.create({
        data: {
          userId: data.userId,
          vehicleId: reservation.vehicle.id,
          reservationId: data.reservationId,
          rating: data.rating,
          comment: data.comment?.trim() || null,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              image: true,
            }
          },
          vehicle: {
            select: {
              id: true,
              make: true,
              model: true,
              year: true,
            }
          }
        }
      });

      // Actualizar estadísticas del vehículo
      await this.updateVehicleRatingStats(reservation.vehicle.id);

      return {
        success: true,
        data: vehicleReview
      };

    } catch (error) {
      console.error('Error creating review:', error);
      return {
        success: false,
        error: 'Error interno del servidor',
        status: 500
      };
    }
  }

  /**
   * Obtener reseñas con filtros y paginación
   */
  static async getReviews(query: GetReviewsQuery) {
    try {
      const {
        vehicleId,
        userId,
        page = 1,
        limit = 10,
        sortBy = 'newest'
      } = query;

      const skip = (page - 1) * limit;

      // Construir filtros
      const where: any = {};
      if (vehicleId) where.vehicleId = vehicleId;
      if (userId) where.userId = userId;

      // Construir ordenamiento
      let orderBy: any = {};
      switch (sortBy) {
        case 'newest':
          orderBy = { createdAt: 'desc' };
          break;
        case 'oldest':
          orderBy = { createdAt: 'asc' };
          break;
        case 'rating_high':
          orderBy = [{ rating: 'desc' }, { createdAt: 'desc' }];
          break;
        case 'rating_low':
          orderBy = [{ rating: 'asc' }, { createdAt: 'desc' }];
          break;
        default:
          orderBy = { createdAt: 'desc' };
      }

      // Obtener reseñas
      const [vehicleReviews, total] = await Promise.all([
        prisma.vehicleReview.findMany({
          where,
          orderBy,
          skip,
          take: limit,
          include: {
            user: {
              select: {
                id: true,
                name: true,
                image: true,
              }
            },
            vehicle: {
              select: {
                id: true,
                make: true,
                model: true,
                year: true,
              }
            }
          }
        }),
        prisma.vehicleReview.count({ where })
      ]);

      return {
        success: true,
        data: {
          reviews: vehicleReviews,
          pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit),
            hasNext: page * limit < total,
            hasPrev: page > 1,
          }
        }
      };

    } catch (error) {
      console.error('Error getting reviews:', error);
      return {
        success: false,
        error: 'Error interno del servidor',
        status: 500
      };
    }
  }

  /**
   * Obtener estadísticas de reseñas de un vehículo
   */
  static async getVehicleReviewStats(vehicleId: string) {
    try {
      const stats = await prisma.vehicleReview.aggregate({
        where: {
          vehicleId,
        },
        _avg: {
          rating: true
        },
        _count: {
          rating: true
        }
      });

      const ratingDistribution = await prisma.vehicleReview.groupBy({
        by: ['rating'],
        where: {
          vehicleId,
        },
        _count: {
          rating: true
        },
        orderBy: {
          rating: 'desc'
        }
      });

      return {
        success: true,
        data: {
          averageRating: stats._avg.rating || 0,
          totalReviews: stats._count.rating || 0,
          ratingDistribution: ratingDistribution.map(item => ({
            rating: item.rating,
            count: item._count.rating
          }))
        }
      };

    } catch (error) {
      console.error('Error getting vehicle review stats:', error);
      return {
        success: false,
        error: 'Error interno del servidor',
        status: 500
      };
    }
  }

  /**
   * Verificar elegibilidad para reseña
   */
  static async checkReviewEligibility(reservationId: string, userId: string) {
    try {
      const eligibility = await ReviewNotificationService.isReservationEligibleForReview(
        reservationId,
        userId
      );

      return {
        success: true,
        data: {
          eligible: eligibility.eligible,
          reason: eligibility.reason,
          reservation: eligibility.reservation ? {
            id: eligibility.reservation.id,
            startDate: eligibility.reservation.startDate,
            endDate: eligibility.reservation.endDate,
            vehicle: eligibility.reservation.vehicle,
          } : null
        }
      };

    } catch (error) {
      console.error('Error checking review eligibility:', error);
      return {
        success: false,
        error: 'Error interno del servidor',
        status: 500
      };
    }
  }

  /**
   * Actualizar estadísticas de rating del vehículo
   */
  static async updateVehicleRatingStats(vehicleId: string) {
    try {
      const stats = await prisma.vehicleReview.aggregate({
        where: {
          vehicleId,
        },
        _avg: {
          rating: true
        },
        _count: {
          rating: true
        }
      });

      await prisma.vehicle.update({
        where: { id: vehicleId },
        data: {
          averageRating: stats._avg.rating || 0,
          totalReviews: stats._count.rating || 0,
        }
      });

      console.log(`✅ Estadísticas actualizadas para vehículo ${vehicleId}: ${stats._avg.rating?.toFixed(1)} ⭐ (${stats._count.rating} reseñas)`);

    } catch (error) {
      console.error('Error updating vehicle rating stats:', error);
      // No lanzar error para no afectar la creación de la reseña
    }
  }

  /**
   * Obtener una reseña específica por ID
   */
  static async getReviewById(reviewId: string, userId?: string) {
    try {
      const vehicleReview = await prisma.vehicleReview.findUnique({
        where: { id: reviewId },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              image: true,
            }
          },
          vehicle: {
            select: {
              id: true,
              make: true,
              model: true,
              year: true,
            }
          },
          reservation: {
            select: {
              id: true,
              startDate: true,
              endDate: true,
            }
          }
        }
      });

      if (!vehicleReview) {
        return {
          success: false,
          error: 'Reseña no encontrada',
          status: 404
        };
      }

      // Si se proporciona userId, verificar que sea el propietario
      if (userId && vehicleReview.userId !== userId) {
        return {
          success: false,
          error: 'No tienes permisos para ver esta reseña',
          status: 403
        };
      }

      return {
        success: true,
        data: vehicleReview
      };

    } catch (error) {
      console.error('Error getting review by ID:', error);
      return {
        success: false,
        error: 'Error interno del servidor',
        status: 500
      };
    }
  }
}
