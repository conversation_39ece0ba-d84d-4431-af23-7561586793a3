"use client"

import * as React from "react"
import {
  type ColumnDef,
  // type ColumnFiltersState,
  // type SortingState,
  // type VisibilityState,
  flexRender,
  getCoreRowModel,
  // getFilteredRowModel,
  getPaginationRowModel,
  // getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { SkeletonRow } from '../ui/skeleton-row'
// import { Input } from "@/components/ui/input"
// import {
//   DropdownMenu,
//   DropdownMenuCheckboxItem,
//   DropdownMenuContent,
//   DropdownMenuTrigger,
// } from "@/components/ui/dropdown-menu"
// import { ChevronDown } from "lucide-react"

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[];
  rowCount: number;
  pageSize: number;
  pageIndex: number;
  onPageChange?: (page: number) => void;
  isLoading?: boolean;
}

export function DataTable<TData, TValue>({
  columns,
  data,
  rowCount,
  pageSize,
  pageIndex,
  onPageChange,
  isLoading,
}: DataTableProps<TData, TValue>) {
  'use no memo';
  const [pagination, setPagination] = React.useState({ pageIndex, pageSize })
  // console.log('rowCount', rowCount, data.length);
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    manualPagination: true,
    rowCount: rowCount || data.length,
    // pageCount is calculated automatically based on the rowCount and pageSize
    pageCount: Math.ceil(rowCount / pageSize),
    onPaginationChange: (updater) => {
      // if (isLoading) {
      //   setStateData([]); // Clear the data while loading
      // }
      const newPagination = typeof updater === 'function' ? updater(table.getState().pagination) : updater;
      setPagination(newPagination);
      if (onPageChange) {
        onPageChange(newPagination.pageIndex + 1);
      }
    },
    state: {
      pagination,
    },
  })

  return (
    // <div className="space-y-4">
    //   <div className="flex items-center justify-between">
    //     {searchKey && (
    //       <div className="flex items-center py-4">
    //         <Input
    //           placeholder={searchPlaceholder}
    //           value={(table.getColumn(searchKey)?.getFilterValue() as string) ?? ""}
    //           onChange={(event) => table.getColumn(searchKey)?.setFilterValue(event.target.value)}
    //           className="max-w-sm"
    //         />
    //       </div>
    //     )}
    //     <div className="flex items-center space-x-2 ml-auto">
    //       <DropdownMenu>
    //         <DropdownMenuTrigger asChild>
    //           <Button variant="outline" className="ml-auto">
    //             Columnas <ChevronDown className="ml-2 h-4 w-4" />
    //           </Button>
    //         </DropdownMenuTrigger>
    //         <DropdownMenuContent align="end">
    //           {table
    //             .getAllColumns()
    //             .filter((column) => column.getCanHide())
    //             .map((column) => {
    //               return (
    //                 <DropdownMenuCheckboxItem
    //                   key={column.id}
    //                   className="capitalize"
    //                   checked={column.getIsVisible()}
    //                   onCheckedChange={(value) => column.toggleVisibility(!!value)}
    //                 >
    //                   {column.id}
    //                 </DropdownMenuCheckboxItem>
    //               )
    //             })}
    //         </DropdownMenuContent>
    //       </DropdownMenu>
    //     </div>
    //   </div>
    //   <div className="rounded-md border">
    //     <Table>
    //       <TableHeader>
    //         {table.getHeaderGroups().map((headerGroup) => (
    //           <TableRow key={headerGroup.id}>
    //             {headerGroup.headers.map((header) => {
    //               return (
    //                 <TableHead key={header.id}>
    //                   {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
    //                 </TableHead>
    //               )
    //             })}
    //           </TableRow>
    //         ))}
    //       </TableHeader>
    //       <TableBody>
    //         {table.getRowModel().rows?.length ? (
    //           table.getRowModel().rows.map((row) => (
    //             <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
    //               {row.getVisibleCells().map((cell) => (
    //                 <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
    //               ))}
    //             </TableRow>
    //           ))
    //         ) : (
    //           <TableRow>
    //             <TableCell colSpan={columns.length} className="h-24 text-center">
    //               No se encontraron resultados.
    //             </TableCell>
    //           </TableRow>
    //         )}
    //       </TableBody>
    //     </Table>
    //   </div>
    //   <div className="flex items-center justify-end space-x-2 py-4">
    //     <div className="flex-1 text-sm text-muted-foreground">
    //       {table.getFilteredSelectedRowModel().rows.length} de {table.getFilteredRowModel().rows.length} fila(s)
    //       seleccionada(s).
    //     </div>
    //     <div className="space-x-2">
    //       <Button
    //         variant="outline"
    //         size="sm"
    //         onClick={() => table.previousPage()}
    //         disabled={!table.getCanPreviousPage()}
    //       >
    //         Anterior
    //       </Button>
    //       <Button variant="outline" size="sm" onClick={() => table.nextPage()} disabled={!table.getCanNextPage()}>
    //         Siguiente
    //       </Button>
    //     </div>
    //   </div>
    // </div>
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header) => {
                return (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                  </TableHead>
                )
              })}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {isLoading ? Array.from({ length: pageSize }).map((_, index) => (
            <SkeletonRow key={index} totalRows={columns.length} className="h-8 w-[100%]" />
          )) : (
            <>

                {table.getRowModel().rows?.length ? (
                  table.getPaginationRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id}>
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                      No results.
                    </TableCell>
                  </TableRow>
                )}
            </>
          )}
        </TableBody>
      </Table>
      <div className="flex items-center justify-end space-x-2 p-4">
        <div className="flex-1 text-sm text-muted-foreground">
          Página {table.getState().pagination.pageIndex + 1} de{" "}
          {table.getPageCount()}. Total: {rowCount || data.length} registros.
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  )
}
