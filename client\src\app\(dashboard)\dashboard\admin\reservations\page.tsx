"use client"

import { useState, useMemo } from "react"
import { useQuery } from "@tanstack/react-query"
import { Search, Filter, MoreHorizontal, Eye, Edit, X, Check, Info } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { reservationsApi } from "@/lib/api/reservations.api"
import { Skeleton } from "@/components/ui/skeleton"

const statusColors = {
  pending: "bg-yellow-100 text-yellow-800",
  confirmed: "bg-blue-100 text-blue-800",
  active: "bg-green-100 text-green-800",
  completed: "bg-gray-100 text-gray-800",
  cancelled: "bg-red-100 text-red-800",
}

const statusLabels = {
  pending: "Pendiente",
  confirmed: "Confirmada",
  active: "Activa",
  completed: "Completada",
  cancelled: "Cancelada",
}

export default function AdminReservationsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  
  const { data: reservations, isLoading } = useQuery({
    queryKey: ['admin-reservations'],
    queryFn: reservationsApi.admin.getReservations,
    staleTime: 60 * 1000, // 1 minuto
  })

  const filteredReservations = useMemo(() => {
    if (!reservations) return [];
    
    return reservations.filter(
      (reservation) =>
        reservation.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        reservation.vehicle.host.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        `${reservation.vehicle.make} ${reservation.vehicle.model}`.toLowerCase().includes(searchTerm.toLowerCase()),
    );
  }, [reservations, searchTerm]);

  // Calcular estadísticas excluyendo reservaciones personales
  const stats = useMemo(() => {
    if (!reservations) return {
      total: 0,
      pending: 0,
      active: 0,
      completed: 0,
    };
    
    // Filtrar reservaciones que no son personales para las estadísticas
    const clientReservations = reservations.filter(r => !r.isPersonalReservation);
    
    return {
      total: clientReservations.length,
      pending: clientReservations.filter((r) => r.status === "pending").length,
      active: clientReservations.filter((r) => r.status === "active").length,
      completed: clientReservations.filter((r) => r.status === "completed").length,
    };
  }, [reservations]);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-10 w-full" />
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-32 w-full" />
        </div>
        <Skeleton className="h-96 w-full" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Reservas</h1>
          <p className="text-muted-foreground">Supervisa y gestiona todas las reservas de la plataforma</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Reservas</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">Reservas de clientes</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pendientes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pending}</div>
            <p className="text-xs text-muted-foreground">Requieren aprobación</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Activas</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.active}</div>
            <p className="text-xs text-muted-foreground">En curso actualmente</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completadas</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.completed}</div>
            <p className="text-xs text-muted-foreground">Finalizadas con éxito</p>
          </CardContent>
        </Card>
      </div>

      {/* Reservations Table */}
      <Card>
        <CardHeader>
          <CardTitle>Lista de Reservas</CardTitle>
          <CardDescription>Administra todas las reservas y su estado actual</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Buscar reservas..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <Button variant="outline">
              <Filter className="mr-2 h-4 w-4" />
              Filtros
            </Button>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Cliente</TableHead>
                  <TableHead>Anfitrión</TableHead>
                  <TableHead>Vehículo</TableHead>
                  <TableHead>Fechas</TableHead>
                  <TableHead>Estado</TableHead>
                  <TableHead>Total</TableHead>
                  <TableHead>Comisión</TableHead>
                  <TableHead className="text-right">Acciones</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredReservations.map((reservation) => (
                  <TableRow 
                    key={reservation.id}
                    className={reservation.isPersonalReservation ? "bg-gray-50" : ""}
                  >
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <Avatar>
                          <AvatarImage src={reservation.user.image || "/placeholder.svg"} />
                          <AvatarFallback>
                            {reservation.user.name
                              .split(" ")
                              .map((n) => n[0])
                              .join("")}
                          </AvatarFallback>
                        </Avatar>
                        <div className="font-medium">
                          {reservation.user.name}
                          {reservation.isPersonalReservation && (
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger>
                                  <Badge variant="outline" className="ml-2 bg-blue-50">
                                    <Info className="h-3 w-3 mr-1" />
                                    Personal
                                  </Badge>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>Reserva personal del anfitrión</p>
                                  {reservation.reason && (
                                    <p className="text-xs mt-1">Razón: {reservation.reason}</p>
                                  )}
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{reservation.vehicle.host.name}</div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{reservation.vehicle.make} {reservation.vehicle.model}</div>
                      <div className="text-sm text-muted-foreground">{reservation.vehicle.year}</div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div>{new Date(reservation.startDate).toLocaleDateString()}</div>
                        <div className="text-muted-foreground">
                          {new Date(reservation.endDate).toLocaleDateString()}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={statusColors[reservation.status as keyof typeof statusColors]}>
                        {statusLabels[reservation.status as keyof typeof statusLabels]}
                      </Badge>
                    </TableCell>
                    <TableCell>${reservation.totalPrice.toLocaleString()}</TableCell>
                    <TableCell>
                      {reservation.isPersonalReservation ? (
                        <span className="text-muted-foreground">N/A</span>
                      ) : (
                        `$${(reservation.totalPrice * 0.1).toLocaleString()}`
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Abrir menú</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>
                            <Eye className="mr-2 h-4 w-4" />
                            Ver detalles
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Edit className="mr-2 h-4 w-4" />
                            Editar
                          </DropdownMenuItem>
                          {reservation.status === "pending" && (
                            <>
                              <DropdownMenuItem className="text-green-600">
                                <Check className="mr-2 h-4 w-4" />
                                Aprobar
                              </DropdownMenuItem>
                              <DropdownMenuItem className="text-red-600">
                                <X className="mr-2 h-4 w-4" />
                                Rechazar
                              </DropdownMenuItem>
                            </>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
