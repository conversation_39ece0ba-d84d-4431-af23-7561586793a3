import { prisma } from "@/lib/prisma";
import { HttpException } from "@/exceptions/HttpExceptions";

export class StatesService {
  // Obtener todos los estados activos
  static async getAll() {
    return await prisma.states.findMany({
      where: {
        active: true
      },
      orderBy: {
        name: 'asc'
      }
    });
  }

  // Obtener un estado por ID
  static async getById(id: string) {
    const state = await prisma.states.findUnique({
      where: { id }
    });

    if (!state) {
      throw HttpException.NotFound("State not found");
    }

    return state;
  }

  // Crear un nuevo estado
  static async create(data: any) {
    // Verificar si ya existe un estado con el mismo código
    const existingState = await prisma.states.findUnique({
      where: {
        code: data.code,
      }
    });

    if (existingState) {
      throw HttpException.BadRequest("A state with this code already exists");
    }

    return await prisma.states.create({
      data: {
        name: data.name,
        code: data.code,
        countryCode: data.countryCode || "mx",
        timezone: data.timezone || "America/Mexico_City",
        active: true
      }
    });
  }

  // Actualizar un estado
  static async update(id: string, data: any) {
    // Verificar si el estado existe
    const state = await prisma.states.findUnique({
      where: { id },
      select: { code: true }
    });

    if (!state) {
      throw HttpException.NotFound("State not found");
    }

    // Si se está actualizando el código, verificar que no exista otro estado con ese código
    if (data.code && data.code !== state.code) {
      const existingState = await prisma.states.findUnique({
        where: { code: data.code }
      });

      if (existingState) {
        throw HttpException.BadRequest("A state with this code already exists");
      }
    }

    return await prisma.states.update({
      where: { id },
      data
    });
  }

  // Activar/desactivar un estado
  static async toggleActive(id: string) {
    // Verificar si el estado existe
    const state = await prisma.states.findUnique({
      where: { id },
      select: { active: true }
    });

    if (!state) {
      throw HttpException.NotFound("State not found");
    }

    return await prisma.states.update({
      where: { id },
      data: {
        active: !state.active
      }
    });
  }

  // Eliminar un estado
  static async delete(id: string) {
    // Verificar si el estado existe
    const state = await prisma.states.findUnique({
      where: { id }
    });

    if (!state) {
      throw HttpException.NotFound("State not found");
    }

    // Verificar si hay vehículos asociados a este estado
    const vehiclesCount = await prisma.vehicle.count({
      where: {
        state_code: state.code
      }
    });

    if (vehiclesCount > 0) {
      throw HttpException.BadRequest(`Cannot delete state: ${vehiclesCount} vehicles are associated with it`);
    }

    return await prisma.states.delete({
      where: { id }
    });
  }
}