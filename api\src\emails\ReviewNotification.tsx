import * as React from 'react';
import {
  Html,
  Head,
  Body,
  Container,
  Section,
  Text,
  Button,
  Img,
  Hr,
  Link,
} from '@react-email/components';

interface ReviewNotificationEmailProps {
  userName: string;
  vehicleMake: string;
  vehicleModel: string;
  reviewUrl: string;
}

export default function ReviewNotificationEmail({
  userName = 'Usuario',
  vehicleMake = 'Toyota',
  vehicleModel = 'Corolla',
  reviewUrl = 'https://autoop.com/review/123',
}: ReviewNotificationEmailProps) {
  return (
    <Html>
      <Head />
      <Body style={main}>
        <Container style={container}>
          {/* Header */}
          <Section style={header}>
            <Img
              src="https://autoop.com/logo.png"
              width="120"
              height="40"
              alt="Autoop"
              style={logo}
            />
          </Section>

          {/* Main Content */}
          <Section style={content}>
            <Text style={title}>
              ¡Hola {userName}! 👋
            </Text>

            <Text style={paragraph}>
              Esperamos que hayas tenido una excelente experiencia con el{' '}
              <strong>{vehicleMake} {vehicleModel}</strong> que rentaste recientemente.
            </Text>

            <Text style={paragraph}>
              Tu opinión es muy valiosa para nosotros y para otros usuarios de Autoop.
              ¿Te gustaría compartir tu experiencia dejando una reseña?
            </Text>

            {/* CTA Button */}
            <Section style={buttonContainer}>
              <Button style={button} href={reviewUrl}>
                Dejar mi reseña ⭐
              </Button>
            </Section>

            <Text style={smallText}>
              O copia y pega este enlace en tu navegador:
            </Text>
            <Link href={reviewUrl} style={link}>
              {reviewUrl}
            </Link>

            <Hr style={hr} />

            <Text style={paragraph}>
              <strong>¿Qué puedes incluir en tu reseña?</strong>
            </Text>
            <Text style={listItem}>• Calificación de 1 a 5 estrellas (opcional)</Text>
            <Text style={listItem}>• Comentarios sobre el vehículo</Text>
            <Text style={listItem}>• Tu experiencia con el anfitrión</Text>
            <Text style={listItem}>• Cualquier detalle que ayude a otros usuarios</Text>

            <Text style={smallText}>
              <em>Recuerda que tanto la calificación como los comentarios son completamente opcionales.</em>
            </Text>

            <Hr style={hr} />

            <Text style={paragraph}>
              Si no deseas dejar una reseña, simplemente ignora este mensaje.
              Este enlace expirará en 30 días.
            </Text>

            <Text style={paragraph}>
              ¡Gracias por elegir Autoop! 🚗
            </Text>
          </Section>

          {/* Footer */}
          <Section style={footer}>
            <Text style={footerText}>
              © 2024 Autoop. Todos los derechos reservados.
            </Text>
            <Text style={footerText}>
              Este email fue enviado porque completaste una reserva en Autoop.
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
}

// Estilos
const main = {
  backgroundColor: '#f6f9fc',
  fontFamily: '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px 0 48px',
  marginBottom: '64px',
  maxWidth: '600px',
};

const header = {
  padding: '20px 30px',
  backgroundColor: '#1a73e8',
  borderRadius: '8px 8px 0 0',
};

const logo = {
  margin: '0 auto',
  display: 'block',
};

const content = {
  padding: '30px',
};

const title = {
  fontSize: '24px',
  fontWeight: 'bold',
  color: '#1a1a1a',
  margin: '0 0 20px',
};

const paragraph = {
  fontSize: '16px',
  lineHeight: '1.6',
  color: '#333333',
  margin: '0 0 16px',
};

const buttonContainer = {
  textAlign: 'center' as const,
  margin: '32px 0',
};

const button = {
  backgroundColor: '#1a73e8',
  borderRadius: '8px',
  color: '#ffffff',
  fontSize: '16px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '14px 28px',
  border: 'none',
  cursor: 'pointer',
};

const link = {
  color: '#1a73e8',
  textDecoration: 'underline',
  fontSize: '14px',
  wordBreak: 'break-all' as const,
};

const smallText = {
  fontSize: '14px',
  color: '#666666',
  margin: '8px 0',
  textAlign: 'center' as const,
};

const listItem = {
  fontSize: '14px',
  color: '#333333',
  margin: '4px 0',
  paddingLeft: '8px',
};

const hr = {
  borderColor: '#e6ebf1',
  margin: '24px 0',
};

const footer = {
  padding: '20px 30px',
  backgroundColor: '#f8f9fa',
  borderRadius: '0 0 8px 8px',
};

const footerText = {
  fontSize: '12px',
  color: '#666666',
  textAlign: 'center' as const,
  margin: '4px 0',
};
