import { apiService } from '@/services/api';

export interface VerificationStatus {
  data: {
    isVerified: boolean;
    verification: {
      status: string;
      notes?: string;
      idFront?: string;
      idBack?: string;
      driverLicense?: string;
      addressProof?: string;
      selfieWithId?: string;
    };
  };
}

export interface UserVerification {
  id: string;
  userId: string;
  status: string;
  createdAt: string;
  user: {
    id: string;
    name: string;
    email: string;
    image?: string;
  };
  idFront?: string;
  idBack?: string;
  driverLicense?: string;
  addressProof?: string;
  selfieWithId?: string;
}

export interface UserVerificationResponse {
  data: UserVerification[];
  pagination: Pagination;
}

export const verificationApi = {
  host: {
    // Obtener estado de verificación
    getStatus: async () => {
      const result = await apiService.get<VerificationStatus>('/host/verification/status');
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },

    // Subir documentos de verificación
    uploadDocuments: async (formData: FormData) => {
      const result = await apiService.post('/host/verification/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },
    editDocuments: async (formData: FormData) => {
      const result = await apiService.put('/host/verification/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },
  },

  admin: {

    // Obtener verificaciones pendientes
    getPendingVerifications: async () => {
      const result = await apiService.get<UserVerificationResponse>('/admin/host-verification/pending');
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },

    // Aprobar verificación
    approveVerification: async (userId: string) => {
      const result = await apiService.post(`/admin/host-verification/${userId}/approve`);
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },

    // Rechazar verificación
    rejectVerification: async (userId: string, notes: string) => {
      const result = await apiService.post(`/admin/host-verification/${userId}/reject`, { notes });
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },
  }

};