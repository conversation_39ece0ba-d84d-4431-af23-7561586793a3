"use client"
import { useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import Image from "next/image"
import { ArrowLeft, Calendar, Check, CheckCircle, Info, Mail, MapPin, Share, User, X, XCircle } from "lucide-react"
import Swal from "sweetalert2"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Separator } from "@/components/ui/separator"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { toast } from "sonner"
import { vehiclesApi } from '@/lib/api/vehicles.api'
import { formatISODate } from '@/lib/utils'
// import { vehiclesApi } from "@/api/vehicles"
import VehicleDocuments from "@/components/vehicles/vehicle-documents";

// Definir colores y etiquetas para los estados
const statusColors: Record<string, string> = {
  pending: "bg-yellow-100 text-yellow-800 hover:bg-yellow-100",
  active: "bg-green-100 text-green-800 hover:bg-green-100",
  rejected: "bg-red-100 text-red-800 hover:bg-red-100",
  inactive: "bg-gray-100 text-gray-800 hover:bg-gray-100"
}

const statusLabels: Record<string, string> = {
  pending: "Pendiente",
  active: "Activo",
  rejected: "Rechazado",
  inactive: "Inactivo"
}

// Añadir estas constantes al principio del archivo, después de las importaciones
const featureLabels: Record<string, string> = {
  rules: "Reglas",
  seats: "Asientos",
  mileage: "Kilometraje",
  fuelType: "Tipo de Combustible",
  location: "Ubicación",
  insurancePolicy: "Póliza de Seguro",
  registrationNumber: "Número de Registro",
  weeklyRate: "Tarifa Semanal",
  monthlyRate: "Tarifa Mensual"
};

const fuelTypeTranslations: Record<string, string> = {
  gasoline: "Gasolina",
  diesel: "Diésel",
  electric: "Eléctrico",
  hybrid: "Híbrido",
  gas: "Gas"
};

export default function VehicleDetailsPage() {

  const params = useParams<{ id: string }>()
  const router = useRouter()
  const queryClient = useQueryClient()
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false)
  const [rejectReason, setRejectReason] = useState("")

  const { data: vehicle, isLoading, error } = useQuery({
    queryKey: ['admin-vehicle', params.id],
    queryFn: () => vehiclesApi.admin.getById(params.id as string),
  })

  // Mutación para aprobar vehículo
  const approveMutation = useMutation({
    mutationFn: vehiclesApi.admin.approveVehicle,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-vehicle', params.id] })
      toast.success("Vehículo aprobado correctamente")
    },
    onError: (error) => {
      toast.error(`Error al aprobar vehículo: ${error.message}`)
    }
  })

  // Mutación para rechazar vehículo
  const rejectMutation = useMutation({
    mutationFn: ({ id, reason }: { id: string, reason: string }) =>
      vehiclesApi.admin.rejectVehicle(id, reason),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-vehicle', params.id] })
      toast.success("Vehículo rechazado correctamente")
      setIsRejectDialogOpen(false)
      setRejectReason("")
    },
    onError: (error) => {
      toast.error(`Error al rechazar vehículo: ${error.message}`)
    }
  })

  // Manejar aprobación de vehículo con confirmación
  const handleApprove = () => {
    Swal.fire({
      title: '¿Estás seguro?',
      text: `¿Deseas aprobar el vehículo ${vehicle?.make} ${vehicle?.model}?`,
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#10b981', // Color verde
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Sí, aprobar',
      cancelButtonText: 'Cancelar'
    }).then((result) => {
      if (result.isConfirmed) {
        approveMutation.mutate(params.id as string)
      }
    })
  }

  // Manejar rechazo de vehículo
  const handleReject = () => {
    if (!rejectReason.trim()) {
      toast.error("Debe proporcionar una razón para el rechazo")
      return
    }

    rejectMutation.mutate({
      id: params.id as string,
      reason: rejectReason
    })
  }

  // Abrir diálogo de rechazo
  const openRejectDialog = () => {
    setIsRejectDialogOpen(true)
  }

  if (isLoading) {
    return (
      <div className="container mx-auto py-6 flex flex-col items-center justify-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        <p className="mt-4 text-muted-foreground">Cargando información del vehículo...</p>
      </div>
    )
  }

  if (error || !vehicle) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center mb-6">
          <Button variant="ghost" onClick={() => router.back()} className="mr-4">
            <ArrowLeft className="mr-2 h-4 w-4" /> Volver
          </Button>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-10">
              <h2 className="text-2xl font-bold mb-2">Vehículo no encontrado</h2>
              <p className="text-muted-foreground">El vehículo que buscas no existe o ha sido eliminado.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  const isRejected = vehicle.status === 'rejected'
  const rejection = vehicle.approvalHistory?.reverse().find((item: any) => item.action === 'rejected')

  return (
    <div className="container mx-auto">
      <div className="flex items-center justify-between mb-6 flex-wrap">
        <div className="flex items-center">
          <Button variant="ghost" onClick={() => router.back()} className="mr-4">
            <ArrowLeft className="mr-2 h-4 w-4" /> Volver
          </Button>
          <h1 className="text-2xl font-bold hidden sm:flex">Detalles del Vehículo</h1>
        </div>
        <div className="flex items-center space-x-2">
          <h1 className="text-2xl font-bold sm:hidden">Detalles del Vehículo</h1>
          <Badge className={statusColors[vehicle.status] || "bg-gray-100"}>
            {statusLabels[vehicle.status] || "Desconocido"}
          </Badge>
        </div>
      </div>

      {/* Sección de rechazo con Popover para soporte móvil */}
      {isRejected && rejection && (
        <div className="my-4 border border-red-200 rounded-lg overflow-hidden bg-white shadow-sm">
          <div className="p-4 bg-red-50 border-b border-red-200">
            <div className="flex items-center">
              <h3 className="text-lg font-semibold text-red-700">
                Vehículo Rechazado por{' '}
                <span className="font-bold">{rejection?.user?.name}</span>{' '}
                <Popover>
                  <PopoverTrigger asChild>
                    <span className="text-sm text-red-500 underline decoration-dotted cursor-pointer">
                      (ver detalles)
                    </span>
                  </PopoverTrigger>
                  <PopoverContent className="w-80 p-3">
                    <div className="space-y-2">
                      <h4 className="font-medium">Detalles del rechazo</h4>
                      <div className="space-y-1 text-sm">
                        <p><strong>Email:</strong> {rejection?.user?.email}</p>
                        <p><strong>Fecha:</strong> {new Date(rejection?.date).toLocaleDateString()}</p>
                        <p><strong>Hora:</strong> {new Date(rejection?.date).toLocaleTimeString()}</p>
                      </div>
                    </div>
                  </PopoverContent>
                </Popover>
              </h3>
            </div>
            <p className="text-sm text-red-700 mt-1">
              Este vehículo fue rechazado por el siguiente motivo:
            </p>
          </div>
          <div className="p-4">
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-800">{rejection?.reason || "No se proporcionó un motivo específico."}</p>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Información General</CardTitle>
              <CardDescription>Detalles básicos del vehículo</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="font-medium text-sm text-muted-foreground mb-1">Marca y Modelo</h3>
                  <p className="text-lg">{vehicle.make} {vehicle.model}</p>
                </div>
                <div>
                  <h3 className="font-medium text-sm text-muted-foreground mb-1">Año</h3>
                  <p className="text-lg">{vehicle.year}</p>
                </div>
                <div>
                  <h3 className="font-medium text-sm text-muted-foreground mb-1">Color</h3>
                  <p className="text-lg">{vehicle.color}</p>
                </div>
                <div>
                  <h3 className="font-medium text-sm text-muted-foreground mb-1">Placa</h3>
                  <p className="text-lg">{vehicle.plate}</p>
                </div>
                <div>
                  <h3 className="font-medium text-sm text-muted-foreground mb-1">VIN</h3>
                  <p className="text-lg">{vehicle.vin}</p>
                </div>
                <div>
                  <h3 className="font-medium text-sm text-muted-foreground mb-1">Precio por día</h3>
                  <p className="text-lg">${vehicle.price}</p>
                </div>
              </div>

              <Separator className="my-6" />

              <div>
                <h3 className="font-medium text-sm text-muted-foreground mb-2">Descripción</h3>
                <p>{vehicle.description}</p>
              </div>

              <Separator className="my-6" />

              {
                vehicle.features && (
                <div>
                  <h3 className="font-medium text-sm text-muted-foreground mb-2">Características</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {Object.entries(vehicle.features).map(([key, value]) => {
                        // Ignorar campos que ya se muestran en otras secciones
                        if (key === 'weeklyRate' || key === 'monthlyRate') return null;

                        // Usar etiquetas en español
                        const label = featureLabels[key] || key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());

                        // Traducir valores específicos
                        let displayValue = value;
                        if (key === 'fuelType' && typeof value === 'string') {
                          displayValue = fuelTypeTranslations[value.toLowerCase()] || value;
                        }

                        return (
                          <div key={key} className="p-3 bg-gray-50 rounded-lg">
                            <h4 className="text-sm font-medium text-muted-foreground mb-1">{label}</h4>
                            <p className="font-medium">
                              {typeof displayValue === 'boolean'
                                ? (displayValue ? <span className="flex items-center"><Check className="h-4 w-4 mr-2 text-green-500" /> Sí</span>
                                  : <span className="flex items-center"><X className="h-4 w-4 mr-2 text-red-500" /> No</span>)
                                : typeof displayValue === 'string' && displayValue.length > 100
                                  ? `${displayValue.substring(0, 100)}...`
                                  : String(displayValue)}
                            </p>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )
              }
            </CardContent>
          </Card>

          {/* Información del Propietario */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Información del Propietario</CardTitle>
              <CardDescription>Datos del anfitrión que registró este vehículo</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start">
                  <User className="h-5 w-5 mr-2 text-muted-foreground" />
                  <div>
                    <h4 className="font-medium">Nombre</h4>
                    <p>{vehicle.host?.name || "No disponible"}</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <Mail className="h-5 w-5 mr-2 text-muted-foreground" />
                  <div>
                    <h4 className="font-medium">Email</h4>
                    <p>{vehicle.host?.email || "No disponible"}</p>
                  </div>
                </div>
                {/* <div className="flex items-start">
                  <Phone className="h-5 w-5 mr-2 text-muted-foreground" />
                  <div>
                    <h4 className="font-medium">Teléfono</h4>
                    <p>{vehicle.host?.phone || "No disponible"}</p>
                  </div>
                </div> */}
              </div>
            </CardContent>
          </Card>

          {/* Detalles Adicionales */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Detalles Adicionales</CardTitle>
              <CardDescription>Información complementaria del vehículo</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-start">
                  <MapPin className="h-5 w-5 mr-2 text-muted-foreground" />
                  <div>
                    <h4 className="font-medium">Ubicación</h4>
                    {/* <p>{vehicle.location?.address || "No especificada"}</p>
                    {vehicle.location?.city && vehicle.location?.state && (
                      <p className="text-sm text-muted-foreground">
                        {vehicle.location.city}, {vehicle.location.state}
                      </p>
                    )} */}
                    <p>{vehicle.features?.location || "No especificada"}</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <Info className="h-5 w-5 mr-2 text-muted-foreground" />
                  <div>
                    <h4 className="font-medium">Tipo de Vehículo</h4>
                    <p>{vehicle.bodyType || "No especificado"}</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <Calendar className="h-5 w-5 mr-2 text-muted-foreground" />
                  <div>
                    <h4 className="font-medium">Fecha de Registro</h4>
                    {/* <p>{vehicle.createdAt ? new Date(vehicle.createdAt).toLocaleDateString() : "No disponible"}</p> */}
                    <p>{vehicle.createdAt ? formatISODate(vehicle.createdAt) : "No disponible"}</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <Calendar className="h-5 w-5 mr-2 text-muted-foreground" />
                  <div>
                    <h4 className="font-medium">Última Actualización</h4>
                    {/* <p>{vehicle.updatedAt ? new Date(vehicle.updatedAt).toLocaleDateString() : "No disponible"}</p> */}
                    <p>{vehicle.updatedAt ? formatISODate(vehicle.updatedAt) : "No disponible"}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Historial de Aprobaciones */}
          {vehicle.approvalHistory && vehicle.approvalHistory.length > 0 && (
            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Info className="h-5 w-5 mr-2" />
                  Historial de Aprobaciones
                </CardTitle>
                <CardDescription>Registro de cambios de estado del vehículo</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {vehicle.approvalHistory.map((item: any, index: number) => (
                    <div key={index} className="border-l-2 border-gray-200 pl-4 pb-2">
                      <div className="flex justify-between">
                        <div>
                          <span className="font-medium">
                            {item.action === 'approved' ? 'Aprobado' :
                              item.action === 'rejected' ? 'Rechazado' :
                                item.action === 'created' ? 'Creado' : 'Actualizado'}
                          </span> por <span className="font-medium">{item.user?.name || 'Sistema'}</span>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {new Date(item.date).toLocaleDateString()} {new Date(item.date).toLocaleTimeString()}
                        </div>
                      </div>
                      {item.reason && (
                        <p className="text-sm mt-1">{item.reason}</p>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <div>
          {/* Acciones - Agregado botón para ver página pública */}
          <Card>
            <CardHeader>
              <CardTitle>Acciones</CardTitle>
            </CardHeader>
            <CardContent className="flex flex-col gap-4">
              {vehicle.status === 'pending' || vehicle.status === 'rejected' && (
                <>
                  <Button 
                    className="w-full bg-green-600 hover:bg-green-700" 
                    onClick={handleApprove}
                    disabled={approveMutation.isPending}
                  >
                    <CheckCircle className="mr-2 h-4 w-4" /> Aprobar Vehículo
                  </Button>
                </>
              )}
              {
                vehicle.status === 'pending' && (
                  <Button
                    className="w-full"
                    variant="destructive"
                    onClick={openRejectDialog}
                    disabled={rejectMutation.isPending}
                  >
                    <XCircle className="mr-2 h-4 w-4" /> Rechazar Vehículo
                  </Button>
                )
              }
              {/* <Button className="w-full" variant="outline" onClick={() => router.push(`/dashboard/admin/vehicles/${vehicle.id}/edit`)}>
                <Edit className="mr-2 h-4 w-4" /> Editar Vehículo
              </Button> */}
              {/* <Button className="w-full" variant="outline" onClick={() => router.push(`/dashboard/admin/vehicles/${vehicle.id}/calendar`)}>
                <Calendar className="mr-2 h-4 w-4" /> Ver Calendario
              </Button> */}
              <Button
                className="w-full"
                variant="outline"
                onClick={() => window.open(`/vehicles/${vehicle.id}`, '_blank')}
              >
                <Share className="mr-2 h-4 w-4" /> Ver Página Pública
              </Button>
            </CardContent>
          </Card>

          {/* Imágenes */}
          {vehicle.images && vehicle.images.length > 0 && (
            <Card className="mt-6">
              <CardHeader>
                <CardTitle>Imágenes</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-2">
                  {vehicle.images.map((image, index) => (
                    <div key={index} className="relative h-48 rounded-md overflow-hidden">
                      <Image
                        src={image}
                        alt={`${vehicle.make} ${vehicle.model}`}
                        fill
                        className="object-cover"
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Información de Precios */}
          <Card className="my-6">
            <CardHeader>
              <CardTitle>Información de Precios</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <h4 className="font-medium">Precio base</h4>
                  <p className="text-xl font-bold">${vehicle.price}/día</p>
                </div>

                <div>
                  <h4 className="font-medium">Tarifa semanal</h4>
                  <p className="text-xl font-bold">${vehicle.features?.weeklyRate}/semana</p>
                </div>

                <div>
                  <h4 className="font-medium">Tarifa mensual</h4>
                  <p className="text-xl font-bold">${vehicle.features?.monthlyRate}/mes</p>
                </div>

                {/* {vehicle.documents && (
                  <div>
                    <h4 className="font-medium">Documentación</h4>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {vehicle.documents.insurance ? (
                        <Badge className="bg-green-100 text-green-800">Seguro ✓</Badge>
                      ) : (
                        <Badge variant="outline">Sin seguro</Badge>
                      )}
                      {vehicle.documents.registration ? (
                        <Badge className="bg-green-100 text-green-800">Registro ✓</Badge>
                      ) : (
                        <Badge variant="outline">Sin registro</Badge>
                      )}
                    </div>
                  </div>
                )} */}
              </div>
            </CardContent>
          </Card>

          <VehicleDocuments vehicleId={vehicle.id} />
        </div>
      </div>

      {/* Diálogo de rechazo */}
      <Dialog open={isRejectDialogOpen} onOpenChange={setIsRejectDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Rechazar Vehículo</DialogTitle>
            <DialogDescription>
              Por favor, proporciona una razón para el rechazo. Esta información será enviada al host.
            </DialogDescription>
          </DialogHeader>
          <Textarea
            placeholder="Razón del rechazo..."
            value={rejectReason}
            onChange={(e) => setRejectReason(e.target.value)}
            rows={4}
          />
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsRejectDialogOpen(false)}>
              Cancelar
            </Button>
            <Button variant="destructive" onClick={handleReject} disabled={rejectMutation.isPending}>
              Rechazar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}




