"use client"

import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { formatDate } from "@/lib/utils"
import { MoreHorizontal, CheckCircle } from "lucide-react"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { usersApi } from "@/lib/api/users.api"
import { toast } from "sonner"
import { DataTable } from "@/components/data-table/data-table"
import { ColumnDef } from "@tanstack/react-table"
import { useSearchParams, useRouter } from "next/navigation"


export function HostTable() {
  const queryClient = useQueryClient()
  const searchParams = useSearchParams()
  const router = useRouter()

  // Obtener parámetros de paginación
  const page = Number(searchParams.get('page') || 1)
  const limit = Number(searchParams.get('limit') || 10)

  const { data, isLoading, error } = useQuery({
    queryKey: ['admin-hosts', page, limit],
    queryFn: () => usersApi.getAllHosts({ page, limit }),
    staleTime: 60 * 1000, // 1 minuto
  })

  const verifyMutation = useMutation({
    mutationFn: usersApi.verifyUser,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-hosts', page, limit] })
      toast.success("Host verificado correctamente")
    },
    onError: (error) => {
      toast.error(`Error al verificar host: ${error.message}`)
    }
  })

  const handlePageChange = (newPage: number) => {
    const params = new URLSearchParams(searchParams.toString())
    params.set('page', newPage.toString())
    router.push(`?${params.toString()}`)
  }

  if (error) {
    return <div className="p-4 text-red-500">Error al cargar los hosts: {error.message}</div>
  }

  // Definir las columnas para el DataTable
  const columns: ColumnDef<any>[] = [
    {
      accessorKey: "name",
      header: "Anfitrión",
      cell: ({ row }) => {
        const host = row.original
        return (
          <div className="flex items-center space-x-3">
            <Avatar>
              <AvatarImage src={host.image || "/placeholder.svg"} />
              <AvatarFallback>
                {host.name
                  ?.split(" ")
                  .map((n: string) => n[0])
                  .join("")}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="font-medium">{host.name}</div>
              <div className="text-sm text-muted-foreground">ID: {host.id.substring(0, 8)}</div>
            </div>
          </div>
        )
      },
    },
    {
      accessorKey: "email",
      header: "Contacto",
      cell: ({ row }) => {
        const host = row.original
        return (
          <div>
            <div>{host.email}</div>
            <div className="text-sm text-muted-foreground">{host.phone || "No disponible"}</div>
          </div>
        )
      },
    },
    {
      accessorKey: "isHostVerified",
      header: "Estado",
      cell: ({ row }) => {
        const host = row.original
        return (
          <Badge variant={host.isHostVerified ? "default" : "outline"}>
            {host.isHostVerified ? "Verificado" : "Pendiente"}
          </Badge>
        )
      },
    },
    {
      accessorKey: "createdAt",
      header: "Fecha de registro",
      cell: ({ row }) => {
        const host = row.original
        return formatDate(host.createdAt)
      },
    },
    {
      id: "actions",
      header: "Acciones",
      cell: ({ row }) => {
        const host = row.original
        return (
          <div className="text-right">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Abrir menú</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => verifyMutation.mutate(host.id)}>
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Verificar
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )
      },
    },
  ]

  return (
    <DataTable
      columns={columns}
      data={data?.data || []}
      rowCount={data?.pagination.total || 0}
      pageSize={limit}
      pageIndex={page - 1}
      onPageChange={handlePageChange}
      isLoading={isLoading}
    />
  )
}
