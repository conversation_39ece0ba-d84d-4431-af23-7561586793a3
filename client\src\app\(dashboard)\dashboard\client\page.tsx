import { ClientDashboardStats } from "@/components/client/client-dashboard-stats"
import { ClientRecentActivity } from "@/components/client/client-recent-activity"
import { ClientUpcomingReservations } from "@/components/client/client-upcoming-reservations"

export default function ClientDashboard() {
  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-2">
        <h1 className="text-2xl font-bold">Mi Dashboard</h1>
        <p className="text-muted-foreground">Bienvenido de vuelta. Encuentra tu próximo vehículo perfecto.</p>
      </div>

      <ClientDashboardStats />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ClientUpcomingReservations />
        <ClientRecentActivity />
      </div>

    </div>
  )
}
