'use client';

import { useQuery } from '@tanstack/react-query';
import { verificationApi } from '@/lib/api/user-verification.api';
import { UserVerificationForm } from '@/components/user/verification-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { CheckCircle, Clock, AlertCircle } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { usePathname, useRouter } from 'next/navigation';
import { UserVerificationEditForm } from '@/components/user/user-verification-edit-form';
import { VerificationDocumentsPreview } from '@/components/user/verification-documents-preview';
import { useUser } from '@/context/user-context';

export default function UserVerificationClientPage() {
  const { data, isLoading } = useQuery({
    queryKey: ['userVerification'],
    queryFn: verificationApi.user.getStatus,
  });
  const { user } = useUser();
  const verificationStatus = data?.data;
  const pathname = usePathname();
  const isClient = pathname.includes('client');

  const isVerified = user?.isHostVerified && verificationStatus?.verification?.status === 'approved';

  const router = useRouter();

  const isUnverified = verificationStatus?.verification?.status === 'unverified';

  if (isLoading) {
    return (
      <div className="container py-10">
        <Skeleton className="h-12 w-3/4 mb-4" />
        <Skeleton className="h-6 w-1/2 mb-8" />
        <Skeleton className="h-[400px] w-full rounded-lg" />
      </div>
    );
  }

  const status = verificationStatus?.verification?.status || 'unverified';

  return (
    <div className="container py-10">
      <h1 className="text-3xl font-bold mb-6">Verificación de identidad</h1>

      {status === 'approved' && (
        <Alert className="mb-8 bg-green-50 border-green-200">
          <CheckCircle className="h-5 w-5 text-green-600" />
          <AlertTitle className="text-green-800">Verificación aprobada</AlertTitle>
          <AlertDescription className="text-green-700">
            Tu identidad ha sido verificada. Ya puedes registrar vehículos en la plataforma.
          </AlertDescription>
        </Alert>
      )}

      {status === 'pending' && (
        <Alert className="mb-8 bg-yellow-50 border-yellow-200">
          <Clock className="h-5 w-5 text-yellow-600" />
          <AlertTitle className="text-yellow-800">Verificación en proceso</AlertTitle>
          <AlertDescription className="text-yellow-700">
            Tus documentos están siendo revisados. Te notificaremos cuando la verificación esté completa.
          </AlertDescription>
        </Alert>
      )}

      {status === 'rejected' && (
        <Alert className="mb-8 bg-red-50 border-red-200">
          <AlertCircle className="h-5 w-5 text-red-600" />
          <AlertTitle className="text-red-800">Verificación rechazada</AlertTitle>
          <AlertDescription className="text-red-700">
            {verificationStatus?.verification?.notes || 'Tu verificación fue rechazada. Por favor, sube nuevos documentos.'}
          </AlertDescription>
        </Alert>
      )}

      {/* Mostrar documentos existentes si los hay */}
      {verificationStatus?.verification && isVerified && (
        <VerificationDocumentsPreview verification={verificationStatus.verification} />
      )}

      {(status === 'unverified' || status === 'rejected') && isUnverified && (
        <UserVerificationForm />
      )}
      {(status === 'unverified' || status === 'rejected') && !isUnverified && verificationStatus?.verification && (
        <UserVerificationEditForm
          verification={verificationStatus?.verification}
        />
      )}


      {status === 'pending' && (
        <Card>
          <CardHeader>
            <CardTitle>Verificación en proceso</CardTitle>
            <CardDescription>
              Estamos revisando tus documentos. Este proceso puede tomar hasta 24 horas.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              Si tienes alguna pregunta sobre el proceso de verificación, por favor contacta a nuestro equipo de soporte.
            </p>
          </CardContent>
        </Card>
      )}

      {status === 'approved' && (
        <Card>
          <CardHeader>
            <CardTitle>¡Felicidades!</CardTitle>
            <CardDescription>
              Tu cuenta ha sido verificada. Ya puedes comenzar a registrar vehículos en la plataforma.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => {
              if (isClient) {
                router.push('/dashboard/client/vehicles/new');
              } else {
                router.push('/dashboard/host/vehicles/new');
              }
            }}>
              Registrar mi primer vehículo
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}