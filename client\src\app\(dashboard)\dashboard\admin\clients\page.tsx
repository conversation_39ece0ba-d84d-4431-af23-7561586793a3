"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ClientTable } from "@/components/clients/client-table"

export default function AdminClientsPage() {
  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-2">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Gestión de Clientes</h1>
            <p className="text-muted-foreground">Ver, verificar y gestionar cuentas de clientes en la plataforma</p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline">Exportar</Button>
          </div>
        </div>
      </div>

      <div className="rounded-md border">
        <ClientTable />
      </div>
    </div>
  )
}
