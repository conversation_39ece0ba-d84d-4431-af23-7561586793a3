'use server';
const LOGFLARE_API_URL = 'https://api.logflare.app/logs';
const LOGFLARE_API_KEY = process.env.LOGFLARE_API_KEY!;
const LOGFLARE_SOURCE_ID = process.env.LOGFLARE_SOURCE_ID!;

const IS_PAGES = process.env.PAGES === 'true';

// Función para enviar logs a Logflare
export const sendLogToLogflare = async (logEntry: Record<string, any>) => {

  if (!IS_PAGES) return;
  const logflareEventBody = {
    event_message: logEntry.message,
    metadata: {
      ...logEntry.metadata,
    },
  };

  // console.log('Logflare Event Body:', logflareEventBody);

  const init = {
    method: "POST",
    headers: {
      "X-API-KEY": LOGFLARE_API_KEY,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      // source: sourceKey,
      log_entry: logflareEventBody.event_message,
      metadata: logflareEventBody.metadata,
    }),
  };

  await fetch(`${LOGFLARE_API_URL}?source=${LOGFLARE_SOURCE_ID}`, init);
};
