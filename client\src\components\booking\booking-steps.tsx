"use client"

import { cn } from "@/lib/utils"
import { Check } from "lucide-react"

interface BookingStepsProps {
  currentStep: number
}

export default function BookingSteps({ currentStep }: BookingStepsProps) {
  const steps = [
    { 
      id: 1, 
      name: "<PERSON><PERSON><PERSON>", 
      description: "Selecciona las fechas de tu reserva" 
    },
    { 
      id: 2, 
      name: "Revisión", 
      description: "Revisa los detalles de tu reserva" 
    },
    { 
      id: 3, 
      name: "Pago", 
      description: "Completa el pago de tu reserva" 
    },
    { 
      id: 4, 
      name: "Confirmación", 
      description: "Reserva confirmada" 
    },
  ]

  return (
    <ol 
      // className="items-center w-full space-y-4 sm:flex sm:space-x-8 sm:space-y-0 rtl:space-x-reverse"
      // center all the steps 
      className="items-center w-full space-y-4 sm:flex sm:space-x-8 sm:space-y-0 rtl:space-x-reverse justify-center"
    >
      {steps.map((step) => {
        const isActive = step.id === currentStep
        const isCompleted = step.id < currentStep
        
        return (
          <li 
            key={step.id}
            className={cn(
              "flex items-center space-x-2.5 rtl:space-x-reverse",
              isActive ? "text-[#1a2b5e]" : 
              isCompleted ? "text-[#1a2b5e]" : 
              "text-gray-500"
            )}
          >
            <span 
              className={cn(
                "flex items-center justify-center w-8 h-8 border rounded-full shrink-0",
                isActive ? "border-[#1a2b5e]" : 
                isCompleted ? "border-[#1a2b5e] bg-[#1a2b5e]" : 
                "border-gray-500"
              )}
            >
              {isCompleted ? (
                <Check className="w-4 h-4 text-white" />
              ) : (
                step.id
              )}
            </span>
            <span>
              <h3 className="font-medium leading-tight">{step.name}</h3>
              <p className="text-sm">{step.description}</p>
            </span>
          </li>
        )
      })}
    </ol>
  )
}
