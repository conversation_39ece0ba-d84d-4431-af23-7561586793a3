"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Check } from "lucide-react"
import { useBookingStore } from "@/lib/store/booking-store"

export default function BookingCoverage() {
  const { vehicle, coverage, setCoverage, nextStep, prevStep } = useBookingStore()
  const [selectedCoverage, setSelectedCoverage] = useState<"basic" | "standard" | "premium">(
    coverage?.type || "standard",
  )

  const coverageOptions = [
    {
      type: "basic",
      title: "Basic Coverage",
      price: 0,
      priceDisplay: "$0/day",
      description: "Essential protection for your journey",
      features: ["Third-party liability coverage", "Collision damage waiver with $1,000 deductible"],
      tag: null,
    },
    {
      type: "standard",
      title: "Standard Coverage",
      price: 49,
      priceDisplay: "$49/day",
      description: "Comprehensive coverage with lower deductible",
      features: [
        "All Basic Coverage benefits",
        "Collision damage waiver with $500 deductible",
        "Theft protection",
        "24/7 roadside assistance",
      ],
      tag: "Most Popular",
    },
    {
      type: "premium",
      title: "Premium Coverage",
      price: 79,
      priceDisplay: "$79/day",
      description: "Maximum peace of mind with zero deductible",
      features: [
        "All Standard Coverage benefits",
        "Zero deductible",
        "Personal accident insurance",
        "Personal effects coverage",
      ],
      tag: null,
    },
  ] as const;

  const handleContinue = () => {
    const selected = coverageOptions.find((option) => option.type === selectedCoverage)
    if (selected) {
      setCoverage({
        type: selected.type,
        price: selected.price,
      })
    }
    nextStep()
  }

  return (
    <div className="max-w-3xl mx-auto">
      <div className="flex items-center mb-6">
        <div className="w-16 h-16 bg-gray-100 rounded-lg mr-4 flex items-center justify-center">
          <span className="text-[#1a2b5e] font-bold">
            {vehicle?.make.charAt(0)}
            {vehicle?.model.charAt(0)}
          </span>
        </div>
        <div>
          <h2 className="text-xl font-bold">
            {vehicle?.make} {vehicle?.model}
          </h2>
          <p className="text-gray-500">${vehicle?.price}/day</p>
        </div>
      </div>

      <h2 className="text-xl font-bold mb-6">Select Coverage Plan</h2>

      <div className="grid grid-cols-1 gap-4 mb-8">
        {coverageOptions.map((option) => (
          <div
            key={option.type}
            className={`border rounded-lg p-4 cursor-pointer transition-all ${selectedCoverage === option.type ? "border-[#1a2b5e] bg-blue-50" : "hover:border-gray-400"
              }`}
            onClick={() => setSelectedCoverage(option.type as "basic" | "standard" | "premium")}
          >
            <div className="flex justify-between items-start mb-4">
              <div>
                <div className="flex items-center">
                  <h3 className="font-semibold text-lg">{option.title}</h3>
                  {option.tag && (
                    <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded">{option.tag}</span>
                  )}
                </div>
                <p className="text-sm text-gray-600 mt-1">{option.description}</p>
              </div>
              <div className="flex items-center">
                <div
                  className={`w-5 h-5 rounded-full border flex items-center justify-center mr-2 ${selectedCoverage === option.type ? "bg-[#1a2b5e] border-[#1a2b5e]" : "border-gray-300"
                    }`}
                >
                  {selectedCoverage === option.type && <Check className="h-3 w-3 text-white" />}
                </div>
                <div className="text-right">
                  <div className="font-semibold">{option.priceDisplay}</div>
                  {option.type === "basic" && <div className="text-xs text-gray-500">+$0 total</div>}
                  {option.type === "standard" && <div className="text-xs text-gray-500">+$147 total</div>}
                  {option.type === "premium" && <div className="text-xs text-gray-500">+$237 total</div>}
                </div>
              </div>
            </div>

            <ul className="space-y-2">
              {option.features.map((feature, index) => (
                <li key={index} className="flex items-start">
                  <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                  <span className="text-sm">{feature}</span>
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>

      <div className="flex justify-between">
        <Button type="button" variant="outline" onClick={prevStep}>
          <span className="font-medium">Back</span>
        </Button>
        <Button type="button" className="bg-[#1a2b5e] hover:bg-[#152348]" onClick={handleContinue}>
          <span className="font-medium">Continue</span>
        </Button>
      </div>
    </div>
  )
}
