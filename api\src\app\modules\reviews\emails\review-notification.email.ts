import * as React from 'react';
import { renderToStaticMarkup } from 'react-dom/server';
import ReviewNotificationEmail from '@/emails/ReviewNotification';
import { sendEmailWithNodemailer } from '@/lib/sendVerificationEmail';

export interface ReviewNotificationEmailData {
  to: string;
  userName: string;
  vehicleMake: string;
  vehicleModel: string;
  reservationId: string;
  reviewToken: string;
}

export async function sendReviewNotificationEmail(data: ReviewNotificationEmailData) {
  try {
    // Construir URL de reseña
    const baseUrl = process.env.CLIENT_URL || 'http://localhost:3000';
    const reviewUrl = `${baseUrl}/dashboard/client/booking/${data.reservationId}/review?token=${data.reviewToken}`;

    console.log(`📧 Enviando email de reseña a ${data.to} para reserva ${data.reservationId}`);

    // Renderizar template
    const emailHtml = renderToStaticMarkup(
      React.createElement(ReviewNotificationEmail, {
        userName: data.userName,
        vehicleMake: data.vehicleMake,
        vehicleModel: data.vehicleModel,
        reviewUrl: reviewUrl,
      })
    );

    // Enviar email
    const result = await sendEmailWithNodemailer({
      to: data.to,
      subject: `¡Comparte tu experiencia con el ${data.vehicleMake} ${data.vehicleModel}! ⭐`,
      text: `
Hola ${data.userName},

Esperamos que hayas tenido una excelente experiencia con el ${data.vehicleMake} ${data.vehicleModel} que rentaste recientemente.

Tu opinión es muy valiosa para nosotros y para otros usuarios de Autoop. ¿Te gustaría compartir tu experiencia dejando una reseña?

Puedes dejar tu reseña en el siguiente enlace:
${reviewUrl}

¿Qué puedes incluir en tu reseña?
• Calificación de 1 a 5 estrellas (opcional)
• Comentarios sobre el vehículo
• Tu experiencia con el anfitrión
• Cualquier detalle que ayude a otros usuarios

Recuerda que tanto la calificación como los comentarios son completamente opcionales.

Si no deseas dejar una reseña, simplemente ignora este mensaje. Este enlace expirará en 30 días.

¡Gracias por elegir Autoop! 🚗

---
© 2024 Autoop. Todos los derechos reservados.
Este email fue enviado porque completaste una reserva en Autoop.
      `.trim(),
      html: emailHtml,
    });

    if (result.error) {
      console.error(`❌ Error enviando email de reseña a ${data.to}:`, result.error);
      throw new Error(`Error enviando email: ${result.error}`);
    }

    console.log(`✅ Email de reseña enviado exitosamente a ${data.to}`);
    return result;

  } catch (error) {
    console.error(`❌ Error en sendReviewNotificationEmail:`, error);
    throw error;
  }
}
