"use client"

import Image from "next/image"
import { Eye, MoreHorizontal, Edit, Ban, CheckCircle, XCircle } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { But<PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

// Mock data
const reservations = [
  {
    id: "RES-1234",
    client: {
      name: "<PERSON>",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "E<PERSON>",
    },
    vehicle: {
      name: "Tesla Model 3",
      image: "/placeholder.svg?height=40&width=40",
      host: "<PERSON>",
    },
    dates: {
      start: "May 10, 2025",
      end: "May 15, 2025",
      days: 5,
    },
    amount: "$550.00",
    status: "Active",
    payment: "Paid",
  },
  {
    id: "RES-1235",
    client: {
      name: "<PERSON>",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "DC",
    },
    vehicle: {
      name: "Porsche 911",
      image: "/placeholder.svg?height=40&width=40",
      host: "James Wilson",
    },
    dates: {
      start: "May 12, 2025",
      end: "May 14, 2025",
      days: 2,
    },
    amount: "$1,200.00",
    status: "Upcoming",
    payment: "Pending",
  },
  {
    id: "RES-1236",
    client: {
      name: "Sarah Miller",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "SM",
    },
    vehicle: {
      name: "Range Rover Sport",
      image: "/placeholder.svg?height=40&width=40",
      host: "Michelle Brown",
    },
    dates: {
      start: "May 8, 2025",
      end: "May 12, 2025",
      days: 4,
    },
    amount: "$960.00",
    status: "Completed",
    payment: "Paid",
  },
  {
    id: "RES-1237",
    client: {
      name: "Michael Thompson",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "MT",
    },
    vehicle: {
      name: "Mercedes-Benz E-Class",
      image: "/placeholder.svg?height=40&width=40",
      host: "Sophia Martinez",
    },
    dates: {
      start: "May 7, 2025",
      end: "May 10, 2025",
      days: 3,
    },
    amount: "$750.00",
    status: "Cancelled",
    payment: "Refunded",
  },
]

export function ReservationTable() {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Reservation ID</TableHead>
          <TableHead>Client</TableHead>
          <TableHead>Vehicle</TableHead>
          <TableHead>Dates</TableHead>
          <TableHead>Amount</TableHead>
          <TableHead>Status</TableHead>
          <TableHead>Payment</TableHead>
          <TableHead className="text-right">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {reservations.map((reservation) => (
          <TableRow key={reservation.id}>
            <TableCell className="font-medium">{reservation.id}</TableCell>
            <TableCell>
              <div className="flex items-center gap-2">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={reservation.client.avatar || "/placeholder.svg"} alt={reservation.client.name} />
                  <AvatarFallback>{reservation.client.initials}</AvatarFallback>
                </Avatar>
                <span className="text-sm">{reservation.client.name}</span>
              </div>
            </TableCell>
            <TableCell>
              <div className="flex items-center gap-2">
                <div className="h-8 w-8 rounded overflow-hidden">
                  <Image
                    src={reservation.vehicle.image || "/placeholder.svg"}
                    alt={reservation.vehicle.name}
                    width={32}
                    height={32}
                    className="h-full w-full object-cover"
                  />
                </div>
                <div>
                  <div className="text-sm font-medium">{reservation.vehicle.name}</div>
                  <div className="text-xs text-muted-foreground">Host: {reservation.vehicle.host}</div>
                </div>
              </div>
            </TableCell>
            <TableCell>
              <div className="text-sm">
                {reservation.dates.start} - {reservation.dates.end}
                <div className="text-xs text-muted-foreground">{reservation.dates.days} days</div>
              </div>
            </TableCell>
            <TableCell className="font-medium">{reservation.amount}</TableCell>
            <TableCell>
              <Badge
                variant={
                  reservation.status === "Active"
                    ? "default"
                    : reservation.status === "Upcoming"
                      ? "secondary"
                      : reservation.status === "Completed"
                        ? "outline"
                        : "destructive"
                }
                className={
                  reservation.status === "Active"
                    ? "bg-green-100 text-green-800 hover:bg-green-100"
                    : reservation.status === "Upcoming"
                      ? "bg-blue-100 text-blue-800 hover:bg-blue-100"
                      : reservation.status === "Completed"
                        ? "bg-gray-100 text-gray-800 hover:bg-gray-100"
                        : "bg-red-100 text-red-800 hover:bg-red-100"
                }
              >
                {reservation.status}
              </Badge>
            </TableCell>
            <TableCell>
              <Badge
                variant={
                  reservation.payment === "Paid"
                    ? "outline"
                    : reservation.payment === "Pending"
                      ? "secondary"
                      : "destructive"
                }
                className={
                  reservation.payment === "Paid"
                    ? "bg-green-100 text-green-800 hover:bg-green-100"
                    : reservation.payment === "Pending"
                      ? "bg-yellow-100 text-yellow-800 hover:bg-yellow-100"
                      : "bg-red-100 text-red-800 hover:bg-red-100"
                }
              >
                {reservation.payment}
              </Badge>
            </TableCell>
            <TableCell className="text-right">
              <div className="flex justify-end gap-2">
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <Eye className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <Edit className="h-4 w-4" />
                </Button>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem>
                      <Eye className="mr-2 h-4 w-4" />
                      <span>View Details</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Edit className="mr-2 h-4 w-4" />
                      <span>Edit Reservation</span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    {reservation.status === "Active" || reservation.status === "Upcoming" ? (
                      <>
                        <DropdownMenuItem className="text-green-600">
                          <CheckCircle className="mr-2 h-4 w-4" />
                          <span>Mark as Completed</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-red-600">
                          <XCircle className="mr-2 h-4 w-4" />
                          <span>Cancel Reservation</span>
                        </DropdownMenuItem>
                      </>
                    ) : reservation.status === "Completed" ? (
                      <DropdownMenuItem>
                        <Ban className="mr-2 h-4 w-4" />
                        <span>Report Issue</span>
                      </DropdownMenuItem>
                    ) : null}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}
