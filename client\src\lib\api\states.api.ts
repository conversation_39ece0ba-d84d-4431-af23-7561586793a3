import { apiService } from '@/services/api';

export interface StateResponse {
  id: string;
  name: string;
  code: string;
  countryCode: string;
  timezone?: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;
}

export const statesApi = {
  // Obtener todos los estados (público)
  getAll: async (): Promise<StateResponse[]> => {
    const result = await apiService.get<StateResponse[]>('/states');
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.error);
    }
  },

  // Obtener un estado por ID (público)
  getById: async (id: string): Promise<StateResponse> => {
    const result = await apiService.get<StateResponse>(`/states/${id}`);
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.error);
    }
  },

  // Funciones específicas para administradores
  admin: {
    // Obtener todos los estados (admin)
    getAll: async (): Promise<StateResponse[]> => {
      const result = await apiService.get<StateResponse[]>('/admin/states');
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },

    // Crear un nuevo estado (admin)
    create: async (data: { name: string; code: string; countryCode?: string; timezone?: string; }): Promise<StateResponse> => {
      const result = await apiService.post<StateResponse>('/admin/states', data);
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },

    // Actualizar un estado (admin)
    update: async (id: string, data: { name?: string; code?: string; countryCode?: string; timezone?: string; active?: boolean; }): Promise<StateResponse> => {
      const result = await apiService.put<StateResponse>(`/admin/states/${id}`, data);
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },

    // Activar/desactivar un estado (admin)
    toggleActive: async (id: string): Promise<StateResponse> => {
      const result = await apiService.patch<StateResponse>(`/admin/states/${id}/toggle-active`, {});
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    },

    // Eliminar un estado (admin)
    delete: async (id: string): Promise<any> => {
      const result = await apiService.delete(`/admin/states/${id}`);
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error);
      }
    }
  }
};