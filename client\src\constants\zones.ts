

export const timezonesOnStateCode: Record<'mx' | 'us', Record<string, { timezone: string; label: string }>> = {
  mx: {
    ags: {
      timezone: 'America/Mexico_City',
      label: 'Aguascalientes'
    },
    bcn: {
      timezone: 'America/Tijuana',
      label: 'Baja California'
    },
    bcs: {
      timezone: 'America/Mazatlan',
      label: 'Baja California Sur'
    },
    cam: {
      timezone: 'America/Merida',
      label: 'Campeche'
    },
    chh: {
      timezone: 'America/Chihuahua',
      label: 'Chihuahua'
    },
    chp: {
      timezone: 'America/Mexico_City',
      label: 'Chiapas'
    },
    cmx: {
      timezone: 'America/Mexico_City',
      label: 'Ciudad de México'
    },
    coa: {
      timezone: 'America/Monterrey',
      label: 'Coahuila'
    },
    col: {
      timezone: 'America/Mexico_City',
      label: 'Colima'
    },
    dur: {
      timezone: 'America/Monterrey',
      label: 'Durango'
    },
    gro: {
      timezone: 'America/Mexico_City',
      label: 'Guerrero'
    },
    gto: {
      timezone: 'America/Mexico_City',
      label: 'Guanajuato'
    },
    hgo: {
      timezone: 'America/Mexico_City',
      label: 'Hidalgo'
    },
    jal: {
      timezone: 'America/Mexico_City',
      label: 'Jalisco'
    },
    mex: {
      timezone: 'America/Mexico_City',
      label: 'Estado de México'
    },
    mic: {
      timezone: 'America/Mexico_City',
      label: 'Michoacán'
    },
    mor: {
      timezone: 'America/Mexico_City',
      label: 'Morelos'
    },
    nay: {
      timezone: 'America/Mazatlan',
      label: 'Nayarit'
    },
    nle: {
      timezone: 'America/Monterrey',
      label: 'Nuevo León'
    },
    oax: {
      timezone: 'America/Mexico_City',
      label: 'Oaxaca'
    },
    pue: {
      timezone: 'America/Mexico_City',
      label: 'Puebla'
    },
    qro: {
      timezone: 'America/Mexico_City',
      label: 'Querétaro'
    },
    roo: {
      timezone: 'America/Cancun',
      label: 'Quintana Roo'
    },
    slp: {
      timezone: 'America/Mexico_City',
      label: 'San Luis Potosí'
    },
    sin: {
      timezone: 'America/Mazatlan',
      label: 'Sinaloa'
    },
    son: {
      timezone: 'America/Hermosillo',
      label: 'Sonora'
    },
    tab: {
      timezone: 'America/Mexico_City',
      label: 'Tabasco'
    },
    tam: {
      timezone: 'America/Mexico_City',
      label: 'Tamaulipas'
    },
    tla: {
      timezone: 'America/Mexico_City',
      label: 'Tlaxcala'
    },
    ver: {
      timezone: 'America/Mexico_City',
      label: 'Veracruz'
    },
    yuc: {
      timezone: 'America/Merida',
      label: 'Yucatán'
    },
    zac: {
      timezone: 'America/Mexico_City',
      label: 'Zacatecas'
    },
  },
  us: {
    CA: {
      timezone: 'America/Los_Angeles',
      label: 'California'
    },
    NY: {
      timezone: 'America/New_York',
      label: 'New York'
    },
    TX: {
      timezone: 'America/Chicago',
      label: 'Texas'
    },
    FL: {
      timezone: 'America/New_York',
      label: 'Florida'
    },
    IL: {
      timezone: 'America/Chicago',
      label: 'Illinois'
    },
    AZ: {
      timezone: 'America/Phoenix',
      label: 'Arizona (no DST)'
    },
    WA: {
      timezone: 'America/Los_Angeles',
      label: 'Washington'
    },
    CO: {
      timezone: 'America/Denver',
      label: 'Colorado'
    },
    GA: {
      timezone: 'America/New_York',
      label: 'Georgia'
    },
    NV: {
      timezone: 'America/Los_Angeles',
      label: 'Nevada'
    },
  }
}


export const getTimezone = (countryCode: 'mx' | 'us', stateCode: string) => {
  return timezonesOnStateCode[countryCode]?.[stateCode]?.timezone || 'America/Mexico_City';
}
