import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { MoreHorizontal, Eye, MessageSquare, Star } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import Image from "next/image"

const reservations = [
  {
    id: "RES-001",
    vehicle: {
      name: "Toyota Camry 2023",
      image: "/placeholder.svg?height=60&width=80",
    },
    host: "<PERSON>",
    startDate: "2024-01-15",
    endDate: "2024-01-18",
    status: "active",
    total: "$240.00",
    location: "Centro, Ciudad",
  },
  {
    id: "RES-002",
    vehicle: {
      name: "Honda Civic 2022",
      image: "/placeholder.svg?height=60&width=80",
    },
    host: "<PERSON>",
    startDate: "2024-01-20",
    endDate: "2024-01-22",
    status: "pending",
    total: "$160.00",
    location: "Norte, Ciudad",
  },
  {
    id: "RES-003",
    vehicle: {
      name: "Nissan Sentra 2023",
      image: "/placeholder.svg?height=60&width=80",
    },
    host: "Luis Garcia",
    startDate: "2024-01-10",
    endDate: "2024-01-12",
    status: "completed",
    total: "$140.00",
    location: "Sur, Ciudad",
  },
]

const getStatusBadge = (status: string) => {
  switch (status) {
    case "active":
      return <Badge className="bg-blue-100 text-blue-800">Activa</Badge>
    case "pending":
      return <Badge className="bg-yellow-100 text-yellow-800">Pendiente</Badge>
    case "completed":
      return <Badge className="bg-green-100 text-green-800">Completada</Badge>
    case "cancelled":
      return <Badge className="bg-red-100 text-red-800">Cancelada</Badge>
    default:
      return <Badge variant="secondary">{status}</Badge>
  }
}

export function ClientReservationTable() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Mis Reservas</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Vehículo</TableHead>
              <TableHead>Anfitrión</TableHead>
              <TableHead>Fechas</TableHead>
              <TableHead>Estado</TableHead>
              <TableHead>Total</TableHead>
              <TableHead className="text-right">Acciones</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {reservations.map((reservation) => (
              <TableRow key={reservation.id}>
                <TableCell>
                  <div className="flex items-center gap-3">
                    <Image
                      src={reservation.vehicle.image || "/placeholder.svg"}
                      alt={reservation.vehicle.name}
                      width={80}
                      height={60}
                      className="rounded-md object-cover"
                    />
                    <div>
                      <p className="font-medium">{reservation.vehicle.name}</p>
                      <p className="text-sm text-muted-foreground">{reservation.location}</p>
                    </div>
                  </div>
                </TableCell>
                <TableCell>{reservation.host}</TableCell>
                <TableCell>
                  <div className="text-sm">
                    <div>{reservation.startDate}</div>
                    <div className="text-muted-foreground">a {reservation.endDate}</div>
                  </div>
                </TableCell>
                <TableCell>{getStatusBadge(reservation.status)}</TableCell>
                <TableCell className="font-medium">{reservation.total}</TableCell>
                <TableCell className="text-right">
                  <div className="flex items-center justify-end gap-2">
                    <Button variant="ghost" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <MessageSquare className="h-4 w-4" />
                    </Button>
                    {reservation.status === "completed" && (
                      <Button variant="ghost" size="sm">
                        <Star className="h-4 w-4" />
                      </Button>
                    )}
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>Ver detalles</DropdownMenuItem>
                        <DropdownMenuItem>Contactar anfitrión</DropdownMenuItem>
                        {reservation.status === "completed" && <DropdownMenuItem>Dejar reseña</DropdownMenuItem>}
                        {reservation.status === "pending" && (
                          <DropdownMenuItem className="text-red-600">Cancelar reserva</DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}
