import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { FileText, CreditCard, User, Car } from 'lucide-react';
import DocumentPreview from '../forms/document-preview';

type VerificationDocumentsPreviewProps = {
  verification: {
    idFront?: string;
    idBack?: string;
    driverLicense?: string;
    addressProof?: string;
    selfieWithId?: string;
    status: string;
  };
};

export function VerificationDocumentsPreview({ verification }: VerificationDocumentsPreviewProps) {
  // console.log('VerificationDocumentsPreview - verification data:', verification);

  const documents = [
    {
      key: 'idFront',
      label: 'Identificación (Frente)',
      icon: CreditCard,
      url: verification.idFront,
    },
    {
      key: 'idBack',
      label: 'Identificación (Reverso)',
      icon: CreditCard,
      url: verification.idBack,
    },
    {
      key: 'driverLicense',
      label: 'Licencia de Conducir',
      icon: Car,
      url: verification.driverLicense,
    },
    {
      key: 'addressProof',
      label: 'Comprobante de Domicilio',
      icon: FileText,
      url: verification.addressProof,
    },
    {
      key: 'selfieWithId',
      label: 'Selfie con Identificación',
      icon: User,
      url: verification.selfieWithId,
    },
  ];

  const uploadedDocuments = documents.filter(doc => doc.url);

  if (uploadedDocuments.length === 0) {
    return null;
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'approved':
        return 'Aprobado';
      case 'pending':
        return 'En revisión';
      case 'rejected':
        return 'Rechazado';
      default:
        return 'Sin verificar';
    }
  };

  return (
    <Card className="mb-8">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Documentos de verificación</CardTitle>
            <CardDescription>
              Documentos que has subido para la verificación de tu identidad
            </CardDescription>
          </div>
          <Badge className={getStatusColor(verification.status)}>
            {getStatusText(verification.status)}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {uploadedDocuments.map((document) => {
            console.log(`Rendering document ${document.label} with URL:`, document.url);

            return (
              <div key={document.key} className="space-y-2">
                <div className="flex items-center">
                  <document.icon className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span className="font-medium text-sm">{document.label}</span>
                </div>
                <DocumentPreview
                  documentKey={document.url!}
                  label={document.label}
                  showButtonOnHover={false}
                />
              </div>
            );
          })}
        </div>
        {uploadedDocuments.length < 5 && (
          <p className="text-sm text-muted-foreground mt-4">
            {5 - uploadedDocuments.length} documento(s) pendiente(s) por subir
          </p>
        )}
      </CardContent>
    </Card>
  );
}
