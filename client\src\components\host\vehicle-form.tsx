'use client'
import type React from "react"
import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import toast from "react-hot-toast"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { X } from "lucide-react"
import { useMutation, useQuery } from "@tanstack/react-query"
import { vehiclesApi, VehicleFormData } from "@/lib/api/vehicles.api"
import { statesApi } from "@/lib/api/states.api"
import { useRouter } from 'next/navigation'
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import FileUploadInput from "@/components/forms/file-upload-input"
import { ScrollArea } from '../ui/scroll-area'

const vehicleFormSchema = z.object({
  // Información básica
  make: z.string().min(1, { message: "La marca es requerida" }),
  model: z.string().min(1, { message: "El modelo es requerido" }),
  year: z.string().min(4, { message: "El año es requerido" }),
  color: z.string().min(1, { message: "El color es requerido" }),
  vin: z.string().min(17, { message: "El VIN debe tener 17 caracteres" }),

  // Nuevos campos estructurados
  engineSize: z.string().min(1, { message: "La motorización es requerida" }),
  trim: z.string().min(1, { message: "La versión es requerida" }),
  bodyType: z.enum([
    "suv", "sedan", "hatchback", "pickup", "coupe",
    "convertible", "wagon", "van", "minivan", "targa",
    "doublecab", "truck"
  ], {
    required_error: "Selecciona el tipo de carrocería",
  }),

  // Documentación
  plate: z.string().min(1, { message: "Las placas son requeridas" }),
  state_code: z.string().min(1, { message: "El estado de las placas es requerido" }),
  country_code: z.string().min(1, { message: "El país es requerido" }),
  registrationNumber: z.string().min(1, { message: "El número de tarjeta de circulación es requerido" }),
  insurancePolicy: z.string().min(1, { message: "El número de póliza de seguro es requerido" }),

  // Especificaciones
  transmission: z.enum(["manual", "automatic"], {
    required_error: "Selecciona el tipo de transmisión",
  }),
  fuelType: z.enum(["gasoline", "diesel", "electric", "hybrid"], {
    required_error: "Selecciona el tipo de combustible",
  }),
  seats: z.string().min(1, { message: "El número de asientos es requerido" }),
  mileage: z.string().min(1, { message: "El kilometraje es requerido" }),

  // Precios
  dailyRate: z.string().min(1, { message: "La tarifa diaria es requerida" }),
  weeklyRate: z.string().optional(),
  monthlyRate: z.string().optional(),

  // Descripción y reglas
  description: z.string().min(20, { message: "La descripción deben tener al menos 20 caracteres" }).max(200, { message: "La descripción no pueden tener más de 200 caracteres" }),
  rules: z.string().min(20, { message: "Las reglas deben tener al menos 20 caracteres" }),
  // Ubicación
  location: z.string().min(1, { message: "La ubicación es requerida" }),
  amenities: z.array(z.string()).min(1, { message: "Selecciona al menos una comodidad" }),

  images: z.array(z.custom<File>())
    .min(1, "Por favor, sube al menos una imagen del vehículo")
    .max(10, "Maximo 10 imagenes")
    .refine((files) => files.every((file) => file.size <= 2 * 1024 * 1024), {
      message: "Las imagenes deben pesar menos de 2MB",
      path: ["files"],
    }),
  // insurancePolicyDocument: z.custom<File>().refine((file) => file && file.size <= 2 * 1024 * 1024, {
  //   message: "El archivo debe pesar menos de 2MB",
  //   path: ["file"],
  // }),
  // plateDocument: z.custom<File>().refine((file) => file && file.size <= 2 * 1024 * 1024, {
  //   message: "El archivo debe pesar menos de 2MB",
  //   path: ["file"],
  // }),
  // vinDocument: z.custom<File>().refine((file) => file && file.size <= 2 * 1024 * 1024, {
  //   message: "El archivo debe pesar menos de 2MB",
  //   path: ["file"],
  // }),
  // registrationDocument: z.custom<File>().refine((file) => {
  //   console.log('file size: ', file.size, file.size <= 2 * 1024 * 1024)
  //   return file && file.size <= 2 * 1024 * 1024
  // }, {
  //   message: "El archivo debe pesar menos de 2MB",
  //   path: ["file"],
  // }),
  insurancePolicyDocument: z.array(z.custom<File>())
    .min(1, "Por favor, sube al menos un archivo")
    .max(1, "Maximo 1 archivo")
    .refine((files) => files.every((file) => file.size <= 2 * 1024 * 1024), {
      message: "El archivo debe pesar menos de 2MB",
      path: ["files"],
    }),
  plateDocument: z.array(z.custom<File>())
    .min(1, "Por favor, sube al menos un archivo")
    .max(1, "Maximo 1 archivo")
    .refine((files) => files.every((file) => file.size <= 2 * 1024 * 1024), {
      message: "El archivo debe pesar menos de 2MB",
      path: ["files"],

    }),
  vinDocument: z.array(z.custom<File>())
    .min(1, "Por favor, sube al menos un archivo")
    .max(1, "Maximo 1 archivo")
    .refine((files) => files.every((file) => file.size <= 2 * 1024 * 1024), {
      message: "El archivo debe pesar menos de 2MB",
      path: ["files"],
    }),
  registrationDocument: z.array(z.custom<File>())
    .min(1, "Por favor, sube al menos un archivo")
    .max(1, "Maximo 1 archivo")
    .refine((files) => files.every((file) => file.size <= 2 * 1024 * 1024), {
      message: "El archivo debe pesar menos de 2MB",
      path: ["files"],
    }),


})

type VehicleFormValues = z.infer<typeof vehicleFormSchema>

// Lista de amenidades disponibles
const availableAmenities = [
  { value: "air_conditioning", label: "Aire acondicionado" },
  { value: "bluetooth", label: "Bluetooth" },
  { value: "cruise_control", label: "Control de crucero" },
  { value: "backup_camera", label: "Cámara de reversa" },
  { value: "navigation", label: "Sistema de navegación" },
  { value: "heated_seats", label: "Asientos calefactados" },
  { value: "sunroof", label: "Techo solar" },
  { value: "leather_seats", label: "Asientos de piel" },
  { value: "apple_carplay", label: "Apple CarPlay" },
  { value: "android_auto", label: "Android Auto" },
  { value: "premium_sound", label: "Sistema de sonido premium" },
  { value: "parking_sensors", label: "Sensores de estacionamiento" },
  { value: "blind_spot_monitoring", label: "Monitoreo de punto ciego" },
  { value: "keyless_entry", label: "Entrada sin llave" },
  { value: "push_button_start", label: "Encendido por botón" },
  { value: "third_row_seating", label: "Tercera fila de asientos" },
  { value: "wifi_hotspot", label: "Punto de acceso WiFi" },
  { value: "wireless_charging", label: "Carga inalámbrica" },
]

export function VehicleForm() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  // Consultar los estados disponibles
  const { data: states, isLoading: statesLoading } = useQuery({
    queryKey: ['states'],
    queryFn: statesApi.getAll
  })

  const form = useForm<VehicleFormValues>({
    resolver: zodResolver(vehicleFormSchema),
    // defaultValues: {
    //   make: "",
    //   model: "",
    //   year: "",
    //   color: "",
    //   vin: "LGXCE4CC7S00653333",
    //   plate: "ABC12355",
    //   registrationNumber: "FG13FG31F133",
    //   insurancePolicy: "GFHSGAG1122",
    //   transmission: "automatic",
    //   fuelType: "gasoline",
    //   seats: "",
    //   mileage: "",
    //   dailyRate: "",
    //   weeklyRate: "",
    //   monthlyRate: "",
    //   description: "Auto amplio y comodo",
    //   rules: "No fumar, no mascotas",
    //   location: "Calle Ignacio Allende #24",
    //   engineSize: "1.6",
    //   trim: "LE",
    //   bodyType: "sedan",
    //   amenities: [],
    //   state_code: "", // Agregar el valor por defecto para state_code
    //   images: [],
    //   vinDocument: [],
    //   plateDocument: [],
    //   registrationDocument: [],
    //   insurancePolicyDocument: [],
    // },
    
    // empty default values
    defaultValues: {
      make: "",
      model: "",
      year: "",
      color: "",
      vin: "",
      plate: "",
      registrationNumber: "",
      insurancePolicy: "",
      transmission: "automatic",
      fuelType: "gasoline",
      seats: "",
      mileage: "",
      dailyRate: "",
      weeklyRate: "",
      monthlyRate: "",
      description: "",
      rules: "",
      location: "",
      engineSize: "",
      trim: "",
      bodyType: "sedan",
      amenities: [],
      state_code: "",
      country_code: "mx",
      images: [],
      vinDocument: [],
      plateDocument: [],
      registrationDocument: [],
      insurancePolicyDocument: [],
    }
  })

  // Mutación para crear vehículo
  const createVehicleMutation = useMutation({
    mutationFn: vehiclesApi.host.create,
    onSuccess: (data) => {
      toast.success("Vehículo registrado exitosamente")

      // Recoger los archivos del formulario
      const files = {
        images: form.getValues("images"),
        vinDocument: form.getValues("vinDocument"),
        plateDocument: form.getValues("plateDocument"),
        registrationDocument: form.getValues("registrationDocument"),
        insurancePolicyDocument: form.getValues("insurancePolicyDocument"),
      }

      toast('Subiendo archivos...', {
        duration: 3000,
        icon: '⏳',
      })
      uploadFilesMutation.mutate({ vehicleId: data.id, data: files })
    },
    onError: (error) => {
      toast.error(error.message || "Ocurrió un error al registrar el vehículo. Inténtalo de nuevo.")
      setIsLoading(false)
    }
  })

  const uploadFilesMutation = useMutation({
    mutationFn: ({ vehicleId, data }: { vehicleId: string, data: Partial<VehicleFormData> }) =>
      vehiclesApi.host.uploadFiles(vehicleId, data),
    onSuccess: (data) => {
      console.log('Response data of upload files:', data);
      toast.success("Archivos subidos exitosamente.")
      router.push(`/dashboard/host/vehicles/${data.id}`)
    },
    onError: (error) => {
      toast.error(error.message || "Ocurrió un error al subir los archivos. Inténtalo de nuevo.")
    },
    onSettled: () => {
      setIsLoading(false)
    }
  })

  const onSubmit = async (data: VehicleFormValues) => {
    setIsLoading(true)

    // Obtener el código de país del estado seleccionado
    const selectedState = states?.find(state => state.code === data.state_code)
    const countryCode = selectedState?.countryCode || "mx"

    // Transformar los datos del formulario al formato esperado por la API
    const vehicleData: VehicleFormData = {
      make: data.make,
      model: data.model,
      year: parseInt(data.year),
      color: data.color,
      vin: data.vin,
      plate: data.plate,
      state_code: data.state_code,
      country_code: countryCode,
      price: parseFloat(data.dailyRate),
      description: data.description,

      // Nuevos campos estructurados
      engineSize: parseFloat(data.engineSize),
      transmission: data.transmission,
      trim: data.trim,
      bodyType: data.bodyType,
      features: {
        fuelType: data.fuelType,
        seats: parseInt(data.seats),
        mileage: parseInt(data.mileage),
        registrationNumber: data.registrationNumber,
        insurancePolicy: data.insurancePolicy,
        rules: data.rules,
        location: data.location,
        weeklyRate: data.weeklyRate ? parseFloat(data.weeklyRate) : undefined,
        monthlyRate: data.monthlyRate ? parseFloat(data.monthlyRate) : undefined,
      },
      amenities: data.amenities,
      images: data.images,
      vinDocument: data.vinDocument,
      plateDocument: data.plateDocument,
      registrationDocument: data.registrationDocument,
      insurancePolicyDocument: data.insurancePolicyDocument,
    }

    createVehicleMutation.mutate(vehicleData)


  }


  // Función para manejar la selección de amenidades
  const handleAmenityChange = (value: string, checked: boolean) => {
    const currentAmenities = form.getValues("amenities") || [];

    if (checked) {
      form.setValue("amenities", [...currentAmenities, value], { shouldValidate: true });
    } else {
      form.setValue(
        "amenities",
        currentAmenities.filter((amenity) => amenity !== value),
        { shouldValidate: true }
      );
    }
  };

  // Función para eliminar una amenidad
  const removeAmenity = (value: string) => {
    const currentAmenities = form.getValues("amenities") || [];
    form.setValue(
      "amenities",
      currentAmenities.filter((amenity) => amenity !== value),
      { shouldValidate: true }
    );
  };

  const errors = form.formState.errors
  console.log('errors: ', errors)

  return (
    <Form {...form}>
      <ScrollArea className="w-full h-full">
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        {/* Información Básica */}
        <Card>
          <CardHeader>
            <CardTitle>Información Básica</CardTitle>
            <CardDescription>Detalles principales de tu vehículo</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-start">
              <FormField
                control={form.control}
                name="make"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Marca</FormLabel>
                    <FormControl>
                      <Input placeholder="Toyota, BMW, Tesla..." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="model"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Modelo</FormLabel>
                    <FormControl>
                      <Input placeholder="Corolla, X5, Model 3..." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="engineSize"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Motorización (litros)</FormLabel>
                    <FormControl>
                      <Input placeholder="1.6, 2.0, 3.5..." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="trim"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Versión / Nivel de equipamiento</FormLabel>
                    <FormControl>
                      <Input placeholder="LE, XLE, ICONIC, GRAND TOURING..." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="bodyType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tipo de carrocería</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Selecciona tipo de carrocería" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="sedan">Sedan</SelectItem>
                        <SelectItem value="suv">SUV</SelectItem>
                        <SelectItem value="hatchback">Hatchback</SelectItem>
                        <SelectItem value="pickup">Pickup</SelectItem>
                        <SelectItem value="coupe">Coupe</SelectItem>
                        <SelectItem value="convertible">Convertible</SelectItem>
                        <SelectItem value="wagon">Wagon</SelectItem>
                        <SelectItem value="van">Van</SelectItem>
                        <SelectItem value="minivan">Minivan</SelectItem>
                        <SelectItem value="targa">Targa</SelectItem>
                        <SelectItem value="doublecab">Doble Cabina</SelectItem>
                        <SelectItem value="truck">Camioneta</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="year"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Año</FormLabel>
                    <FormControl>
                      <Input placeholder="2023" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="color"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Color</FormLabel>
                    <FormControl>
                      <Input placeholder="Blanco, Negro, Azul..." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="state_code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ubicación del vehículo (Estado)</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Selecciona el estado" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {statesLoading ? (
                          <SelectItem value="loading">Cargando estados...</SelectItem>
                        ) : (
                          states?.map(state => (
                            <SelectItem key={state.id} value={state.code}>
                              {state.name}
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* Documentación */}
        <Card>
          <CardHeader>
            <CardTitle>Documentación</CardTitle>
            <CardDescription>Información legal y de registro</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex flex-col gap-4">
              <FormField
                control={form.control}
                name="plate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Placas (sin espacios ni guiones)</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="ABC123 para (ABC-123)"
                        {...field}
                        onChange={(e) => {
                          const value = e.target.value.replace(/-/g, '').replace(/\s/g, '');
                          field.onChange(value.toUpperCase());
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Documento de placas */}
              <FileUploadInput
                form={form}
                name="plateDocument"
                label="Imagen de placas"
                description="Sube una foto clara de las placas del vehículo"
                maxSize={2 * 1024 * 1024}
                maxFiles={1}
              />

              <FormField
                control={form.control}
                name="vin"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>VIN (Número de serie)</FormLabel>
                    <FormControl>
                      <Input placeholder="1HGBH41JXMN109186" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Documento de VIN */}
              <FileUploadInput
                form={form}
                name="vinDocument"
                label="Imagen del VIN"
                accept="image/*,application/pdf"
                description="Sube una foto clara del número de serie del vehículo"
                maxSize={2 * 1024 * 1024}
                maxFiles={1}
              />

              <FormField
                control={form.control}
                name="registrationNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Número de tarjeta de circulación</FormLabel>
                    <FormControl>
                      <Input placeholder="12345678" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Documento de tarjeta de circulación */}
              <FileUploadInput
                form={form}
                name="registrationDocument"
                label="Imagen de tarjeta de circulación"
                description="Sube una foto clara de la tarjeta de circulación"
                maxSize={2 * 1024 * 1024}
                maxFiles={1}
                accept="image/*,application/pdf"
              />

              <FormField
                control={form.control}
                name="insurancePolicy"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Número de póliza de seguro</FormLabel>
                    <FormControl>
                      <Input placeholder="POL-123456" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Documento de póliza de seguro */}
              <FileUploadInput
                form={form}
                name="insurancePolicyDocument"
                label="Imagen de póliza de seguro"
                description="Sube una foto clara de la póliza de seguro vigente"
                maxSize={2 * 1024 * 1024}
                maxFiles={1}
                accept="image/*,application/pdf"
              />

            </div>
          </CardContent>
        </Card>

        {/* Especificaciones */}
        <Card>
          <CardHeader>
            <CardTitle>Especificaciones</CardTitle>
            <CardDescription>Características técnicas del vehículo</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="transmission"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Transmisión</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Selecciona transmisión" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="manual">Manual</SelectItem>
                        <SelectItem value="automatic">Automática</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="fuelType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tipo de Combustible</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Selecciona combustible" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="gasoline">Gasolina</SelectItem>
                        <SelectItem value="diesel">Diésel</SelectItem>
                        <SelectItem value="electric">Eléctrico</SelectItem>
                        <SelectItem value="hybrid">Híbrido</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="seats"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Número de Asientos</FormLabel>
                    <FormControl>
                      <Input placeholder="5" type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="mileage"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Kilometraje</FormLabel>
                    <FormControl>
                      <Input placeholder="50000" type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* Precios */}
        <Card>
          <CardHeader>
            <CardTitle>Precios</CardTitle>
            <CardDescription>Establece las tarifas de renta</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-start">
              <FormField
                control={form.control}
                name="dailyRate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tarifa Diaria (MXN)</FormLabel>
                    <FormControl>
                      <Input placeholder="500" type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="weeklyRate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tarifa Semanal (MXN)</FormLabel>
                    <FormControl>
                      <Input placeholder="3000" type="number" {...field} />
                    </FormControl>
                    <FormDescription>Opcional - descuento por semana</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="monthlyRate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tarifa Mensual (MXN)</FormLabel>
                    <FormControl>
                      <Input placeholder="10000" type="number" {...field} />
                    </FormControl>
                    <FormDescription>Opcional - descuento por mes</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* Fotos */}
        <FileUploadInput
          form={form}
          name="images"
          label="Fotos del Vehículo"
          description="Sube fotos atractivas de tu vehículo (mínimo 3, máximo 10)"
          maxSize={2 * 1024 * 1024}
          maxFiles={10}
          multiple
        />

        {/* Descripción y Reglas */}
        <Card>
          <CardHeader>
            <CardTitle>Descripción y Reglas</CardTitle>
            <CardDescription>Información adicional para los clientes</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Descripción del Vehículo</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe tu vehículo, sus características especiales, comodidades, etc."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>Mínimo 20 caracteres, máximo 200 caracteres</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="rules"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Reglas y Restricciones</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Ej: No fumar, no mascotas, combustible lleno al regresar, etc."
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>Establece reglas claras para el uso del vehículo</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Ubicación de Entrega</FormLabel>
                  <FormControl>
                    <Input placeholder="Dirección donde se entregará el vehículo" {...field} />
                  </FormControl>
                  <FormDescription>Los clientes podrán recoger el vehículo en esta ubicación</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Amenidades */}
        <Card>
          <CardHeader>
            <CardTitle>Amenidades y Características</CardTitle>
            <CardDescription>Selecciona las características y comodidades que ofrece tu vehículo</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="amenities"
              render={() => (
                <FormItem>
                  <div className="mb-4">
                    <FormLabel>Amenidades</FormLabel>
                    <FormDescription>
                      Selecciona al menos una amenidad que ofrece tu vehículo
                    </FormDescription>
                    <FormMessage />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 mb-4">
                    {availableAmenities.map((amenity) => {
                      const amenities = form.getValues("amenities") || [];
                      const isChecked = amenities.includes(amenity.value);

                      return (
                        <div key={amenity.value} className="flex items-center space-x-2">
                          <Checkbox
                            id={`amenity-${amenity.value}`}
                            checked={isChecked}
                            onCheckedChange={(checked) =>
                              handleAmenityChange(amenity.value, checked as boolean)
                            }
                          />
                          <label
                            htmlFor={`amenity-${amenity.value}`}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            {amenity.label}
                          </label>
                        </div>
                      );
                    })}
                  </div>

                  <div className="flex flex-wrap gap-2">
                    {form.watch("amenities")?.map((value) => {
                      const amenity = availableAmenities.find((a) => a.value === value);
                      return (
                        <Badge key={value} variant="secondary" className="flex items-center gap-1">
                          {amenity?.label || value}
                          <button
                            type="button"
                            onClick={() => removeAmenity(value)}
                            className="rounded-full hover:bg-gray-200 p-0.5"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </Badge>
                      );
                    })}
                  </div>
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        <div className="flex justify-end gap-4">
          <Button type="button" variant="outline" onClick={() => router.back()}>
            Cancelar
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? "Registrando..." : "Registrar Vehículo"}
          </Button>
        </div>
      </form>
      </ScrollArea>

    </Form>
  )
}







