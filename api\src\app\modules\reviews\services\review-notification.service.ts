import { prisma } from '@/lib/prisma';
import { reviewNotificationQueue } from '@/lib/bullmq';
import { DateTime } from 'luxon';

export interface ReviewNotificationData {
  reservationId: string;
  userId: string;
  vehicleId: string;
  userEmail: string;
  userName: string;
  vehicleMake: string;
  vehicleModel: string;
  endDate: string;
}

export class ReviewNotificationService {
  /**
   * Completa reservas que ya han expirado
   * Criterios:
   * - Estado: confirmed
   * - Fecha de fin ya pasó
   */
  static async completeExpiredReservations(): Promise<number> {
    try {
      const now = DateTime.now().setZone('America/Mexico_City');

      console.log(`🔍 Buscando reservas confirmadas que deben completarse antes de ${now.toISO()}`);

      const result = await prisma.vehicleReservation.updateMany({
        where: {
          status: 'confirmed',
          endDate: {
            lt: now.toJSDate(), // Fecha de fin ya pasó
          }
        },
        data: {
          status: 'completed'
        }
      });

      console.log(`📋 ${result.count} reservas marcadas como completadas`);
      return result.count;

    } catch (error) {
      console.error('❌ Error completing expired reservations:', error);
      throw error;
    }
  }

  /**
   * Busca reservas elegibles para notificación de reseña
   * Criterios:
   * - Reserva terminó hace 1 mes máximo
   * - Estado: completed
   * - Host ha verificado la entrega (hostVerification: true)
   * - No tiene reseña asociada
   * - Usuario es de tipo client
   */
  static async findEligibleReservations(): Promise<ReviewNotificationData[]> {
    try {
      // Calcular fechas (1 mes atrás hasta ahora)
      const now = DateTime.now().setZone('America/Mexico_City');
      const oneMonthAgo = now.minus({ months: 1 }).startOf('day');

      console.log(`🔍 Buscando reservas elegibles desde ${oneMonthAgo.toISO()} hasta ${now.toISO()}`);

      const eligibleReservations = await prisma.vehicleReservation.findMany({
        where: {
          status: 'completed',
          hostVerification: true, // ✅ Host debe haber verificado la entrega
          endDate: {
            gte: oneMonthAgo.toJSDate(), // Desde hace 1 mes
            lte: now.toJSDate(), // Hasta ahora
          },
          vehicleReview: null, // No tiene reseña
          user: {
            userType: 'client' // Solo clientes
          }
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              userType: true
            }
          },
          vehicle: {
            select: {
              id: true,
              make: true,
              model: true
            }
          }
        }
      });

      console.log(`📋 Encontradas ${eligibleReservations.length} reservas elegibles`);

      return eligibleReservations.map(reservation => ({
        reservationId: reservation.id,
        userId: reservation.user.id,
        vehicleId: reservation.vehicle.id,
        userEmail: reservation.user.email,
        userName: reservation.user.name,
        vehicleMake: reservation.vehicle.make,
        vehicleModel: reservation.vehicle.model,
        endDate: reservation.endDate.toISOString(),
      }));

    } catch (error) {
      console.error('❌ Error finding eligible reservations:', error);
      throw error;
    }
  }

  /**
   * Verifica la entrega del vehículo por parte del host
   */
  static async verifyVehicleReturn(
    reservationId: string,
    hostId: string,
    notes?: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Verificar que la reserva existe y pertenece al host
      const reservation = await prisma.vehicleReservation.findFirst({
        where: {
          id: reservationId,
          vehicle: {
            hostId: hostId
          },
          status: 'completed'
        }
      });

      if (!reservation) {
        return {
          success: false,
          message: 'Reserva no encontrada o no pertenece a este host'
        };
      }

      if (reservation.hostVerification) {
        return {
          success: false,
          message: 'La entrega ya ha sido verificada anteriormente'
        };
      }

      // Marcar como verificado
      await prisma.vehicleReservation.update({
        where: { id: reservationId },
        data: {
          hostVerification: true,
          hostVerificationDate: new Date(),
          hostVerificationNotes: notes
        }
      });

      console.log(`✅ Host verificó entrega de reserva ${reservationId}`);

      return {
        success: true,
        message: 'Entrega verificada exitosamente'
      };

    } catch (error) {
      console.error('❌ Error verifying vehicle return:', error);
      throw error;
    }
  }

  /**
   * Programa notificaciones individuales para cada reserva elegible
   */
  static async scheduleNotifications(reservations: ReviewNotificationData[]): Promise<void> {
    try {
      // for (const reservation of reservations) {
      //   await reviewNotificationQueue.add(
      //     'send-review-notification',
      //     reservation,
      //     {
      //       attempts: 3,
      //       backoff: {
      //         type: 'exponential',
      //         delay: 2000,
      //       },
      //       removeOnComplete: 100,
      //       removeOnFail: 50,
      //     }
      //   );

      //   console.log(`📧 Notificación programada para reserva ${reservation.reservationId}`);
      // }
      await reviewNotificationQueue.addBulk(
        reservations.map(reservation => ({
          name: 'send-review-notification',
          data: reservation,
          opts: {
            attempts: 3,
            backoff: {
              type: 'exponential',
              delay: 2000,
            },
            removeOnComplete: 100,
            removeOnFail: 50,
          }
        }))
      );

      // await Promise.all(reservations.map(reservation => {
      //   return reviewNotificationQueue.add(
      //     'send-review-notification',
      //     reservation,
      //     {
      //       attempts: 3,
      //       backoff: {
      //         type: 'exponential',
      //         delay: 2000,
      //       },
      //       removeOnComplete: 100,
      //       removeOnFail: 50,
      //     }
      //   );
      // }));

      console.log(`✅ ${reservations.length} notificaciones programadas exitosamente`);
    } catch (error) {
      console.error('❌ Error scheduling notifications:', error);
      throw error;
    }
  }

  /**
   * Verifica si una reserva es elegible para reseña
   */
  static async isReservationEligibleForReview(reservationId: string, userId: string): Promise<{
    eligible: boolean;
    reason?: string;
    reservation?: any;
  }> {
    try {
      const reservation = await prisma.vehicleReservation.findFirst({
        where: {
          id: reservationId,
          userId: userId,
        },
        include: {
          vehicleReview: true,
          vehicle: {
            select: {
              id: true,
              make: true,
              model: true,
              year: true,
            }
          },
          user: {
            select: {
              id: true,
              name: true,
              userType: true,
            }
          }
        }
      });

      if (!reservation) {
        return {
          eligible: false,
          reason: 'Reserva no encontrada o no pertenece al usuario'
        };
      }

      if (reservation.user.userType !== 'client') {
        return {
          eligible: false,
          reason: 'Solo los clientes pueden dejar reseñas'
        };
      }

      if (reservation.status !== 'completed') {
        return {
          eligible: false,
          reason: 'La reserva debe estar completada para poder reseñar'
        };
      }

      if (reservation.vehicleReview) {
        return {
          eligible: false,
          reason: 'Ya existe una reseña para esta reserva'
        };
      }

      // ✅ Verificar que el host haya confirmado la entrega
      if (!reservation.hostVerification) {
        return {
          eligible: false,
          reason: 'El host aún no ha verificado la entrega del vehículo'
        };
      }

      // Verificar que la reserva haya terminado
      const now = DateTime.now().setZone('America/Mexico_City');
      const endDate = DateTime.fromJSDate(reservation.endDate).setZone('America/Mexico_City');

      if (endDate > now) {
        return {
          eligible: false,
          reason: 'La reserva aún no ha terminado'
        };
      }

      // Verificar ventana de tiempo (máximo 30 días después)
      const thirtyDaysAfterEnd = endDate.plus({ days: 30 });
      if (now > thirtyDaysAfterEnd) {
        return {
          eligible: false,
          reason: 'El período para dejar reseñas ha expirado (máximo 30 días)'
        };
      }

      return {
        eligible: true,
        reservation
      };

    } catch (error) {
      console.error('Error checking reservation eligibility:', error);
      return {
        eligible: false,
        reason: 'Error interno del servidor'
      };
    }
  }

  /**
   * Genera token único para link de reseña
   */
  static generateReviewToken(reservationId: string, userId: string): string {
    const data = `${reservationId}-${userId}-${Date.now()}`;
    return Buffer.from(data).toString('base64url');
  }

  /**
   * Valida token de reseña
   */
  static validateReviewToken(token: string, reservationId: string, userId: string): boolean {
    try {
      const decoded = Buffer.from(token, 'base64url').toString();
      const [tokenReservationId, tokenUserId] = decoded.split('-');

      return tokenReservationId === reservationId && tokenUserId === userId;
    } catch {
      return false;
    }
  }
}
