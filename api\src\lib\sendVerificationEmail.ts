import nodemailer from "nodemailer";


const transporter = nodemailer.createTransport({
  host: "smtp.gmail.com",
  port: 465,
  secure: true, // true for 465, false for other ports
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
});

export async function sendEmail({
  to,
  subject,
  text,
  html
}: {
  to: string;
  subject: string;
  text: string;
  html?: string;
}) {
  if (!process.env.EMAIL_USER) {
    throw new Error("EMAIL_USER environment variable is not set");
  }
  if (!process.env.EMAIL_PASS) {
    throw new Error("EMAIL_PASS environment variable is not set");
  }

  const message = {
    from: process.env.EMAIL_USER,
    to: to.toLowerCase().trim(),
    subject: subject.trim(),
    text: text.trim(),
    html: html?.trim(),
  };

  try {
    const result = await transporter.sendMail(message);

    console.log("Email sent:", result);
    return {
      success: true,
    };

  } catch (error) {
    console.error("Error sending email:", error);
    return {
      success: false,
      message: "Failed to send email. Please try again later.",
    }
  }
}

// Función para enviar email muy parecida a resend en cuanto a la devolución del objeto data y error
// y recibiendo los mismos params que puede recibir nodemailer


/** 
 * Enviar email con nodemailer
 */

export async function sendEmailWithNodemailer({
  to,
  subject,
  text,
  html,
}: {
  to: string;
  subject: string;
  text: string;
  html?: string;
}) {
  if (!process.env.EMAIL_USER) {
    throw new Error("EMAIL_USER environment variable is not set");
  }
  if (!process.env.EMAIL_PASS) {
    throw new Error("EMAIL_PASS environment variable is not set");
  }

  const message = {
    from: process.env.EMAIL_USER,
    to: to.toLowerCase().trim(),
    subject: subject.trim(),
    text: text.trim(),
    html: html?.trim(),
  };

  try {
    const result = await transporter.sendMail(message);

    console.log("Email sent:", result);
    return {
      data: result,
      error: null,
    };

  } catch (error) {
    console.error("Error sending email:", error);
    return {
      data: null,
      error,
    };
  }
}