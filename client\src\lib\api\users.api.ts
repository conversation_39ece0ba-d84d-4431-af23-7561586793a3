import { apiService } from "@/services/api";


export interface UserResponse {
  id: string;
  name: string;
  email: string;
  phone: string;
  status: string;
  image: string;
  isVerified: boolean;
  isBlocked: boolean;
  role: string;
  createdAt: string;
  updatedAt: string;
}



// Tipos para paginación
interface PaginationParams {
  page?: number;
  limit?: number;
}

interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export const usersApi = {
  // Obtener todos los hosts (admin)
  getAllHosts: async (params: PaginationParams = {}) => {
    const { page = 1, limit = 10 } = params;
    const result = await apiService.get<PaginatedResponse<UserResponse>>(`/admin/users/hosts?page=${page}&limit=${limit}`);
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.error);
    }
  },

  // Obtener todos los clientes (admin)
  getAllClients: async (params: PaginationParams = {}) => {
    const { page = 1, limit = 10 } = params;
    const result = await apiService.get<PaginatedResponse<UserResponse>>(`/admin/users/clients?page=${page}&limit=${limit}`);
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.error);
    }
  },

  // Verificar un usuario
  verifyUser: async (userId: string) => {
    const result = await apiService.patch<UserResponse>(`/admin/users/${userId}/verify`);
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.error);
    }
  },

};