"use client"

import { useState } from "react"
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query"
import { vehiclesApi } from "@/lib/api/vehicles.api"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, /* DialogTrigger */ } from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Eye, Search, CheckCircle, XCircle } from "lucide-react"
import Image from "next/image"
import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"
import { toast } from "react-hot-toast"
import { format } from "date-fns"
import Link from 'next/link'
import Swal from 'sweetalert2'

export default function AdminVehicleApprovalsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [rejectReason, setRejectReason] = useState("")
  const [selectedVehicleId, setSelectedVehicleId] = useState<string | null>(null)
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false)
  const queryClient = useQueryClient()

  // Obtener vehículos pendientes
  const { data: pendingVehicles, isLoading, error } = useQuery({
    queryKey: ['admin-pending-vehicles'],
    queryFn: vehiclesApi.admin.getPendingVehicles,
    staleTime: 60 * 1000, // 1 minuto
  })

  // Mutación para aprobar vehículo
  const approveMutation = useMutation({
    mutationFn: vehiclesApi.admin.approveVehicle,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-pending-vehicles'] })
      toast.success("Vehículo aprobado correctamente")
    },
    onError: (error) => {
      toast.error(`Error al aprobar vehículo: ${error.message}`)
    }
  })

  // Mutación para rechazar vehículo
  const rejectMutation = useMutation({
    mutationFn: ({ id, reason }: { id: string, reason: string }) =>
      vehiclesApi.admin.rejectVehicle(id, reason),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-pending-vehicles'] })
      toast.success("Vehículo rechazado correctamente")
      setIsRejectDialogOpen(false)
      setRejectReason("")
    },
    onError: (error) => {
      toast.error(`Error al rechazar vehículo: ${error.message}`)
    }
  })

  // Filtrar vehículos por término de búsqueda
  const filteredVehicles = pendingVehicles?.filter(
    (vehicle) =>
      vehicle.make.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vehicle.model.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vehicle.host.name.toLowerCase().includes(searchTerm.toLowerCase())
  ) || []

  // Manejar aprobación de vehículo con confirmación
  const handleApprove = (id: string) => {
    const vehicle = pendingVehicles?.find(v => v.id === id);

    Swal.fire({
      title: '¿Estás seguro?',
      text: `¿Deseas aprobar el vehículo ${vehicle?.make} ${vehicle?.model}?`,
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#10b981', // Color verde
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Sí, aprobar',
      cancelButtonText: 'Cancelar'
    }).then((result) => {
      if (result.isConfirmed) {
        approveMutation.mutate(id)
      }
    })
  }

  // Manejar rechazo de vehículo
  const handleReject = () => {
    if (!selectedVehicleId) return

    if (!rejectReason.trim()) {
      toast.error("Debe proporcionar una razón para el rechazo")
      return
    }

    rejectMutation.mutate({
      id: selectedVehicleId,
      reason: rejectReason
    })
  }

  // Abrir diálogo de rechazo
  const openRejectDialog = (id: string) => {
    setSelectedVehicleId(id)
    setIsRejectDialogOpen(true)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Aprobación de Vehículos</h1>
        <p className="text-muted-foreground">Revisa y aprueba los vehículos pendientes de los hosts</p>
      </div>

      {/* Filtros */}
      <div className="flex items-center space-x-2">
        <Search className="h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Buscar por marca, modelo o host..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="max-w-sm"
        />
      </div>

      {/* Tabla de vehículos */}
      <Card>
        <CardHeader>
          <CardTitle>Vehículos Pendientes de Aprobación</CardTitle>
          <CardDescription>
            {filteredVehicles.length} vehículos pendientes de revisión
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-2">
              {Array.from({ length: 5 }).map((_, i) => (
                <Skeleton key={i} className="h-12 w-full" />
              ))}
            </div>
          ) : error ? (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>
                No se pudieron cargar los vehículos pendientes
              </AlertDescription>
            </Alert>
          ) : filteredVehicles.length === 0 ? (
            <div className="text-center py-6">
              <p className="text-muted-foreground">No hay vehículos pendientes de aprobación</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Vehículo</TableHead>
                  <TableHead>Host</TableHead>
                  <TableHead>Fecha de Registro</TableHead>
                  <TableHead>Estado</TableHead>
                  <TableHead className="text-right">Acciones</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredVehicles.map((vehicle) => (
                  <TableRow key={vehicle.id}>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <div className="h-12 w-12 rounded-md overflow-hidden relative">
                          <Image
                            src={vehicle.images?.[0] || "/placeholder.svg"}
                            alt={`${vehicle.make} ${vehicle.model}`}
                            fill
                            className="object-cover"
                          />
                        </div>
                        <div>
                          <div className="font-medium">{vehicle.make} {vehicle.model}</div>
                          <div className="text-sm text-muted-foreground">{vehicle.year}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <div className="h-8 w-8 rounded-full overflow-hidden relative">
                          <Image
                            src={vehicle.host.image || "/placeholder.svg"}
                            alt={vehicle.host.name}
                            fill
                            className="object-cover"
                          />
                        </div>
                        <div className="font-medium">{vehicle.host.name}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {vehicle.createdAt ? format(new Date(vehicle.createdAt), 'dd/MM/yyyy') : 'N/A'}
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
                        Pendiente
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">

                        <Link href={`/dashboard/admin/vehicles/${vehicle.id}`} passHref>
                          <Button
                            variant="outline"
                            size="icon"
                          // onClick={() => router.push(`/dashboard/admin/vehicles/${vehicle.id}`)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </Link>
                        <Button
                          variant="default"
                          size="icon"
                          className="bg-green-600 hover:bg-green-700"
                          onClick={() => handleApprove(vehicle.id)}
                          disabled={approveMutation.isPending}
                        >
                          <CheckCircle className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="destructive"
                          size="icon"
                          onClick={() => openRejectDialog(vehicle.id)}
                          disabled={rejectMutation.isPending}
                        >
                          <XCircle className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Diálogo de rechazo */}
      <Dialog open={isRejectDialogOpen} onOpenChange={setIsRejectDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Rechazar Vehículo</DialogTitle>
            <DialogDescription>
              Proporcione una razón para el rechazo. Esta información será enviada al host.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <Textarea
              placeholder="Razón del rechazo (ej: Las imágenes no son claras, falta información, etc.)"
              value={rejectReason}
              onChange={(e) => setRejectReason(e.target.value)}
              rows={5}
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsRejectDialogOpen(false)}>
              Cancelar
            </Button>
            <Button
              variant="destructive"
              onClick={handleReject}
              disabled={rejectMutation.isPending}
            >
              Rechazar Vehículo
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
