import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"

const rentals = [
  {
    id: 1,
    client: {
      name: "<PERSON>",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "<PERSON><PERSON>",
    },
    vehicle: {
      name: "Tesla Model 3",
      image: "/placeholder.svg?height=40&width=40",
      details: "5 seats • White",
    },
    dates: {
      start: "May 10",
      end: "May 15, 2025",
      days: "5 days",
    },
    amount: "$550.00",
    status: "Active",
  },
  {
    id: 2,
    client: {
      name: "<PERSON>",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "DC",
    },
    vehicle: {
      name: "Porsche 911",
      image: "/placeholder.svg?height=40&width=40",
      details: "Sport • Black",
    },
    dates: {
      start: "May 12",
      end: "May 14, 2025",
      days: "2 days",
    },
    amount: "$1,200.00",
    status: "Upcoming",
  },
  {
    id: 3,
    client: {
      name: "<PERSON>",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "SM",
    },
    vehicle: {
      name: "Range Rover",
      image: "/placeholder.svg?height=40&width=40",
      details: "SUV • Silver",
    },
    dates: {
      start: "May 8",
      end: "May 12, 2025",
      days: "4 days",
    },
    amount: "$960.00",
    status: "Completed",
  },
]

export function RecentRentals() {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-md font-medium">Recent Rentals</CardTitle>
        <Button variant="ghost" size="sm" asChild>
          <Link href="/dashboard/rentals">View All</Link>
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {rentals.map((rental) => (
            <div key={rental.id} className="flex items-center gap-4">
              <Avatar>
                <AvatarImage src={rental.client.avatar || "/placeholder.svg"} alt={rental.client.name} />
                <AvatarFallback>{rental.client.initials}</AvatarFallback>
              </Avatar>
              <div className="flex-1 space-y-1">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <p className="text-sm font-medium">{rental.client.name}</p>
                  </div>
                  <Badge
                    variant={
                      rental.status === "Active" ? "default" : rental.status === "Upcoming" ? "secondary" : "outline"
                    }
                    className="text-xs"
                  >
                    {rental.status}
                  </Badge>
                </div>
                <div className="flex items-center gap-2">
                  <div className="h-8 w-8 rounded overflow-hidden">
                    <Image
                      src={rental.vehicle.image || "/placeholder.svg"}
                      alt={rental.vehicle.name}
                      width={32}
                      height={32}
                      className="h-full w-full object-cover"
                    />
                  </div>
                  <div>
                    <p className="text-xs font-medium">{rental.vehicle.name}</p>
                    <p className="text-xs text-muted-foreground">{rental.vehicle.details}</p>
                  </div>
                </div>
                <div className="flex items-center justify-between text-xs">
                  <span>
                    {rental.dates.start} - {rental.dates.end}
                    <span className="text-muted-foreground ml-1">({rental.dates.days})</span>
                  </span>
                  <span className="font-medium">{rental.amount}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
