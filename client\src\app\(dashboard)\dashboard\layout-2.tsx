"use client"

import type React from "react"
import { useEffect } from "react"
import { useRouter, usePathname } from "next/navigation"
import { AdminSidebar } from "@/components/layout/admin-sidebar"
import { HostSidebar } from "@/components/layout/host-sidebar"
import { ClientSidebar } from "@/components/layout/client-sidebar"
import { TopBar } from "@/components/layout/top-bar"
import { useUser } from '@/context/user-context'


export default function DashboardLayout2({
  children,
}: {
  children: React.ReactNode
}) {
  const router = useRouter()
  const pathname = usePathname()
  const { user } = useUser()

  // Verificar si la ruta es válida para el rol del usuario
  useEffect(() => {
    const userType = user.userType;

    if (user.role === "admin") {
      if (!pathname.includes('admin')) {
        router.push('/dashboard/admin')
      }
    } else if (userType === "host") {
      if (!pathname.includes('host')) {
        router.push('/dashboard/host')
      }
    } else if (userType === "client") {
      if (!pathname.includes('client')) {
        router.push('/dashboard/client')
      }
    } else {
      router.push('/dashboard')
    }

  }, [pathname, user.role, user.userType, router])

  const getSidebarComponent = () => {

    if (user.role === "admin") {
      return <AdminSidebar />
    } else if (user.userType === "host") {
      return <HostSidebar />
    } else if (user.userType === "client") {
      return <ClientSidebar />
    }

  }


  return (
    <>
      <div className="flex h-screen bg-background overflow-hidden">
        {/* Sidebar - solo visible en desktop */}
        <aside className="hidden md:block ">
          {getSidebarComponent()}
        </aside>

        <div className="flex flex-col flex-1 min-w-0">
          <TopBar />

          <main className="flex-1 overflow-y-auto p-6 bg-background">{children}</main>
        </div>
      </div>
    </>
  )
}
