import { Queue } from 'bullmq';
// import IORedis from 'ioredis';

const FLY_API_TOKEN = process.env.FLY_API_TOKEN!;
const FLY_APP_NAME = process.env.FLY_APP_NAME! || 'vexo-dashboard-develop';
const FLY_API_HOST = 'https://api.machines.dev/v1';

// 1. Función para obtener todas las máquinas de worker
async function getWorkerMachines() {
  const res = await fetch(`${FLY_API_HOST}/apps/${FLY_APP_NAME}/machines`, {
    headers: { Authorization: `Bearer ${FLY_API_TOKEN}` }
  });
  if (!res.ok) throw new Error(`Fly API error: ${res.statusText}`);
  const all = await res.json() as any[];
  return all.filter(m =>
    m.config?.metadata?.fly_process_group === 'worker'
  );
}

// // 2. Filtrar aquellas que están paradas
// async function getStoppedWorkerMachines() {
//   const workers = await getWorkerMachines();
//   return workers.filter(m => m.state === 'stopped');
// }

// 3. Arrancar una máquina por ID
async function startMachine(id: string) {
  const res = await fetch(
    `${FLY_API_HOST}/apps/${FLY_APP_NAME}/machines/${id}/start`,
    { method: 'POST', headers: { Authorization: `Bearer ${FLY_API_TOKEN}` } }
  );
  if (!res.ok) throw new Error(`Failed to start ${id}: ${res.statusText}`);
}

// 4. Lógica de escalado según la profundidad de la cola
export async function scaleWorkersAccordingToQueue(queue: Queue) {

  // Only run the code if we are running in Fly.io
  if (!process.env.FLY_MACHINE_ID) {
    console.log('Not running in Fly.io, skipping scaling and start machines logic');
    return;
  }

  // Obtener conteo de jobs pendientes
  const counts = await queue.getJobCounts('waiting', 'delayed');
  console.log('Counts:', counts);

  // return;
  const depth = counts.waiting + counts.delayed;
  console.log('Depth:', depth);

  // Define cuántos jobs por máquina consideras ideal
  const JOBS_PER_MACHINE = 100;

  const workersMachines = await getWorkerMachines();
  console.log('Workers machines:', workersMachines);
  console.log('Total workers machines:', workersMachines.length);

  // Calcula cuántas máquinas necesitas mínimo
  const ceil = Math.ceil(depth / JOBS_PER_MACHINE);
  console.log('Ceil:', ceil);
  const needed = Math.min(
    ceil,
    workersMachines.length
  );
  console.log('Needed:', needed);

  // Cuántas ya están arrancadas
  // const running = (await getWorkerMachines()).filter(m => m.state === 'started').length;
  const running = workersMachines.filter(m => m.state === 'started').length;
  console.log('Running:', running);

  const toStart = Math.max(0, needed - running);
  console.log('To start:', toStart);

  const stopped = workersMachines.filter(m => m.state === 'stopped');
  console.log('Total Stopped:', stopped.length);
  if (toStart > 0) {

    // const stopped = await getStoppedWorkerMachines();
    console.log('Starting machines...');
    for (let i = 0; i < toStart && i < stopped.length; i++) {
      console.log('Starting machine:', stopped[i].id);
      await startMachine(stopped[i].id);
    }
  }
}

// 5. Ejemplo de producer que escala antes de encolar
// const queue = new Queue('mi-cola', { connection: new IORedis(REDIS_CONFIG) });

// export async function enqueueJob(name: string, data: any) {
//   // Escala workers según la carga actual
//   await scaleWorkersAccordingToQueue(queue);

//   // Finalmente encola el job
//   await queue.add(name, data);
// }
