// Servicio de caché con límites de memoria y estrategia LRU

const max_cache_mb = +process.env.MAX_CACHE_MB! || 50;
const max_cache_size = +process.env.MAX_CACHE_SIZE! || 100;

// Configuración del caché
const MAX_CACHE_SIZE = max_cache_size; // Número máximo de archivos en caché
const MAX_CACHE_MEMORY = max_cache_mb * 1024 * 1024; // 200MB máximo en memoria
const CACHE_TTL = 60 * 60 * 1000; // 1 hora de TTL por defecto

// Estructura para el caché de archivos
type CacheEntry = {
  buffer: ArrayBuffer;
  etag: string;
  contentType: string;
  lastModified: string;
  size: number;
  lastAccessed: number;
};

class FileCache {
  private cache = new Map<string, CacheEntry>();
  private totalSize = 0;

  // Obtener un archivo del caché
  get(key: string): CacheEntry | undefined {
    const entry = this.cache.get(key);

    if (!entry) {
      return undefined;
    }

    // Verificar si ha expirado
    const now = Date.now();
    if (now - entry.lastAccessed > CACHE_TTL) {
      this.delete(key);
      return undefined;
    }

    // Actualizar el tiempo de último acceso
    entry.lastAccessed = now;
    this.cache.set(key, entry);

    return entry;
  }

  // Añadir un archivo al caché
  set(key: string, entry: Omit<CacheEntry, 'lastAccessed' | 'size'> & { buffer: ArrayBuffer }): void {
    const size = entry.buffer.byteLength;
    const newEntry: CacheEntry = {
      ...entry,
      size,
      lastAccessed: Date.now()
    };

    // Si el archivo ya existe en caché, actualizar su tamaño
    if (this.cache.has(key)) {
      const oldEntry = this.cache.get(key)!;
      this.totalSize -= oldEntry.size;
    }

    // Verificar si hay que hacer espacio en el caché
    if (this.cache.size >= MAX_CACHE_SIZE || this.totalSize + size > MAX_CACHE_MEMORY) {
      this.evictEntries(size);
    }

    // Añadir la nueva entrada
    this.cache.set(key, newEntry);
    this.totalSize += size;

    console.log(`Cache: Added file ${key}, size: ${(size / 1024 / 1024).toFixed(2)}MB, total cache size: ${(this.totalSize / 1024 / 1024).toFixed(2)}MB`);
  }

  // Eliminar una entrada del caché
  delete(key: string): void {
    if (this.cache.has(key)) {
      const entry = this.cache.get(key)!;
      this.totalSize -= entry.size;
      this.cache.delete(key);
    }
  }

  // Eliminar entradas para hacer espacio para un nuevo archivo
  private evictEntries(neededSpace: number): void {
    // Ordenar entradas por tiempo de último acceso (más antiguo primero)
    const entries = Array.from(this.cache.entries())
      .sort((a, b) => a[1].lastAccessed - b[1].lastAccessed);

    let freedSpace = 0;
    const keysToRemove: string[] = [];

    // Eliminar entradas hasta liberar suficiente espacio
    for (const [key, entry] of entries) {
      keysToRemove.push(key);
      freedSpace += entry.size;

      if (this.cache.size - keysToRemove.length < MAX_CACHE_SIZE &&
        freedSpace >= neededSpace) {
        break;
      }
    }

    // Eliminar las entradas seleccionadas
    for (const key of keysToRemove) {
      this.delete(key);
      console.log(`Cache: Evicted file ${key} (LRU strategy)`);
    }
  }

  // Limpiar entradas expiradas
  cleanExpired(): void {
    const now = Date.now();
    const keysToRemove: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.lastAccessed > CACHE_TTL) {
        keysToRemove.push(key);
      }
    }

    for (const key of keysToRemove) {
      this.delete(key);
      console.log(`Cache: Removed expired file ${key}`);
    }
  }

  // Obtener estadísticas del caché
  getStats(): { entries: number; totalSize: number; maxSize: number } {
    return {
      entries: this.cache.size,
      totalSize: this.totalSize,
      maxSize: MAX_CACHE_MEMORY
    };
  }
}

// Exportar una instancia única del caché
export const fileCache = new FileCache();

// Programar limpieza periódica del caché (cada hora)
setInterval(() => {
  fileCache.cleanExpired();
}, CACHE_TTL);