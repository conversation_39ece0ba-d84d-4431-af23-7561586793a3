"use client"
import { <PERSON>, MessageSquare, MoreHorizontal, <PERSON>r<PERSON>og, ShieldCheck, ShieldAlert, Clock } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { But<PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

// Mock data
const hosts = [
  {
    id: 1,
    name: "<PERSON>",
    avatar: "/placeholder.svg?height=40&width=40",
    initials: "J<PERSON>",
    email: "<EMAIL>",
    phone: "+****************",
    cars: 4,
    rating: 4.8,
    status: "Verified",
    joinDate: "April 2023",
  },
  {
    id: 2,
    name: "<PERSON>",
    avatar: "/placeholder.svg?height=40&width=40",
    initials: "<PERSON>",
    email: "<EMAIL>",
    phone: "+****************",
    cars: 7,
    rating: 4.9,
    status: "Verified",
    joinDate: "March 2023",
  },
  {
    id: 3,
    name: "Robert Chen",
    avatar: "/placeholder.svg?height=40&width=40",
    initials: "RC",
    email: "<EMAIL>",
    phone: "+****************",
    cars: 2,
    rating: 4.6,
    status: "Pending",
    joinDate: "April 2023",
  },
  {
    id: 4,
    name: "Sophia Martinez",
    avatar: "/placeholder.svg?height=40&width=40",
    initials: "SM",
    email: "<EMAIL>",
    phone: "+****************",
    cars: 5,
    rating: 4.7,
    status: "Verified",
    joinDate: "January 2023",
  },
  {
    id: 5,
    name: "Daniel Johnson",
    avatar: "/placeholder.svg?height=40&width=40",
    initials: "DJ",
    email: "<EMAIL>",
    phone: "+****************",
    cars: 3,
    rating: 4.5,
    status: "Pending",
    joinDate: "February 2023",
  },
  {
    id: 6,
    name: "Thomas Anderson",
    avatar: "/placeholder.svg?height=40&width=40",
    initials: "TA",
    email: "<EMAIL>",
    phone: "+****************",
    cars: 10,
    rating: 4.9,
    status: "Suspended",
    joinDate: "June 2022",
  },
]

export function HostTable() {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead className="w-[250px]">Name</TableHead>
          <TableHead>Email</TableHead>
          <TableHead>Phone</TableHead>
          <TableHead className="text-center">Cars</TableHead>
          <TableHead className="text-center">Rating</TableHead>
          <TableHead>Status</TableHead>
          <TableHead className="text-right">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {hosts.map((host) => (
          <TableRow key={host.id}>
            <TableCell className="font-medium">
              <div className="flex items-center gap-2">
                <Avatar>
                  <AvatarImage src={host.avatar || "/placeholder.svg"} alt={host.name} />
                  <AvatarFallback>{host.initials}</AvatarFallback>
                </Avatar>
                <div>
                  <div className="font-medium">{host.name}</div>
                  <div className="text-xs text-muted-foreground">Member since {host.joinDate}</div>
                </div>
              </div>
            </TableCell>
            <TableCell>{host.email}</TableCell>
            <TableCell>{host.phone}</TableCell>
            <TableCell className="text-center">{host.cars}</TableCell>
            <TableCell className="text-center">
              <div className="flex items-center justify-center">
                <span className="font-medium">{host.rating}</span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  className="w-4 h-4 ml-1 text-yellow-500"
                >
                  <path
                    fillRule="evenodd"
                    d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
            </TableCell>
            <TableCell>
              <Badge
                variant={host.status === "Verified" ? "success" : host.status === "Pending" ? "warning" : "destructive"}
                className={
                  host.status === "Verified"
                    ? "bg-green-100 text-green-800 hover:bg-green-100"
                    : host.status === "Pending"
                      ? "bg-yellow-100 text-yellow-800 hover:bg-yellow-100"
                      : "bg-red-100 text-red-800 hover:bg-red-100"
                }
              >
                {host.status}
              </Badge>
            </TableCell>
            <TableCell className="text-right">
              <div className="flex justify-end gap-2">
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <Eye className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <MessageSquare className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <UserCog className="h-4 w-4" />
                </Button>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem>
                      <Eye className="mr-2 h-4 w-4" />
                      <span>View Details</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <MessageSquare className="mr-2 h-4 w-4" />
                      <span>Send Message</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <UserCog className="mr-2 h-4 w-4" />
                      <span>Edit Profile</span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    {host.status === "Verified" ? (
                      <DropdownMenuItem className="text-yellow-600">
                        <Clock className="mr-2 h-4 w-4" />
                        <span>Set as Pending</span>
                      </DropdownMenuItem>
                    ) : host.status === "Pending" ? (
                      <DropdownMenuItem className="text-green-600">
                        <ShieldCheck className="mr-2 h-4 w-4" />
                        <span>Verify Host</span>
                      </DropdownMenuItem>
                    ) : (
                      <DropdownMenuItem className="text-green-600">
                        <ShieldCheck className="mr-2 h-4 w-4" />
                        <span>Reactivate Host</span>
                      </DropdownMenuItem>
                    )}
                    {host.status !== "Suspended" && (
                      <DropdownMenuItem className="text-red-600">
                        <ShieldAlert className="mr-2 h-4 w-4" />
                        <span>Suspend Host</span>
                      </DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}
