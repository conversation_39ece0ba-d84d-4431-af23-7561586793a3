import { Elysia, t } from 'elysia';
import { authMiddleware } from '@/app/middlewares/auth.middleware';
import { prisma } from '@/lib/prisma';
import { HttpException } from '@/exceptions/HttpExceptions';

const VALID_USER_TYPES = ['client', 'host'] as const;
type UserType = typeof VALID_USER_TYPES[number];

export const switchRoleController = new Elysia({ prefix: '/user' })
  .use(authMiddleware)
  .patch('/switch-role', async ({ body, user }) => {
    const { userType } = body as { userType: UserType };

    // Validar que el userType sea válido
    if (!VALID_USER_TYPES.includes(userType)) {
      throw HttpException.BadRequest('Invalid user type. Must be "client" or "host"');
    }

    // Obtener el usuario actual con sus tipos disponibles
    const currentUser = await prisma.user.findUnique({
      where: { id: user.id },
      select: {
        id: true,
        userType: true,
        availableUserTypes: true,
        isHostVerified: true,
      }
    });

    if (!currentUser) {
      throw HttpException.NotFound('User not found');
    }

    // Verificar que el usuario tenga acceso a este tipo
    if (!currentUser.availableUserTypes.includes(userType)) {
      throw HttpException.Forbidden(`You don't have access to ${userType} role. Please request access first.`);
    }

    // Si ya está en ese rol, no hacer nada
    if (currentUser.userType === userType) {
      return {
        success: true,
        message: `Already in ${userType} mode`,
        userType: currentUser.userType,
        availableUserTypes: currentUser.availableUserTypes,
      };
    }

    // Actualizar el userType del usuario
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: { userType },
      select: {
        id: true,
        userType: true,
        availableUserTypes: true,
        isHostVerified: true,
      }
    });

    return {
      success: true,
      message: `Cambiado a ${userType === 'host' ? 'Anfitrión' : 'Cliente'} satisfactoriamente.`,
      userType: updatedUser.userType,
      availableUserTypes: updatedUser.availableUserTypes,
      isHostVerified: updatedUser.isHostVerified,
    };
  }, {
    body: t.Object({
      userType: t.String()
    }),
    detail: {
      tags: ['User'],
      summary: 'Switch user role',
      description: 'Switch between available user roles (client/host)'
    }
  });
