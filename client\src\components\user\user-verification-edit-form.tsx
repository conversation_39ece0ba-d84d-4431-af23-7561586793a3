import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useMutation } from '@tanstack/react-query';
import { verificationApi, VerificationStatus } from '@/lib/api/user-verification.api';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Form } from '@/components/ui/form';
import toast from 'react-hot-toast';
import FileUploadInput from '../forms/file-upload-input';
import DocumentPreview from '../forms/document-preview';

// Esquema de validación para edición - todos los archivos son opcionales
const verificationEditFormSchema = z.object({
  idFront: z.array(z.custom<File>())
    .optional()
    .refine((files) => !files || files.every((file) => file.size <= 2 * 1024 * 1024), {
      message: "El archivo debe pesar menos de 2MB",
    }),
  idBack: z.array(z.custom<File>())
    .optional()
    .refine((files) => !files || files.every((file) => file.size <= 2 * 1024 * 1024), {
      message: "El archivo debe pesar menos de 2MB",
    }),
  driverLicense: z.array(z.custom<File>())
    .optional()
    .refine((files) => !files || files.every((file) => file.size <= 2 * 1024 * 1024), {
      message: "El archivo debe pesar menos de 2MB",
    }),
  addressProof: z.array(z.custom<File>())
    .optional()
    .refine((files) => !files || files.every((file) => file.size <= 2 * 1024 * 1024), {
      message: "El archivo debe pesar menos de 2MB",
    }),
  selfieWithId: z.array(z.custom<File>())
    .optional()
    .refine((files) => !files || files.every((file) => file.size <= 2 * 1024 * 1024), {
      message: "El archivo debe pesar menos de 2MB",
    }),
});

type VerificationEditFormValues = z.infer<typeof verificationEditFormSchema>;

type UserVerificationEditFormProps = {
  verification: VerificationStatus['data']['verification'];
};

export function UserVerificationEditForm({ verification }: UserVerificationEditFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [documentsToRemove, setDocumentsToRemove] = useState<{
    idFront?: boolean;
    idBack?: boolean;
    driverLicense?: boolean;
    addressProof?: boolean;
    selfieWithId?: boolean;
  }>({});


  const form = useForm<VerificationEditFormValues>({
    resolver: zodResolver(verificationEditFormSchema),
    defaultValues: {
      idFront: [],
      idBack: [],
      driverLicense: [],
      addressProof: [],
      selfieWithId: [],
    }
  });

  const uploadMutation = useMutation({
    mutationFn: (data: FormData) => verificationApi.user.editDocuments(data),
    onSuccess: () => {
      toast.success("Documentos actualizados correctamente\nTe notificaremos cuando sean revisados.")
      setIsLoading(false);

      setTimeout(() => {
        window.location.reload();
      }, 1200);
    },
    onError: (error) => {
      console.error("Error al actualizar documentos:", error)
      toast.error("No se pudieron actualizar los documentos. Por favor, intenta de nuevo.")
      setIsLoading(false);
    }
  });

  const onSubmit = (data: VerificationEditFormValues) => {
    setIsLoading(true);

    const formData = new FormData();

    // Solo agregar archivos que se hayan seleccionado para cambiar
    if (data.idFront && data.idFront[0]) formData.append('idFront', data.idFront[0]);
    if (data.idBack && data.idBack[0]) formData.append('idBack', data.idBack[0]);
    if (data.driverLicense && data.driverLicense[0]) formData.append('driverLicense', data.driverLicense[0]);
    if (data.addressProof && data.addressProof[0]) formData.append('addressProof', data.addressProof[0]);
    if (data.selfieWithId && data.selfieWithId[0]) formData.append('selfieWithId', data.selfieWithId[0]);

    // Agregar información sobre documentos a eliminar
    Object.entries(documentsToRemove).forEach(([key, shouldRemove]) => {
      if (shouldRemove) {
        console.log(`Removing ${key} with remove_${key}`);
        formData.append(`remove_${key}`, 'true');
      }
    });

    uploadMutation.mutate(formData);
  };

  // Manejar eliminación de documentos existentes
  const handleRemoveDocument = (documentType: keyof typeof documentsToRemove) => {
    setDocumentsToRemove(prev => ({
      ...prev,
      [documentType]: true
    }));
  };

  // Verificar si un documento debe mostrarse (no está marcado para eliminar)
  const shouldShowDocument = (documentType: keyof typeof documentsToRemove, documentKey?: string) => {
    return documentKey && !documentsToRemove[documentType];
  };

  // Función para cancelar y restaurar estado original
  const handleCancel = () => {
    // Verificar si hay cambios para cancelar
    const formValues = form.getValues();
    const hasFileChanges = Object.values(formValues).some(files => files && files.length > 0);
    const hasRemovalChanges = Object.values(documentsToRemove).some(Boolean);

    if (!hasFileChanges && !hasRemovalChanges) {
      toast("No hay cambios para cancelar.", { icon: 'ℹ️' });
      return;
    }

    // Resetear el formulario a valores vacíos
    form.reset({
      idFront: [],
      idBack: [],
      driverLicense: [],
      addressProof: [],
      selfieWithId: [],
    });

    // Resetear documentos marcados para eliminar
    setDocumentsToRemove({});

    toast.success("Cambios cancelados. Se restauraron los documentos originales.");
  };

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle>Editar verificación de identidad</CardTitle>
        <CardDescription>
          Actualiza solo los documentos que necesites corregir. Los documentos actuales se muestran a continuación.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">

              {/* Identificación (Frente) */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium">Identificación (Frente)</h3>
                {shouldShowDocument('idFront', verification?.idFront) ? (
                  <DocumentPreview
                    documentKey={verification.idFront!}
                    label="Identificación (Frente)"
                    onRemove={() => handleRemoveDocument('idFront')}
                    showButtonOnHover={false}
                  />
                ) : (
                  <FileUploadInput
                    form={form}
                    name="idFront"
                    label="Nueva identificación (Frente)"
                    description="Sube una nueva foto del frente de tu identificación oficial"
                    maxSize={2 * 1024 * 1024}
                    maxFiles={1}
                    accept="image/*"
                  />
                )}
              </div>

              {/* Identificación (Reverso) */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium">Identificación (Reverso)</h3>
                {shouldShowDocument('idBack', verification?.idBack) ? (
                  <DocumentPreview
                    documentKey={verification.idBack!}
                    label="Identificación (Reverso)"
                    onRemove={() => handleRemoveDocument('idBack')}
                    showButtonOnHover={false}
                  />
                ) : (
                  <FileUploadInput
                    form={form}
                    name="idBack"
                    label="Nueva identificación (Reverso)"
                    description="Sube una nueva foto del reverso de tu identificación oficial"
                    maxSize={2 * 1024 * 1024}
                    maxFiles={1}
                    accept="image/*"
                  />
                )}
              </div>

              {/* Licencia de conducir */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium">Licencia de conducir</h3>
                {shouldShowDocument('driverLicense', verification?.driverLicense) ? (
                  <DocumentPreview
                    documentKey={verification.driverLicense!}
                    label="Licencia de conducir"
                    onRemove={() => handleRemoveDocument('driverLicense')}
                    showButtonOnHover={false}
                  />
                ) : (
                  <FileUploadInput
                    form={form}
                    name="driverLicense"
                    label="Nueva licencia de conducir"
                    description="Sube una nueva foto de tu licencia de conducir vigente"
                    maxSize={2 * 1024 * 1024}
                    maxFiles={1}
                    accept="image/*"
                  />
                )}
              </div>

              {/* Comprobante de domicilio */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium">Comprobante de domicilio</h3>
                {shouldShowDocument('addressProof', verification?.addressProof) ? (
                  <DocumentPreview
                    documentKey={verification.addressProof!}
                    label="Comprobante de domicilio"
                    onRemove={() => handleRemoveDocument('addressProof')}
                    showButtonOnHover={false}
                  />
                ) : (
                  <FileUploadInput
                    form={form}
                    name="addressProof"
                    label="Nuevo comprobante de domicilio"
                    description="Sube un nuevo comprobante de domicilio reciente (no mayor a 3 meses)"
                    maxSize={2 * 1024 * 1024}
                    maxFiles={1}
                    accept="image/*,application/pdf"
                  />
                )}
              </div>

              {/* Selfie con identificación */}
              <div className="md:col-span-2 space-y-4">
                <h3 className="text-sm font-medium">Selfie con identificación</h3>
                {shouldShowDocument('selfieWithId', verification?.selfieWithId) ? (
                  <DocumentPreview
                    documentKey={verification.selfieWithId!}
                    label="Selfie con identificación"
                    onRemove={() => handleRemoveDocument('selfieWithId')}
                    showButtonOnHover={false}
                  />
                ) : (
                  <FileUploadInput
                    form={form}
                    name="selfieWithId"
                    label="Nueva selfie con identificación"
                    description="Tómate una nueva foto sosteniendo tu identificación junto a tu rostro"
                    maxSize={2 * 1024 * 1024}
                    maxFiles={1}
                    accept="image/*"
                  />
                )}
              </div>
            </div>

            <div className="flex gap-4">
              <Button
                type="button"
                variant="outline"
                className="flex-1"
                onClick={handleCancel}
                disabled={isLoading}
              >
                Cancelar cambios
              </Button>
              <Button type="submit" className="flex-1" disabled={isLoading}>
                {isLoading ? "Actualizando documentos..." : "Actualizar documentos"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
      <CardFooter className="flex flex-col text-sm text-muted-foreground">
        <p>Solo actualiza los documentos que necesites corregir. Los demás se mantendrán sin cambios.</p>
      </CardFooter>
    </Card>
  );
}
