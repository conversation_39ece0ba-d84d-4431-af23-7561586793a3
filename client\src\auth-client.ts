import { createAuthClient } from "better-auth/react";
import { adminClient, /* multiSessionClient, */ organizationClient } from "better-auth/client/plugins";
import { getCookie } from 'cookies-next/client';

import env from "@/constants/env";
import { BEARER_COOKIE_NAME } from './constants';

export const authClient = createAuthClient({
  baseURL: env.NEXT_PUBLIC_API_URL,

  plugins: [
    adminClient(),
    organizationClient(),
    // multiSessionClient()
  ],
  credentials: 'include',
  fetchOptions: {
    onError: (ctx) => {
      console.log('Error:', ctx.error);
      console.log('Response:', ctx.response.url);
    },
    headers: {
      'x-dashboard-call': 'true'
    },
    auth: {
      type: 'Bearer',
      token: () => {
        const token = getCookie(BEARER_COOKIE_NAME);
        if (token) {
          return token; // No truncar el token
        }
      }
    }
  },
});