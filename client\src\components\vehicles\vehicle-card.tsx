import { Card, CardContent } from "@/components/ui/card"
import { MapPin, Star } from "lucide-react"
import Image from "next/image"
import { DateTime } from 'luxon';
import FavoriteButton from "./favorite-button"
// import Link from "next/link"

// Mapeo de tipos de carrocería en español
const bodyTypeLabels: Record<string, string> = {
  sedan: "Sedan",
  suv: "SUV",
  hatchback: "Hatchback",
  pickup: "Pickup",
  coupe: "Coupe",
  convertible: "Convertible",
  wagon: "Wagon",
  van: "Van",
  minivan: "Minivan",
  targa: "Targa",
  doublecab: "Doble Cabina",
  truck: "Camioneta"
};

interface VehicleCardProps {
  id: string
  make: string
  model: string
  price: number
  rating: number
  reviews: number
  images: string[] | any
  engineSize: number
  transmission: string
  trim: string
  bodyType: string
  features: {
    fuelType: string
    seats: number
    mileage: number
    registrationNumber: string
    insurancePolicy: string
    rules: string
    location: string
    weeklyRate: number
    monthlyRate: number
  }
  year: number
  createdAt: string
}

export default function VehicleCard({
  id,
  make,
  model,
  price,
  rating,
  reviews,
  images,
  features,
  year,
  engineSize,
  transmission,
  trim,
  bodyType,
  createdAt
}: VehicleCardProps) {
  // Obtener valores clave para mostrar en la tarjeta
  const transmissionLabel = transmission === 'automatic' ? 'Automático' : 'Manual';
  const mileage = features?.mileage ? `${features.mileage.toLocaleString()} km` : '';
  const location = features?.location || '';
  const bodyTypeLabel = bodyTypeLabels[bodyType] || bodyType;

  const isRecent = (createdAt: string) => {
    const createdDate = DateTime.fromISO(createdAt);
    const sevenDaysAgo = DateTime.now().minus({ days: 7 });
    return createdDate > sevenDaysAgo;
  }

  return (
    <Card className="overflow-hidden hover:shadow-md pt-0 transition-shadow h-full">
      <div className="relative h-64 sm:h-80 md:h-48 xl:h-64">
        {isRecent(createdAt) && (
          <div className="absolute top-2 left-2 z-10">
            <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-md">
              Recién publicado
            </span>
          </div>
        )}
          <Image
            src={Array.isArray(images) && images.length > 0 ? images[0] : "/placeholder.svg"}
            alt={`${make} ${model}`}
          width={300}
          height={200}
          className="w-full h-64 sm:h-80 md:h-48 xl:h-64 object-cover"
        />
        <div className="absolute top-2 right-2 z-10">
          <FavoriteButton vehicleId={id} className="bg-white hover:bg-gray-50" />
        </div>
      </div>
      <CardContent className="px-4">
        <div className="mb-2">
            <h3 className="font-semibold text-lg hover:text-blue-600 transition-colors">
              {make} {model}
          </h3>
          <div className="text-sm text-gray-600">
            {engineSize}L {trim} {bodyTypeLabel}
          </div>
          <div className="text-sm text-gray-600">
            {year && `${year} • `}{mileage}
            {transmission && ` • ${transmissionLabel}`}
          </div>
        </div>

        <div className="mt-4">
          <div className="text-sm text-gray-600">Precio desde</div>
          <div className="flex items-baseline">
            <span className="font-bold text-xl">${price.toLocaleString()}</span>
            <span className="text-gray-500 text-sm ml-1">
              / día
            </span>
          </div>
        </div>

        {/* Rating */}
        {reviews > 0 && (
          <div className="flex items-center mt-2">
            <Star className="h-4 w-4 text-yellow-500 fill-current" />
            <span className="font-medium ml-1">{rating.toFixed(1)}</span>
            <span className="text-gray-500 text-sm ml-1">({reviews} reseñas)</span>
          </div>
        )}

        {location && (
          <div className="flex items-center mt-3 pt-3 border-t border-gray-100">
            <MapPin className="h-4 w-4 text-gray-400 mr-1" />
            <span className="text-sm text-gray-600">{location}</span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}


