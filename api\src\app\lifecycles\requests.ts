import { context, trace } from '@opentelemetry/api';

export function getRequestId() {
  const span = trace.getSpan(context.active());
  const requestId = span?.spanContext().traceId || '';
  return requestId;
}

export function onRequest(c: Record<string, any>) {
  const start = performance.now();
  c.store.start = start;

  const requestId = getRequestId();
  c.store.requestId = requestId;
  c.set.headers['X-Request-Id'] = requestId;
}