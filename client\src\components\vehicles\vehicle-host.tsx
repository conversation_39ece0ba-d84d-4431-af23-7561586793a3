import { Button } from "@/components/ui/button"
import { Host } from '@/lib/api/vehicles.api'
import { DateTime } from 'luxon'
import Image from 'next/image'

interface HostProps {
  host: Host
}

export default function VehicleHost({ host }: HostProps) {
  if (!host) {
    return (
      <div className="p-4 bg-gray-50 rounded-lg">
        <p className="text-gray-500 text-center">Información del anfitrión no disponible.</p>
      </div>
    )
  }


  const parseDate = (date: string) => {
    // parse it like this format: "25 de junio de 2025" using luxon, date is a iso string
    return DateTime.fromISO(date).toFormat("dd 'de' MMMM 'de' yyyy", { locale: "es" })
  }

  return (

    <>
      <div className="border rounded-lg p-6 hidden lg:block">
        <div className="flex items-center gap-4 mb-4">
          <div className="w-12 h-12 rounded-full bg-gray-200 overflow-hidden relative">
            <Image
              src={host.image || "/placeholder.svg?height=48&width=48"}
              alt="Host"
              fill
              className="object-cover"
            />
          </div>
          <div>
            <h3 className="font-medium">{host.name}</h3>
            <p className="text-sm text-gray-500">Miembro desde {parseDate(host.createdAt)}</p>
          </div>
        </div>

        <Button variant="outline" className="w-full">
          <span className="font-medium">Contact Host</span>
        </Button>
      </div>
    </>
  )
}