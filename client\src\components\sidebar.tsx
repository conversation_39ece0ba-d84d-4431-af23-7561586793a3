"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { usePathname } from "next/navigation"
import {
  LayoutDashboard,
  Users,
  UserCircle,
  Car,
  CalendarClock,
  CreditCard,
  BarChart,
  LifeBuoy,
  User,
  Settings,
  Menu,
} from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { UserProfileSidebar } from "./layout/user-profile-sidebar"

const menuItems = [
  {
    title: "MAIN MENU",
    items: [
      {
        name: "Dashboard",
        href: "/dashboard",
        icon: LayoutDashboard,
      },
      {
        name: "Hosts",
        href: "/dashboard/hosts",
        icon: Users,
      },
      {
        name: "Clients",
        href: "/dashboard/clients",
        icon: UserCircle,
      },
      {
        name: "Vehicles",
        href: "/dashboard/vehicles",
        icon: Car,
      },
      {
        name: "Reservations",
        href: "/dashboard/reservations",
        icon: CalendarClock,
      },
      {
        name: "Payouts",
        href: "/dashboard/payouts",
        icon: CreditCard,
        badge: undefined,
      },
    ],
  },
  {
    title: "ANALYTICS",
    items: [
      {
        name: "Reports",
        href: "/dashboard/reports",
        icon: BarChart,
      },
      {
        name: "Support",
        href: "/dashboard/support",
        icon: LifeBuoy,
      },
    ],
  },
  {
    title: "SETTINGS",
    items: [
      {
        name: "Profile",
        href: "/dashboard/profile",
        icon: User,
      },
      {
        name: "Settings",
        href: "/dashboard/settings",
        icon: Settings,
      },
    ],
  },
]

export function Sidebar() {
  const pathname = usePathname()
  const [open, setOpen] = useState(false)

  const SidebarContent = () => (
    <div className="w-64 bg-[#0f2a5c] text-white flex flex-col h-full">
      <div className="p-4 flex items-center">
        <div className="flex items-center">
          <div className="bg-white rounded-md p-1 mr-2">
            <Image src="/placeholder.svg?height=24&width=24" alt="Autoop Logo" width={24} height={24} />
          </div>
          <span className="text-xl font-bold">Autoop</span>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto py-2">
        {menuItems.map((section, i) => (
          <div key={i} className="px-4 py-2">
            <div className="text-xs font-semibold text-gray-400 mb-2">{section.title}</div>
            <ul className="space-y-1">
              {section.items.map((item, j) => (
                <li key={j}>
                  <Link
                    href={item.href}
                    className={cn(
                      "flex items-center px-3 py-2 rounded-md text-sm font-medium",
                      pathname === item.href
                        ? "bg-white/10 text-white"
                        : "text-gray-300 hover:bg-white/5 hover:text-white",
                    )}
                    onClick={() => setOpen(false)}
                  >
                    <item.icon className="h-5 w-5 mr-3" />
                    <span>{item.name}</span>
                    {item.badge && (
                      <span className="ml-auto bg-primary text-white text-xs font-semibold px-2 py-0.5 rounded-full">
                        {item.badge}
                      </span>
                    )}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>

      <UserProfileSidebar />
    </div>
  )

  // Versión móvil con Sheet
  const MobileSidebar = () => (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon" className="md:hidden">
          <Menu className="h-6 w-6" />
          <span className="sr-only">Toggle Menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="p-0 w-72">
        <SidebarContent />
      </SheetContent>
    </Sheet>
  )

  return (
    <>
      <MobileSidebar />
      <aside className="hidden md:block">
        <SidebarContent />
      </aside>
    </>
  )
}
