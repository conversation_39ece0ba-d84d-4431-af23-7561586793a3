import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"

const activities = [
  {
    id: 1,
    type: "new_reservation",
    title: "Nueva reserva",
    description: "Tesla Model 3 - 5 días",
    client: "<PERSON>",
    time: "Hace 10 minutos",
    status: "pending",
  },
  {
    id: 2,
    type: "reservation_completed",
    title: "Reserva completada",
    description: "BMW X5 - 3 días",
    client: "<PERSON>",
    time: "Hace 2 horas",
    status: "completed",
  },
  {
    id: 3,
    type: "payment_received",
    title: "Pago recibido",
    description: "$450.00 - Mercedes E-Class",
    client: "<PERSON>",
    time: "Hace 1 día",
    status: "paid",
  },
]

export function HostRecentActivity() {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-md font-medium">Actividad Reciente</CardTitle>
        <Button variant="ghost" size="sm" asChild>
          <Link href="/dashboard/host/activity">Ver Todo</Link>
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => (
            <div key={activity.id} className="flex items-start gap-4">
              <Avatar className="bg-primary text-white">
                <AvatarFallback>
                  {activity.client
                    .split(" ")
                    .map((n) => n[0])
                    .join("")}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 space-y-1">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium">{activity.title}</p>
                  <Badge
                    variant={
                      activity.status === "pending"
                        ? "secondary"
                        : activity.status === "completed"
                          ? "outline"
                          : "default"
                    }
                    className="text-xs"
                  >
                    {activity.status === "pending"
                      ? "Pendiente"
                      : activity.status === "completed"
                        ? "Completado"
                        : "Pagado"}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">{activity.description}</p>
                <p className="text-xs text-muted-foreground">{activity.time}</p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
