import { Elysia, t } from "elysia";
import { authMiddleware } from "@/app/middlewares/auth.middleware";
import { checkAdmin } from '@/lib/check-admin';
import { ReviewsService } from './reviews.service';
import { prisma } from '@/lib/prisma';

// Esquemas de validación
const createReviewSchema = t.Object({
  reservationId: t.String(),
  // rating: t.Optional(t.Number({ minimum: 1, maximum: 5 })),
  rating: t.Number({ minimum: 1, maximum: 5 }),
  comment: t.Optional(t.String({ maxLength: 1000 }))
});

const getReviewsQuerySchema = t.Object({
  vehicleId: t.Optional(t.String()),
  userId: t.Optional(t.String()),
  page: t.Optional(t.String()),
  limit: t.Optional(t.String()),
  sortBy: t.Optional(t.Union([
    t.Literal('newest'),
    t.Literal('oldest'),
    t.Literal('rating_high'),
    t.Literal('rating_low')
  ]))
});

// Controlador público para reseñas
export const publicReviewsController = new Elysia({ prefix: '/public/reviews' })
  .get('/vehicle/:vehicleId', async ({ params, query }) => {
    const { vehicleId } = params;
    const queryParams = {
      vehicleId,
      page: query.page ? parseInt(query.page as string) : 1,
      limit: query.limit ? parseInt(query.limit as string) : 10,
      sortBy: query.sortBy as any || 'newest'
    };

    return await ReviewsService.getReviews(queryParams);
  })
  .get('/vehicle/:vehicleId/stats', async ({ params }) => {
    return await ReviewsService.getVehicleReviewStats(params.vehicleId);
  });

// Controlador para usuarios autenticados
export const userReviewsController = new Elysia({ prefix: '/user/reviews' })
  .use(authMiddleware)
  .post('/', async ({ body, user }) => {
    // Validar que el usuario sea de tipo cliente
    if (user.userType !== 'client') {
      return {
        success: false,
        error: 'Solo los usuarios de tipo cliente pueden crear reseñas',
        status: 403
      };
    }

    return await ReviewsService.createReview({
      ...body,
      userId: user.id
    });
  }, {
    body: createReviewSchema
  })
  .get('/', async ({ user, query }) => {
    const queryParams = {
      userId: user.id,
      page: query.page ? parseInt(query.page as string) : 1,
      limit: query.limit ? parseInt(query.limit as string) : 10,
      sortBy: query.sortBy as any || 'newest'
    };

    return await ReviewsService.getReviews(queryParams);
  })
  .get('/:reviewId', async ({ params, user }) => {
    return await ReviewsService.getReviewById(params.reviewId, user.id);
  })
  .get('/eligibility/:reservationId', async ({ params, user }) => {
    return await ReviewsService.checkReviewEligibility(params.reservationId, user.id);
  });

// Controlador para administradores
export const adminReviewsController = new Elysia({ prefix: '/admin/reviews' })
  .use(authMiddleware)
  .derive(({ user }) => {
    checkAdmin(user);
  })
  .get('/', async ({ user, query }) => {
    // Verificar que sea admin

    const queryParams = {
      vehicleId: query.vehicleId as string,
      userId: query.userId as string,
      page: query.page ? parseInt(query.page as string) : 1,
      limit: query.limit ? parseInt(query.limit as string) : 20,
      sortBy: query.sortBy as any || 'newest'
    };

    return await ReviewsService.getReviews(queryParams);
  })
  .get('/:reviewId', async ({ params, user }) => {

    return await ReviewsService.getReviewById(params.reviewId);
  })
  .delete('/:reviewId', async ({ params, user }) => {
    // Verificar que sea admin

    try {
      // Obtener la reseña para actualizar estadísticas después
      const reviewResult = await ReviewsService.getReviewById(params.reviewId);

      if (!reviewResult.success || !reviewResult.data) {
        return reviewResult;
      }

      const review = reviewResult.data;

      // Eliminar la reseña
      await prisma.vehicleReview.delete({
        where: { id: params.reviewId }
      });

      // Actualizar estadísticas del vehículo
      await ReviewsService.updateVehicleRatingStats(review.vehicleId);

      return {
        success: true,
        message: 'Reseña eliminada exitosamente'
      };

    } catch (error) {
      console.error('Error deleting review:', error);
      return {
        success: false,
        error: 'Error interno del servidor',
        status: 500
      };
    }
  });
