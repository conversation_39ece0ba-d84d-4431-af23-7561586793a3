import { cn } from '@/lib/utils';
import React from 'react';

interface SkeletonRowProps {
  totalRows?: number;
  className?: string;
}

export function SkeletonRow({ totalRows = 1, className }: SkeletonRowProps) {
  return (
    <>
      <tr className="border-b py-2"  >
        {

          Array.from({ length: totalRows }).map((_, i) => {
            return (
              <td className="py-3" key={i}>
                {/* <div className="h-4 w-[100px] bg-gray-200 animate-pulse rounded-none" /> */}
                <div className={cn("h-4 w-[100px] bg-gray-200 animate-pulse rounded-none", className)} />
              </td>
            )
          })

        }
      </tr>
    </>
  )
}

