import { opentelemetry } from "@elysiajs/opentelemetry";
import { RedactingSpanExporter } from './redactingSpanExporter';
import { BatchSpanProcessor } from '@opentelemetry/sdk-trace-node';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-proto';
import { diag, DiagConsoleLogger, DiagLogLevel } from '@opentelemetry/api';
import { env } from 'bun';

// Debug outgoing telemetry logs in development
if (env.DEBUG == "true") {
    diag.setLogger(new DiagConsoleLogger(), DiagLogLevel.NONE);
}

export default function telemetry() {
    const otlpExporter = new OTLPTraceExporter({
        url: 'https://api.axiom.co/v1/traces',
        headers: {
            Authorization: `Bearer ${process.env.AXIOM_TOKEN}`,
            'X-Axiom-Dataset': process.env.AXIOM_DATASET,
        },
    });

    const exporter = new RedactingSpanExporter(otlpExporter);
    return opentelemetry({
        //@ts-ignore - BatchSpanProcessor types are incorrect (this shouldn't cause problems)
        spanProcessors: [new BatchSpanProcessor(exporter)],
    })
}