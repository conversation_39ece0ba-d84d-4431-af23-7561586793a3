import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import env from "../constants/env";
import { BEARER_COOKIE_NAME } from '@/constants';
import { getCookie } from 'cookies-next/client';
// import { sendLogToLogflare } from '@/lib/log-requests';

function checkIfIsClient() {
    return typeof window !== 'undefined';
}

type ApiResponse<T> =
    {
        success: true;
        data: T; status:
        number;
        statusText: string;
        headers: AxiosResponse['headers'];
        config: InternalAxiosRequestConfig;
        request: AxiosRequestConfig;
        error: undefined
    }
    |
    {
        success: false;
        data: undefined;
        status: number;
        statusText: string;
        headers: AxiosResponse['headers'];
        config: AxiosRequestConfig;
        request: AxiosRequestConfig;
        error: any
    };

type ErrorHandler = (error: any) => void;

const baseURL = env.NEXT_PUBLIC_API_URL;

class ApiService {
    private axiosInstance: AxiosInstance;
    baseURL: string = '';

    /** 
     * @param version - The version of the API to use. Defaults to 'v1'.
     */
    constructor(
        {
            version,
            prefix
        }:
            {
                version?: 'v1' | 'v2',
                prefix?: 'api' | 'dash-utils'
            } =
            {
                version: 'v1',
                prefix: 'api'
            }) {


        // Version is only available for api prefix so if prefix is dash-utils, version is not used
        this.baseURL = `${baseURL}/${prefix}${prefix === 'api' ? `/${version}` : ''}`;

        this.axiosInstance = axios.create({
            baseURL: this.baseURL,
            withCredentials: true
        });
    }

    private async setHeaders() {
        const isClient = checkIfIsClient();
        if (isClient) {
            return {};

        };
        const headers = (await import('next/headers')).headers;
        const rawHeaders = await headers();
        const headersObj: Record<string, string> = {};
        rawHeaders.forEach((value, key) => {
            headersObj[key] = value;
        });
        return headersObj;
    }

    private async request<T>(
        method: 'get' | 'post' | 'patch' | 'put' | 'delete',
        path: string,
        config?: AxiosRequestConfig,
        onError?: ErrorHandler
    ): Promise<ApiResponse<T>> {
        try {
            const isClient = checkIfIsClient();
            const headers = await this.setHeaders();
            let token = ''

            if (isClient) {
                token = getCookie(BEARER_COOKIE_NAME) as string;
            } else {
                const getCookieServerByName = (await import('@/actions/cookies')).getCookieServerByName;
                token = await getCookieServerByName({ name: BEARER_COOKIE_NAME }) as string;
                if (path.includes('/files/download')) {
                    console.log('Token on request for /api/v1/files/download: ', token);
                }
            }

            const requestHeaders = {
                    cookie: isClient ? undefined : headers.cookie,
                    'x-dashboard-call': 'true',
                    Authorization: `Bearer ${token}`,
                    ...config?.headers
                }

            if (path.includes('/files/download')) {
                console.log('============================================================')
                console.log('============================================================')

                console.log('Headers: ', headers);
                console.log('Request on /api/v1/files/download: ', requestHeaders);
                console.log('============================================================')
                console.log('============================================================')

            }

            const response = await this.axiosInstance({
                method,
                url: path,
                ...config,
                headers: requestHeaders,
            });
            // Simplificamos el objeto de respuesta para evitar problemas de serialización
            return {
                success: true,
                data: response.data,
                status: response.status,
                statusText: response.statusText,
                headers: response.headers,
                config: response.config,
                request: response.request,
                error: undefined
            }
        } catch (error: any) {
            console.error('Error in request:', error?.config?.url);

            if (onError) onError(error);
            await this.handleError(error);
            return {
                success: false,
                data: undefined,
                status: error.response?.status || 500,
                statusText: error.response?.statusText || 'Unknown Error',
                headers: error.response?.headers || {},
                config: error.config || {},
                request: error.request || {},
                error: error?.response?.data || error.message,
            };
        }
    }

    public async get<T>(path: string, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {
        return this.request('get', path, config, onError);
    }

    public async post<T>(path: string, data?: any, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {
        return this.request('post', path, { ...config, data }, onError);
    }

    public async patch<T>(path: string, data?: any, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {
        return this.request('patch', path, { ...config, data }, onError);
    }

    public async put<T>(path: string, data?: any, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {
        return this.request('put', path, { ...config, data }, onError);
    }

    public async delete<T>(path: string, config?: AxiosRequestConfig, onError?: ErrorHandler): Promise<ApiResponse<T>> {
        return this.request('delete', path, config, onError);
    }

    private async handleError(error: any) {
        console.error('API Error:', {
            message: error.message,
            status: error.response?.status,
            data: error.response?.data
        });
    }
}

export const apiService = new ApiService();

export const dashUtilsService = new ApiService({ prefix: 'dash-utils' });
