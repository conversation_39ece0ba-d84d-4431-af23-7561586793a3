import { Elysia, t } from "elysia";
import { ReservationsService } from "./reservations.service";
import { authMiddleware } from "@/app/middlewares/auth.middleware";
import { checkAdmin } from '@/lib/check-admin';

// Esquema para validación de reservas
const reservationSchema = t.Object({
  vehicleId: t.String(),
  startDate: t.String(), // ISO date string
  endDate: t.String(),   // ISO date string
  totalPrice: t.Number(),
  contactName: t.Optional(t.String()),
  contactEmail: t.Optional(t.String()),
  contactPhone: t.Optional(t.String())
});

// Esquema para actualización de estado
const statusSchema = t.Object({
  status: t.String()
});

// Esquema para bloqueo de fechas
const blockDatesSchema = t.Object({
  vehicleId: t.String(),
  startDate: t.String(), // ISO date string
  endDate: t.String(),   // ISO date string
  reason: t.Optional(t.String())
});

// Controlador público de reservas
export const publicReservationsController = new Elysia({ prefix: '/reservations' })
  .get('/unavailable-dates/:vehicleId', async ({ params }) => {
    return await ReservationsService.getUnavailableDates(params.vehicleId);
  });

// Controlador para usuarios
export const userReservationsController = new Elysia({ prefix: '/user/reservations' })
  .use(authMiddleware)
  .get('/', async ({ user }) => {
    return await ReservationsService.getUserReservations(user.id);
  })
  .get('/:id', async ({ params, user }) => {
    return await ReservationsService.getReservationById(params.id, user.id);
  })
  .post('/',
    async ({ body, user }) => {
      // Validar que el usuario sea de tipo cliente
      if (user.userType !== 'client') {
        return {
          success: false,
          error: 'Solo los usuarios de tipo cliente pueden crear reservas',
          status: 403
        };
      }

      return await ReservationsService.create({
        ...body,
        userId: user.id
      });
    },
    {
      body: reservationSchema
    }
  )
  .patch('/:id/cancel', async ({ params, user }) => {
    return await ReservationsService.cancelReservation(params.id, user.id);
  });

// Controlador para hosts
export const hostReservationsController = new Elysia({ prefix: '/host/reservations' })
  .use(authMiddleware)
  .get('/', async ({ user }) => {
    if (user.userType !== 'host' && user.role !== 'admin') {
      return { error: 'Unauthorized', status: 403 };
    }
    return await ReservationsService.getHostReservations(user.id);
  })
  .get('/personal', async ({ user }) => {
    if (user.userType !== 'host' && user.role !== 'admin') {
      return { error: 'Unauthorized', status: 403 };
    }
    return await ReservationsService.getHostPersonalReservations(user.id);
  })
  .get('/vehicle/:vehicleId', async ({ params, user }) => {
    if (user.userType !== 'host' && user.role !== 'admin') {
      return { error: 'Unauthorized', status: 403 };
    }
    return await ReservationsService.getVehicleReservations(params.vehicleId, user.id);
  })
  .patch('/:id/status',
    async ({ params, body, user }) => {
      if (user.userType !== 'host' && user.role !== 'admin') {
        return { error: 'Unauthorized', status: 403 };
      }
      return await ReservationsService.updateReservationStatus(params.id, body.status, user.id);
    },
    {
      body: statusSchema
    }
  )
  .post('/block-dates',
    async ({ body, user }) => {
      if (user.userType !== 'host') {
        return { error: 'Unauthorized', status: 403 };
      }
      console.log("Bloqueando fechas:", body);

      return await ReservationsService.blockDates({
        ...body,
        userId: user.id,
        by: 'host'
      });
    },
    {
      body: blockDatesSchema
    }
  )
  .post('/block-dates',
    async ({ body, user }) => {
      return await ReservationsService.blockDates({
        ...body,
        userId: user.id,
        by: 'host'
      });
    },
    {
      body: blockDatesSchema
    }
)
  .get('/stats', async ({ user }) => {
    if (user.userType !== 'host' && user.role !== 'admin') {
      return { error: 'Unauthorized', status: 403 };
    }

    const {
      totalReservations,
      activeReservations,
      pendingReservations,
      completedReservations,
      cancelledReservations,
      // averageRating,
      totalEarnings
    } = await ReservationsService.getStatsByHostId(user.id);

    return {
      totalReservations,
      activeReservations,
      pendingReservations,
      completedReservations,
      cancelledReservations,
      // averageRating,
      totalEarnings
    };
  })
  .get('/host', async ({ user, query }) => {
    if (user.userType !== 'host' && user.role !== 'admin') {
      return { error: 'Unauthorized', status: 403 };
    }

    const page = Number(query?.page) || 1;
    const limit = Number(query?.limit) || 10;

    const { data, pagination } = await ReservationsService.getByHostId(user.id, { page, limit });

    return {
      data,
      pagination
    };
  });

// Controlador para administradores
export const adminReservationsController = new Elysia({ prefix: '/admin/reservations' })
  .use(authMiddleware)
  .derive(({ user }) => {
    checkAdmin(user);
  })
  .get('/', async ({ user }) => {

    return await ReservationsService.getAllReservationsForAdmin();
  })
  .get('/:id', async ({ params, user }) => {
    return await ReservationsService.getReservationByIdAdmin(params.id);
  })
  .patch('/:id/status',
    async ({ params, body, user }) => {
      return await ReservationsService.updateReservationStatusAdmin(params.id, body.status);
    },
    {
      body: statusSchema
    }
  )
  .post('/block-dates',
    async ({ body, user }) => {
      return await ReservationsService.blockDates({
        ...body,
        userId: user.id,
        by: 'admin'
      });
    },
    {
      body: blockDatesSchema
    }
  );


