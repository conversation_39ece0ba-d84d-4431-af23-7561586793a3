

export const timezonesOnStateCode: Record<'mx' | 'us', Record<string, string>> = {
  mx: {
    ags: 'America/Mexico_City',     // Aguascalientes
    bcn: 'America/Tijuana',         // Baja California
    bcs: 'America/Mazatlan',        // Baja California Sur
    cam: 'America/Merida',          // Campeche
    chh: 'America/Chihuahua',       // Chihuahua
    chp: 'America/Mexico_City',     // Chiapas
    cmx: 'America/Mexico_City',     // Ciudad de México
    coa: 'America/Monterrey',       // Coahuila
    col: 'America/Mexico_City',     // Colima
    dur: 'America/Monterrey',       // Durango
    gro: 'America/Mexico_City',     // Guerrero
    gto: 'America/Mexico_City',     // Guanajuato
    hgo: 'America/Mexico_City',     // Hidalgo
    jal: 'America/Mexico_City',     // Jalisco
    mex: 'America/Mexico_City',     // Estado de México
    mic: 'America/Mexico_City',     // Michoacán
    mor: 'America/Mexico_City',     // Morelos
    nay: 'America/Mazatlan',        // Nayarit
    nle: 'America/Monterrey',       // Nuevo León
    oax: 'America/Mexico_City',     // Oaxaca
    pue: 'America/Mexico_City',     // Puebla
    qro: 'America/Mexico_City',     // Querétaro
    roo: 'America/Cancun',          // Quintana Roo
    slp: 'America/Mexico_City',     // San Luis Potosí
    sin: 'America/Mazatlan',        // Sinaloa
    son: 'America/Hermosillo',      // Sonora
    tab: 'America/Mexico_City',     // Tabasco
    tam: 'America/Mexico_City',     // Tamaulipas
    tla: 'America/Mexico_City',     // Tlaxcala
    ver: 'America/Mexico_City',     // Veracruz
    yuc: 'America/Merida',          // Yucatán
    zac: 'America/Mexico_City',     // Zacatecas
  },
  us: {
    CA: 'America/Los_Angeles',      // California
    NY: 'America/New_York',         // New York
    TX: 'America/Chicago',          // Texas
    FL: 'America/New_York',         // Florida
    IL: 'America/Chicago',          // Illinois
    AZ: 'America/Phoenix',          // Arizona (no DST)
    WA: 'America/Los_Angeles',      // Washington
    CO: 'America/Denver',           // Colorado
    GA: 'America/New_York',         // Georgia
    NV: 'America/Los_Angeles',      // Nevada
  }
}


export const getTimezone = (countryCode: 'mx' | 'us', stateCode: string) => {
  return timezonesOnStateCode[countryCode][stateCode];
}
