import { But<PERSON> } from "@/components/ui/button"
import { HostTable } from "@/components/hosts/host-table"
import { HostFilters } from "@/components/host-filters"

export default function AdminHostsPage() {
  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-2">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Gestión de Anfitriones</h1>
            <p className="text-muted-foreground">Ver, verificar y gestionar cuentas de anfitriones en la plataforma</p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline">Exportar</Button>
            {/* <Button>+ Añadir Anfitrión</Button> */}
          </div>
        </div>
      </div>

      <HostFilters />

      <div className="rounded-md border">
        <HostTable />
      </div>
    </div>
  )
}
