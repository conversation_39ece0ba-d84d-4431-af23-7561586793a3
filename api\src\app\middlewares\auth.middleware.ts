
import { auth } from '@/lib/auth';
// import { Session, User } from "better-auth/types";
import { Session, User as PrismaUser } from '@prisma/client';
import Elysia, { Context } from "elysia";
import { HttpException } from '@/exceptions/HttpExceptions';
import { setRequestContext } from '../context/request-context';

type User = Omit<PrismaUser, 'banExpires'> & { banExpires: Date | null };

export const authCache = new Map<string, { session: Session; user: User; /* message: string | undefined; sucess: string | undefined */ }>();

export const getSession = async (c: Context) => {
  const session = await auth.api.getSession({ headers: c.request.headers }) as { user: User; session: Session } | null;

  // If the path has /api/v1/files/download, then it's a file download request, log the session
  if (c.path.includes('/files/download')) {
    // log cookies:
    console.log('============================================================')
    console.log('Cookies: ', c.request.headers.get('cookie'));
    console.log('Session on /api/v1/files/download: ', session);
    console.log('Headers: ', c.request.headers);
    console.log('Query: ', c.request.url);
    console.log('============================================================')
  }

  if (!session) {
    throw HttpException.Unauthorized();
  }

  return {
    user: session.user,
    session: session.session
  }
}

interface AuthHeader {
  type: 'Bearer' | 'Key';
  value: string;
}

const authorizationTypeMap: Record<string, 'Bearer' | 'Key'> = {
  'Bearer': 'Bearer',
  'Key': 'Key'
}


/** 
 * Auth middleware and extend context with user and session, 
 * this makes the authentication middleware and make organizationId available in all controllers
 * @param app Elysia instance
 */

export const authMiddleware = (app: Elysia) =>
  app.derive(async (ctx) => {
    const path = ctx.path;

    // console.log('path: ', path);
    const { request, headers, ...rest } = ctx;
    const requestId = (rest.store as { requestId: string }).requestId;
    if (requestId && authCache.has(requestId)) {
      return authCache.get(requestId)!;
    }

    const session = await getSession(ctx);


    if (requestId) {
      authCache.set(requestId, session);
    }

    return session;
  })
    .onBeforeHandle(async (ctx) => {
      // const clonedBody = await ctx.request.clone().json() as { }

      setRequestContext({
        requestId: (ctx.store as { requestId: string }).requestId,
        user: {
          id: ctx.user.id,
          role: ctx.user.role,
          email: ctx.user.email,
        },
        session: {
          userId: ctx.session.userId
        },
        preferenceTimezone: "America/Mexico_City" // here I can use the user preference timezone or organization timezone
      });

    })