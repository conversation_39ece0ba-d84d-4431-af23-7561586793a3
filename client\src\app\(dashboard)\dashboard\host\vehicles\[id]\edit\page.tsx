'use client'

import { useParams } from 'next/navigation'
import VehicleEditForm from '@/components/host/vehicle-edit-form'
import { Separator } from '@/components/ui/separator'

export default function EditVehiclePage() {
  const params = useParams()
  const vehicleId = params.id as string

  return (
    <div className="container py-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Editar Vehículo</h1>
        <p className="text-muted-foreground">
          Actualiza la información de tu vehículo
        </p>
      </div>
      <Separator />
      <VehicleEditForm vehicleId={vehicleId} />
    </div>
  )
}