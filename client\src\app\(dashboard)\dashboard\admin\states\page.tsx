"use client"

import { useState, useEffect } from "react"
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query"
import { statesApi, StateResponse } from "@/lib/api/states.api"
import { timezonesOnStateCode } from "@/constants/zones"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Edit, Filter, MoreHorizontal, Plus, Search, Trash2, Power } from "lucide-react"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"
import toast from "react-hot-toast"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, /* DialogTrigger */ } from "@/components/ui/dialog"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

const stateFormSchema = z.object({
  name: z.string().min(1, { message: "El nombre es requerido" }),
  code: z.string().min(1, { message: "El código es requerido" }),
  countryCode: z.string().min(1, { message: "El código de país es requerido" }).default("mx"),
  timezone: z.string().min(1, { message: "La zona horaria es requerida" }).default("America/Mexico_City"),
  active: z.boolean().default(true)
})

type StateFormValues = z.infer<typeof stateFormSchema>

export default function StatesPage() {
  const queryClient = useQueryClient()
  const [searchTerm, setSearchTerm] = useState("")
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [selectedState, setSelectedState] = useState<StateResponse | null>(null)
  const [selectedCountry, setSelectedCountry] = useState<'mx' | 'us'>('mx')
  const [availableStates, setAvailableStates] = useState<{ code: string, label: string }[]>([])

  // Actualizar estados disponibles cuando cambia el país
  useEffect(() => {
    const states = Object.entries(timezonesOnStateCode[selectedCountry]).map(([code, data]) => ({
      code,
      label: data.label
    }));
    setAvailableStates(states);
  }, [selectedCountry]);

  // Consultar todos los estados
  const { data: states, isLoading, isError } = useQuery({
    queryKey: ['admin-states'],
    queryFn: statesApi.admin.getAll
  })

  // Formulario para crear/editar estados
  const form = useForm<StateFormValues>({
    resolver: zodResolver(stateFormSchema),
    defaultValues: {
      name: "",
      code: "",
      countryCode: "mx",
      timezone: "America/Mexico_City",
      active: true
    }
  })

  // Función para manejar cambio de estado
  const handleStateChange = (stateCode: string) => {
    const countryCode = form.getValues().countryCode as 'mx' | 'us';
    const stateData = timezonesOnStateCode[countryCode][stateCode];

    if (stateData) {
      form.setValue('timezone', stateData.timezone);
      form.setValue('name', stateData.label);
    }
  }

  // Función para manejar cambio de país
  const handleCountryChange = (country: 'mx' | 'us') => {
    setSelectedCountry(country);
    form.setValue('code', "");
    form.setValue('name', "");
    form.setValue('timezone', "");
  }

  // Mutación para crear un estado
  const createStateMutation = useMutation({
    mutationFn: statesApi.admin.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-states'] })
      setIsCreateDialogOpen(false)
      form.reset()

      toast.success("Estado creado exitosamente.")
    },
    onError: (error) => {

      toast.error(`No se pudo crear el estado: ${error.message}`)
    }
  })

  // Mutación para actualizar un estado
  const updateStateMutation = useMutation({
    mutationFn: (data: StateFormValues & { id: string }) =>
      statesApi.admin.update(data.id, {
        name: data.name,
        code: data.code,
        countryCode: data.countryCode,
        timezone: data.timezone,
        active: data.active
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-states'] })
      setIsEditDialogOpen(false)
      setSelectedState(null)
      toast.success("Estado actualizado exitosamente.")
    },
    onError: (error) => {

      toast.error(`No se pudo actualizar el estado: ${error.message}`)
    }
  })

  // Mutación para activar/desactivar un estado
  const toggleStateMutation = useMutation({
    mutationFn: statesApi.admin.toggleActive,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-states'] })
      toast.success("Estado actualizado exitosamente.")
    },
    onError: (error) => {
      toast.error(`No se pudo actualizar el estado: ${error.message}`)
    }
  })

  // Mutación para eliminar un estado
  const deleteStateMutation = useMutation({
    mutationFn: statesApi.admin.delete,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-states'] })
      setIsDeleteDialogOpen(false)
      setSelectedState(null)
      toast.success("Estado eliminado exitosamente.")
    },
    onError: (error) => {
      toast.error(`No se pudo eliminar el estado: ${error.message}`)
    }
  })

  // Filtrar estados por término de búsqueda
  const filteredStates = states?.filter(state =>
    state.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    state.code.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Manejar la creación de un estado
  const handleCreateState = (data: StateFormValues) => {
    createStateMutation.mutate(data)
  }

  // Manejar la edición de un estado
  const handleEditState = (data: StateFormValues) => {
    if (selectedState) {
      updateStateMutation.mutate({
        ...data,
        id: selectedState.id
      })
    }
  }

  // Manejar la eliminación de un estado
  const handleDeleteState = () => {
    if (selectedState) {
      deleteStateMutation.mutate(selectedState.id)
    }
  }

  // Abrir el diálogo de edición y cargar los datos del estado
  const openEditDialog = (state: StateResponse) => {
    setSelectedState(state)
    form.reset({
      name: state.name,
      code: state.code,
      countryCode: state.countryCode,
      timezone: state.timezone || "America/Mexico_City",
      active: state.active
    })
    setIsEditDialogOpen(true)
  }

  // Abrir el diálogo de eliminación
  const openDeleteDialog = (state: StateResponse) => {
    setSelectedState(state)
    setIsDeleteDialogOpen(true)
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Administración de Estados</h1>
        <Button onClick={() => {
          form.reset({
            name: "",
            code: "",
            countryCode: "mx",
            timezone: "America/Mexico_City",
            active: true
          })
          setIsCreateDialogOpen(true)
        }}>
          <Plus className="mr-2 h-4 w-4" />
          Nuevo Estado
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Estados</CardTitle>
          <CardDescription>
            Administra los estados disponibles en la plataforma.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                placeholder="Buscar estados..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Button variant="outline" className="ml-2">
              <Filter className="mr-2 h-4 w-4" />
              Filtrar
            </Button>
          </div>

          {isError && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>
                No se pudieron cargar los estados. Intenta de nuevo más tarde.
              </AlertDescription>
            </Alert>
          )}

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nombre</TableHead>
                  <TableHead>Código</TableHead>
                  <TableHead>País</TableHead>
                  <TableHead>Zona Horaria</TableHead>
                  <TableHead>Estado</TableHead>
                  <TableHead className="text-right">Acciones</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  Array.from({ length: 5 }).map((_, index) => (
                    <TableRow key={index}>
                      <TableCell><Skeleton className="h-6 w-32" /></TableCell>
                      <TableCell><Skeleton className="h-6 w-16" /></TableCell>
                      <TableCell><Skeleton className="h-6 w-16" /></TableCell>
                      <TableCell><Skeleton className="h-6 w-32" /></TableCell>
                      <TableCell><Skeleton className="h-6 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-6 w-20 ml-auto" /></TableCell>
                    </TableRow>
                  ))
                ) : filteredStates?.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4">
                      No se encontraron estados
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredStates?.map((state) => (
                    <TableRow key={state.id}>
                      <TableCell>{state.name}</TableCell>
                      <TableCell>{state.code}</TableCell>
                      <TableCell>{state.countryCode.toUpperCase()}</TableCell>
                      <TableCell>{state.timezone || "America/Mexico_City"}</TableCell>
                      <TableCell>
                        <Badge variant={state.active ? "success" : "secondary"}>
                          {state.active ? "Activo" : "Inactivo"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Abrir menú</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent className='[&>*]:cursor-pointer' align="end">
                            <DropdownMenuItem onClick={() => openEditDialog(state)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Editar
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => toggleStateMutation.mutate(state.id)}>
                              <Power className="mr-2 h-4 w-4" />
                              {state.active ? "Desactivar" : "Activar"}
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => openDeleteDialog(state)}>
                              <Trash2 className="mr-2 h-4 w-4" />
                              Eliminar
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Diálogo para crear un estado */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Crear Nuevo Estado</DialogTitle>
            <DialogDescription>
              Completa el formulario para crear un nuevo estado.
            </DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleCreateState)} className="space-y-4">
              <FormField
                control={form.control}
                name="countryCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>País</FormLabel>
                    <Select
                      onValueChange={(value) => {
                        field.onChange(value);
                        handleCountryChange(value as 'mx' | 'us');
                      }}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecciona un país" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="mx">México</SelectItem>
                        <SelectItem value="us">Estados Unidos</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Estado</FormLabel>
                    <Select
                      onValueChange={(value) => {
                        field.onChange(value);
                        handleStateChange(value);
                      }}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecciona un estado" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {availableStates.map((state) => (
                          <SelectItem key={state.code} value={state.code}>
                            {state.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {/* <FormDescription>
                      Código único para identificar el estado
                    </FormDescription> */}
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nombre</FormLabel>
                    <FormControl>
                      <Input {...field} readOnly />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="timezone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Zona Horaria</FormLabel>
                    <FormControl>
                      <Input {...field} readOnly />
                    </FormControl>
                    {/* <FormDescription>
                      Zona horaria del estado
                    </FormDescription> */}
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="active"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <input
                        type="checkbox"
                        checked={field.value}
                        onChange={field.onChange}
                        className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Activo</FormLabel>
                      <FormDescription>
                        Los estados activos aparecerán en las listas de selección.
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancelar
                </Button>
                <Button type="submit" disabled={createStateMutation.isPending}>
                  {createStateMutation.isPending ? "Creando..." : "Crear Estado"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Diálogo para editar un estado */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Editar Estado</DialogTitle>
            <DialogDescription>
              Actualiza la información del estado.
            </DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleEditState)} className="space-y-4">
              <FormField
                control={form.control}
                name="countryCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>País</FormLabel>
                    <Select
                      onValueChange={(value) => {
                        field.onChange(value);
                        handleCountryChange(value as 'mx' | 'us');
                      }}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecciona un país" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="mx">México</SelectItem>
                        <SelectItem value="us">Estados Unidos</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Estado</FormLabel>
                    <Select
                      onValueChange={(value) => {
                        field.onChange(value);
                        handleStateChange(value);
                      }}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecciona un estado" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {availableStates.map((state) => (
                          <SelectItem key={state.code} value={state.code}>
                            {state.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nombre</FormLabel>
                    <FormControl>
                      <Input {...field} readOnly />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="timezone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Zona Horaria</FormLabel>
                    <FormControl>
                      <Input {...field} readOnly />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="active"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <input
                        type="checkbox"
                        checked={field.value}
                        onChange={field.onChange}
                        className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Activo</FormLabel>
                      <FormDescription>
                        Los estados inactivos no aparecerán en las listas de selección.
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                  Cancelar
                </Button>
                <Button type="submit" disabled={updateStateMutation.isPending}>
                  {updateStateMutation.isPending ? "Actualizando..." : "Actualizar Estado"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Diálogo para confirmar eliminación */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmar Eliminación</DialogTitle>
            <DialogDescription>
              ¿Estás seguro de que deseas eliminar el estado &quot;{selectedState?.name}&quot;? Esta acción no se puede deshacer.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancelar
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={handleDeleteState}
              disabled={deleteStateMutation.isPending}
            >
              {deleteStateMutation.isPending ? "Eliminando..." : "Eliminar Estado"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
