import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"

const activities = [
  {
    id: 1,
    type: "new_host",
    title: "New host registered",
    description: "<PERSON> added 3 luxury vehicles",
    time: "10 minutes ago",
    icon: "JW",
    iconColor: "bg-blue-500",
  },
  {
    id: 2,
    type: "new_reservation",
    title: "New reservation",
    description: "Tesla Model 3 booked for 5 days",
    time: "25 minutes ago",
    icon: "TR",
    iconColor: "bg-yellow-500",
  },
  {
    id: 3,
    type: "payout",
    title: "Payout completed",
    description: "$1,200.00 to <PERSON>",
    time: "1 hour ago",
    icon: "PC",
    iconColor: "bg-green-500",
  },
  {
    id: 4,
    type: "support",
    title: "Support ticket opened",
    description: "Vehicle damage reported by client",
    time: "3 hours ago",
    icon: "ST",
    iconColor: "bg-red-500",
  },
]

export function RecentActivity() {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-md font-medium">Recent Activity</CardTitle>
        <Button variant="ghost" size="sm" asChild>
          <Link href="/dashboard/activity">View All</Link>
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => (
            <div key={activity.id} className="flex items-start gap-4">
              <Avatar className={`${activity.iconColor} text-white`}>
                <AvatarFallback>{activity.icon}</AvatarFallback>
              </Avatar>
              <div className="flex-1 space-y-1">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium">{activity.title}</p>
                  <Badge variant="outline" className="text-xs">
                    {activity.time}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">{activity.description}</p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
