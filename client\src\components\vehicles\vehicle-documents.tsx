import { Card, CardContent, <PERSON>Header, CardTitle, CardDescription } from "@/components/ui/card";
import { vehiclesApi } from "@/lib/api/vehicles.api";
import DocumentPreview from "@/components/forms/document-preview";
import { useQuery } from "@tanstack/react-query";

export interface VehicleDocuments {
  plateDocument?: string | null;
  vinDocument?: string | null;
  registrationDocument?: string | null;
  insurancePolicyDocument?: string | null;
}

interface VehicleDocumentsProps {
  vehicleId: string;
}

export default function VehicleDocuments({ vehicleId }: VehicleDocumentsProps) {
  const { data: documents, isLoading, error } = useQuery({
    queryKey: ['vehicle-documents', vehicleId],
    queryFn: () => vehiclesApi.host.getDocuments(vehicleId),
    staleTime: 60 * 60 * 1000, // Cache for 1 hour
    enabled: !!vehicleId
  });


  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Documentos del Vehículo</CardTitle>
          <CardDescription>Cargando documentos...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="h-40 bg-gray-100 animate-pulse rounded-md"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Documentos del Vehículo</CardTitle>
          <CardDescription>Error al cargar los documentos</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-red-500">
            No se pudieron cargar los documentos del vehículo. Por favor, intente nuevamente.
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Documentos del Vehículo</CardTitle>
        <CardDescription>Documentación legal del vehículo</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {documents?.plateDocument && (
            <DocumentPreview
              documentKey={documents.plateDocument}
              label="Documento de placas"
            />
          )}
          {documents?.vinDocument && (
            <DocumentPreview
              documentKey={documents.vinDocument}
              label="Documento VIN"
            />
          )}
          {documents?.registrationDocument && (
            <DocumentPreview
              documentKey={documents.registrationDocument}
              label="Tarjeta de circulación"
            />
          )}
          {documents?.insurancePolicyDocument && (
            <DocumentPreview
              documentKey={documents.insurancePolicyDocument}
              label="Póliza de seguro"
            />
          )}
        </div>
      </CardContent>
    </Card>
  );
}
