const chokidar = require('chokidar');
const { exec, spawn } = require('child_process');
const path = require('path');
const net = require('net');
const { parseArgs } = require('util');
const fs = require('fs');

const { values, positionals } = parseArgs({
  args: Bun.argv,
  options: {
    run: {
      type: 'boolean',
    }
  },
  strict: true,
  allowPositionals: true,
});

const PORT = process.env.PORT || 3000;
const pidDir = path.join(__dirname, 'tmp', 'pids');
const workerPidFile = path.join(pidDir, 'workers.pid');

const prismaDir = path.join(__dirname, 'prisma');
let isProcessing = false;
let serverProcess = null;


console.log('🔍 Vigilando cambios en la carpeta prisma...');

// Función que chequea si hay un servidor escuchando en el puerto indicado
function isServerRunning(port) {
    return new Promise((resolve) => {
      const socket = new net.Socket();
      socket.setTimeout(1000);
      socket.on('connect', () => {
        socket.destroy();
        resolve(true);
      });
      socket.on('error', () => resolve(false));
      socket.on('timeout', () => resolve(false));
      socket.connect(port, '127.0.0.1');
    });
  }

// Función para verificar si hay un proceso de workers ejecutándose
function checkForWorkersProcess() {
  try {
    if (fs.existsSync(workerPidFile)) {
      console.log('Archivo PID de worker encontrado', workerPidFile);
      const data = fs.readFileSync(workerPidFile, 'utf8');
      const pidInfo = JSON.parse(data);
      console.log(`Proceso worker encontrado con PID ${pidInfo.pid}`);
      return pidInfo;
    }
  } catch (error) {
    console.error('Error al leer el archivo PID de worker:', error);
  }
  return null;
}

// Función para detener el proceso de workers
async function killWorkersProcess() {
  const workerProcess = checkForWorkersProcess();
  
  if (workerProcess) {
    console.log(`Deteniendo el proceso worker con PID ${workerProcess.pid}...`);
    
    try {
      // En Windows
      if (process.platform === 'win32') {
        exec(`taskkill /F /PID ${workerProcess.pid}`);
      } 
      // En Linux/Mac
      else {
        exec(`kill -15 ${workerProcess.pid}`);
      }
      console.log('Proceso worker detenido.');
      
      // Esperar a que el archivo PID sea eliminado
      await new Promise((resolve) => {
        const checkInterval = setInterval(() => {
          if (!fs.existsSync(workerPidFile)) {
            clearInterval(checkInterval);
            resolve();
          }
        }, 100);
        
        // Timeout después de 5 segundos
        setTimeout(() => {
          clearInterval(checkInterval);
          if (fs.existsSync(workerPidFile)) {
            fs.unlinkSync(workerPidFile);
            console.log('Archivo PID de worker eliminado manualmente después del timeout');
          }
          resolve();
        }, 5000);
      });
    } catch (error) {
      console.error('Error al intentar detener el worker:', error);
      // Intentar eliminar el archivo PID en caso de error
      if (fs.existsSync(workerPidFile)) {
        fs.unlinkSync(workerPidFile);
      }
    }
  } else {
    console.log('No se encontró proceso worker en ejecución.');
  }
}

// Función para detener el proceso del servidor
function killServerProcess() {
    return new Promise((resolve) => {
      if (serverProcess) {
        console.log('Deteniendo el servidor actual...');
        serverProcess.kill();
        serverProcess.on('exit', () => {
          serverProcess = null;
          resolve();
        });
      } else {
        // Si no tenemos referencia, usar kill-port como respaldo
        exec(`npx kill-port ${PORT}`, (error) => {
          if (error) {
            console.error(`❌ Error al matar el proceso en el puerto ${PORT}:`, error);
          } else {
            console.log(`Servidor en puerto ${PORT} detenido.`);
          }
          resolve();
        });
      }
    });
  }
  
  // Función para iniciar el servidor y heredar sus logs a la terminal
  function startServer() {
    console.log('🔄 Iniciando el servidor...');
    // Usamos spawn para que la salida se muestre en la terminal
    serverProcess = spawn('bun', ['--watch', 'src/index.ts'], { stdio: 'inherit' });
    serverProcess.on('exit', (code) => {
      console.log(`Servidor detenido con código ${code}`);
      serverProcess = null;
    });
  }

async function ejecutarPrismaGenerate() {
    console.log('Is processing:', isProcessing);
    if (isProcessing) return;
    isProcessing = true;

      // Verificar si el servidor está en ejecución en el puerto 3000
  const running = await isServerRunning(3000);
  if (running) {
    console.log('Servidor en ejecución detectado, deteniendo servidor...');
    await killServerProcess();
  }

  // Verificar y detener los workers si están en ejecución
  await killWorkersProcess();

    console.log('📦 Generando cliente Prisma...');
    exec('bun prisma generate --schema ./prisma/schema ', (error, stdout, stderr) => {
        if (error) {
            console.error('❌ Error al generar el cliente Prisma:', error);
            isProcessing = false;
            return;
        }
        // console.log(stdout);
        console.log('[PRISMA GENERATE] stdout:', stdout);
        console.log('✅ Cliente Prisma generado correctamente');
        
        console.log('🚀 Actualizando la base de datos...');
        exec('bun prisma db push', (error, stdout, stderr) => {
            if (error) {
                console.error('❌ Error al hacer push a la base de datos:', error);
                isProcessing = false;
                return;
            }
            // console.log(stdout);
            console.log('[PRISMA DB PUSH] stdout:', stdout);
            console.log('✅ Base de datos actualizada correctamente');
            isProcessing = false;
        // restartServer();
        startServer();

        });
    });
}

// Usar chokidar en lugar de fs.watch
const watcher = chokidar.watch(prismaDir, {
    ignored: /(^|[\/\\])\../, // ignorar archivos ocultos
    persistent: true,
    ignoreInitial: true,
});

watcher
    .on('change', async (path) => {
        console.log(`🔄 Cambios detectados en: ${path}`);
        if (path.endsWith('.prisma')) {
            console.log(`🔄 Cambios detectados en: ${path}`);
            await ejecutarPrismaGenerate();
        }
    })
    .on('error', error => console.error('Error en el watcher:', error));


// Si se pasa el flag --run, ejecutar la generación inicial
if (values.run) {
    console.log('🚀 Ejecutando generación inicial por flag --run');
    ejecutarPrismaGenerate();
}
