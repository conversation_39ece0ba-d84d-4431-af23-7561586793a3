"use client"
import { useRouter, useSearchParams } from "next/navigation"
import Image from "next/image"
import { Badge } from "@/components/ui/badge"
import { DataTable } from "@/components/data-table/data-table"
import { createColumns, createSortableHeader } from "@/components/data-table/columns"
import toast from "react-hot-toast"
import { DropdownMenuItem } from "@/components/ui/dropdown-menu"
import { Calendar, DollarSign, Settings } from "lucide-react"
import { Vehicle } from "@/lib/api/vehicles.api"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { vehiclesApi } from "@/lib/api/vehicles.api"

interface HostVehicleTableProps {
  vehicles: Vehicle[]
  rowCount: number
  isLoading: boolean
}

export function HostVehicleTable({ vehicles, rowCount, isLoading }: HostVehicleTableProps) {
  const router = useRouter()
  const queryClient = useQueryClient();


  // Mutación para eliminar vehículo
  const deleteMutation = useMutation({
    mutationFn: vehiclesApi.host.delete,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['host-vehicles'] })
      toast.success("Vehículo eliminado exitosamente.")
    },
    onError: () => {

      toast.error("No se pudo eliminar el vehículo. Intenta de nuevo más tarde.")
    }
  })


  const handleView = (vehicle: Vehicle) => {
    router.push(`/dashboard/host/vehicles/${vehicle.id}`)
  }

  const handleEdit = (vehicle: Vehicle) => {
    router.push(`/dashboard/host/vehicles/${vehicle.id}/edit`)
  }

  const handleDelete = (vehicle: Vehicle) => {
    if (confirm(`¿Estás seguro de que deseas eliminar ${vehicle.make} ${vehicle.model}?`)) {
      deleteMutation.mutate(vehicle.id)
    }
  }

  const getExtraActions = (vehicle: Vehicle) => {
    return [
      <DropdownMenuItem
        key="calendar"
        onClick={() => router.push(`/dashboard/host/vehicles/${vehicle.id}/calendar`)}
      >
        <Calendar className="mr-2 h-4 w-4" />
        <span>Ver calendario</span>
      </DropdownMenuItem>,
      <DropdownMenuItem
        key="pricing"
        onClick={() => router.push(`/dashboard/host/vehicles/${vehicle.id}/pricing`)}
      >
        <DollarSign className="mr-2 h-4 w-4" />
        <span>Ajustar precios</span>
      </DropdownMenuItem>,
      <DropdownMenuItem
        key="settings"
        onClick={() => router.push(`/dashboard/host/vehicles/${vehicle.id}/settings`)}
      >
        <Settings className="mr-2 h-4 w-4" />
        <span>Configuración</span>
      </DropdownMenuItem>,
    ]
  }

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "active": return "success"
      case "inactive": return "secondary"
      // case "maintenance": return "warning"
      case "rejected": return "destructive"
      case "pending": return "outline"
      default: return "secondary"
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "active": return "Activo"
      case "inactive": return "Inactivo"
      // case "maintenance": return "Mantenimiento"
      case "rejected": return "Rechazado"
      case "pending": return "Pendiente"
      default: return status
    }
  }

  const columns = createColumns<Vehicle>(
    [
      {
        accessorKey: "vehicle",
        header: createSortableHeader("Vehículo"),
        cell: ({ row }) => {
          const vehicle = row.original
          const imageUrl = Array.isArray(vehicle.images) && vehicle.images.length > 0
            ? vehicle.images[0]
            : "/placeholder.svg?height=48&width=48"

          return (
            <div className="flex items-center gap-3">
              <div className="h-12 w-12 rounded overflow-hidden">
                <Image
                  src={imageUrl}
                  alt={`${vehicle.make} ${vehicle.model}`}
                  width={48}
                  height={48}
                  className="h-full w-full object-cover"
                />
              </div>
              <div>
                <div className="font-medium">{vehicle.make} {vehicle.model}</div>
                <div className="text-xs text-muted-foreground">
                  {vehicle.year} • {vehicle.color}
                </div>
                <div className="text-xs text-muted-foreground">{vehicle.plate}</div>
              </div>
            </div>
          )
        },
      },
      {
        accessorKey: "make",
        header: "Marca",
        cell: ({ row }) => <div className="font-medium">{row.original.make}</div>,
      },
      {
        accessorKey: "status",
        header: "Estado",
        cell: ({ row }) => {
          const status = row.original.status || "pending"
          return (
            <Badge
              variant={getStatusBadgeVariant(status)}
            >
              {getStatusLabel(status)}
            </Badge>
          )
        },
      },
      {
        accessorKey: "price",
        header: createSortableHeader("Tarifa Diaria"),
        cell: ({ row }) => <div className="font-medium">${row.original.price}</div>,
      },
      {
        accessorKey: "reviews",
        header: createSortableHeader("Rentas"),
        cell: ({ row }) => <div className="text-center">{row.original.reviews}</div>,
      },
      {
        accessorKey: "averageRating",
        header: createSortableHeader("Calificación"),
        cell: ({ row }) => (
          <div className="flex items-center">
            <span className="font-medium">{(row.original.averageRating || 0).toFixed(1)}</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="currentColor"
              className="w-4 h-4 ml-1 text-yellow-500"
            >
              <path
                fillRule="evenodd"
                d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z"
                clipRule="evenodd"
              />
            </svg>
          </div>
        ),
      },
    ],
    {
      onView: handleView,
      onEdit: handleEdit,
      onDelete: handleDelete,
      extraActions: getExtraActions,
    },
  )

  const searchParams = useSearchParams()

  const page = searchParams.get('page') || '1'
  const limit = searchParams.get('limit') || '10'

  const handlePageChange = (pageIndex: number) => {
    console.log('pageIndex', pageIndex)
    if (pageIndex !== Number(page)) {
      router.push(`/dashboard/host/vehicles?page=${pageIndex}&limit=${limit}`)
    }
  }

  return (
    <div>
      <DataTable
        columns={columns}
        data={vehicles}
        rowCount={rowCount}
        pageSize={Number(limit)}
        pageIndex={Number(page) - 1}
        onPageChange={handlePageChange}
        isLoading={isLoading}
      />
    </div>
  )
}


