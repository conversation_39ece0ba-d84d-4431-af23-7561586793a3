import { prisma } from "@/lib/prisma";

export class VehicleFavoritesService {
  // Agregar vehículo a favoritos
  static async addToFavorites(userId: string, vehicleId: string) {
    try {
      // Verificar que el vehículo existe y está activo
      const vehicle = await prisma.vehicle.findFirst({
        where: {
          id: vehicleId,
          status: "active"
        }
      });

      if (!vehicle) {
        throw new Error("Vehículo no encontrado o no disponible");
      }

      // Verificar que no esté ya en favoritos
      const existingFavorite = await prisma.vehicleFavorite.findUnique({
        where: {
          userId_vehicleId: {
            userId,
            vehicleId
          }
        }
      });

      if (existingFavorite) {
        throw new Error("El vehículo ya está en favoritos");
      }

      // Agregar a favoritos
      const favorite = await prisma.vehicleFavorite.create({
        data: {
          userId,
          vehicleId
        },
        include: {
          vehicle: {
            select: {
              id: true,
              make: true,
              model: true,
              year: true,
              price: true,
              images: true,
              rating: true,
              reviews: true,
              features: true,
              host: {
                select: {
                  id: true,
                  name: true,
                  image: true
                }
              }
            }
          }
        }
      });

      return {
        success: true,
        message: "Vehículo agregado a favoritos",
        data: favorite
      };
    } catch (error: any) {
      return {
        success: false,
        message: error.message || "Error al agregar a favoritos"
      };
    }
  }

  // Quitar vehículo de favoritos
  static async removeFromFavorites(userId: string, vehicleId: string) {
    try {
      const favorite = await prisma.vehicleFavorite.findUnique({
        where: {
          userId_vehicleId: {
            userId,
            vehicleId
          }
        }
      });

      if (!favorite) {
        throw new Error("El vehículo no está en favoritos");
      }

      await prisma.vehicleFavorite.delete({
        where: {
          userId_vehicleId: {
            userId,
            vehicleId
          }
        }
      });

      return {
        success: true,
        message: "Vehículo removido de favoritos"
      };
    } catch (error: any) {
      return {
        success: false,
        message: error.message || "Error al remover de favoritos"
      };
    }
  }

  // Obtener favoritos del usuario con paginación
  static async getUserFavorites({
    userId,
    query = {
      page: 1,
      limit: 10
    }
  }: {
    userId: string;
    query: {
      page: number;
      limit: number;
    }
  }) {
    try {
      const { page, limit } = query;
      const skip = (page - 1) * limit;

      const favorites = await prisma.vehicleFavorite.findMany({
        where: {
          userId
        },
        include: {
          vehicle: {
            select: {
              id: true,
              make: true,
              model: true,
              year: true,
              color: true,
              price: true,
              rating: true,
              reviews: true,
              images: true,
              features: true,
              description: true,
              amenities: true,
              engineSize: true,
              transmission: true,
              trim: true,
              bodyType: true,
              status: true,
              createdAt: true,
              host: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                  createdAt: true
                }
              }
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: limit,
        skip: skip
      });

      const totalFavorites = await prisma.vehicleFavorite.count({
        where: {
          userId
        }
      });

      const totalPages = Math.ceil(totalFavorites / limit);

      return {
        success: true,
        data: favorites.map(fav => fav.vehicle),
        pagination: {
          page,
          limit,
          total: totalFavorites,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      };
    } catch (error: any) {
      return {
        success: false,
        message: error.message || "Error al obtener favoritos"
      };
    }
  }

  // Verificar si un vehículo está en favoritos
  static async isFavorite(userId: string, vehicleId: string) {
    try {
      const favorite = await prisma.vehicleFavorite.findUnique({
        where: {
          userId_vehicleId: {
            userId,
            vehicleId
          }
        }
      });

      return {
        success: true,
        isFavorite: !!favorite
      };
    } catch (error: any) {
      return {
        success: false,
        message: error.message || "Error al verificar favorito",
        isFavorite: false
      };
    }
  }

  // Obtener múltiples estados de favoritos para una lista de vehículos
  static async getFavoritesStatus(userId: string, vehicleIds: string[]) {
    try {
      const favorites = await prisma.vehicleFavorite.findMany({
        where: {
          userId,
          vehicleId: {
            in: vehicleIds
          }
        },
        select: {
          vehicleId: true
        }
      });

      const favoritesMap = favorites.reduce((acc, fav) => {
        acc[fav.vehicleId] = true;
        return acc;
      }, {} as Record<string, boolean>);

      return {
        success: true,
        data: favoritesMap
      };
    } catch (error: any) {
      return {
        success: false,
        message: error.message || "Error al obtener estados de favoritos",
        data: {}
      };
    }
  }
}
