import { cookie } from "@elysiajs/cookie";
// src/app/utils/auth-view.ts
import { auth } from '@/lib/auth';
import { Context } from "elysia";
import { prisma } from '@/lib/prisma';
import { getSession } from "../middlewares/auth.middleware";

const betterAuthView = async (context: Context) => {
  const BETTER_AUTH_ACCEPT_METHODS = ["POST", "GET"]
  if (!context.path.includes("api/auth")) {
    // console.log('context', context)
    return;
  }
  if (BETTER_AUTH_ACCEPT_METHODS.includes(context.request.method)) {
    if (context.path === '/api/auth/revoke-session') {

      const body = context?.body as any;
      if (!body.token) return auth.handler(context.request);
      await prisma.session.deleteMany({
        where: {
          token: body.token
        }
      })
    }

    if (context.path === '/api/auth/sign-out') {

      const session = await getSession(context);
      await prisma.session.deleteMany({
        where: {
          token: session.session.token
        }
      })
    }

    return auth.handler(context.request);
  }
  else {
    context.error(405)
    return {
      success: false,
      message: "Method Not Allowed"
    }
  }
}

export default betterAuthView;