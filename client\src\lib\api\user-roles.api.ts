import { apiService } from '@/services/api';

export interface UserRoleResponse {
  success: boolean;
  message: string;
  userType: 'client' | 'host';
  availableUserTypes: string[];
  isHostVerified: boolean;
  newRoleAdded?: string;
}

export const userRolesApi = {
  // Cambiar rol actual del usuario
  switchRole: async (userType: 'client' | 'host'): Promise<UserRoleResponse> => {
    const result = await apiService.patch<UserRoleResponse>('/user/switch-role', {
      userType
    });
    
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.error || 'Error switching role');
    }
  },

  // Solicitar rol adicional
  requestAdditionalRole: async (userType: 'client' | 'host'): Promise<UserRoleResponse> => {
    const result = await apiService.post<UserRoleResponse>('/user/request-additional-role', {
      userType
    });
    
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.error || 'Error requesting additional role');
    }
  },
};
