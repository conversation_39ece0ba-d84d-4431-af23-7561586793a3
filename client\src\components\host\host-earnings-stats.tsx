import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card"
import { DollarSign, TrendingUp, Calendar, CreditCard } from "lucide-react"

const stats = [
  {
    title: "Ganancias Totales",
    value: "$12,450",
    change: "+15% vs mes anterior",
    icon: DollarSign,
    color: "text-green-600",
  },
  {
    title: "Este Mes",
    value: "$2,340",
    change: "+8% vs mes anterior",
    icon: TrendingUp,
    color: "text-blue-600",
  },
  {
    title: "Pendiente de Pago",
    value: "$580",
    change: "Próximo pago: 15 Ene",
    icon: Calendar,
    color: "text-yellow-600",
  },
  {
    title: "Comisión Plataforma",
    value: "12%",
    change: "Tarifa estándar",
    icon: CreditCard,
    color: "text-purple-600",
  },
]

export function HostEarningsStats() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {stats.map((stat, index) => (
        <Card key={index}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
            <stat.icon className={`h-4 w-4 ${stat.color}`} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
            <p className="text-xs text-muted-foreground">{stat.change}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
