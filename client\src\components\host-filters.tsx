"use client"

import { useState } from "react"
import { ChevronDown, Search } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

export function HostFilters() {
  const [searchQuery, setSearchQuery] = useState("")

  return (
    <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
      <div className="flex flex-1 items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search by name, email or phone..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>
      <div className="flex flex-wrap items-center gap-2">
        <Button variant="outline" size="sm" className="h-8 gap-1">
          All Verification Status
          <ChevronDown className="h-4 w-4" />
        </Button>
        <Button variant="outline" size="sm" className="h-8 gap-1">
          Number of Cars
          <ChevronDown className="h-4 w-4" />
        </Button>
        <Button variant="outline" size="sm" className="h-8 gap-1">
          Activity Level
          <ChevronDown className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
}
