import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Star, MapPin } from "lucide-react"

const recommendations = [
  {
    id: 1,
    name: "Tesla Model Y",
    image: "/placeholder.svg?height=120&width=200",
    host: "Carlos Mendoza",
    location: "Ciudad de México",
    dailyRate: 180,
    rating: 4.9,
    reviews: 23,
    features: ["Eléctrico", "SUV", "5 asientos"],
  },
  {
    id: 2,
    name: "Audi A4",
    image: "/placeholder.svg?height=120&width=200",
    host: "Ana García",
    location: "Guadalajara",
    dailyRate: 140,
    rating: 4.8,
    reviews: 18,
    features: ["Sedan", "Lujo", "Automático"],
  },
  {
    id: 3,
    name: "Jeep Wrangler",
    image: "/placeholder.svg?height=120&width=200",
    host: "<PERSON>",
    location: "Cancún",
    dailyRate: 160,
    rating: 4.7,
    reviews: 31,
    features: ["SUV", "4x4", "Aventura"],
  },
]

export function ClientRecommendations() {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-md font-medium">Recomendados para Ti</CardTitle>
        <Button variant="ghost" size="sm" asChild>
          <Link href="/dashboard/client/search">Ver Más</Link>
        </Button>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {recommendations.map((vehicle) => (
            <div key={vehicle.id} className="border rounded-lg overflow-hidden hover:shadow-md transition-shadow">
              <div className="relative h-32">
                <Image src={vehicle.image || "/placeholder.svg"} alt={vehicle.name} fill className="object-cover" />
              </div>
              <div className="p-4 space-y-3">
                <div>
                  <h3 className="font-medium text-sm">{vehicle.name}</h3>
                  <p className="text-xs text-muted-foreground">por {vehicle.host}</p>
                </div>

                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <MapPin className="h-3 w-3" />
                  <span>{vehicle.location}</span>
                </div>

                <div className="flex items-center gap-1">
                  <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                  <span className="text-xs font-medium">{vehicle.rating}</span>
                  <span className="text-xs text-muted-foreground">({vehicle.reviews})</span>
                </div>

                <div className="flex flex-wrap gap-1">
                  {vehicle.features.map((feature, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {feature}
                    </Badge>
                  ))}
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-lg font-bold">${vehicle.dailyRate}</span>
                    <span className="text-xs text-muted-foreground">/día</span>
                  </div>
                  <Button size="sm" className="text-xs">
                    Ver detalles
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
