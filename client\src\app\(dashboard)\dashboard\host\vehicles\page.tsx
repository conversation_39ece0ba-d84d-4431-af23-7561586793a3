"use client"

import { But<PERSON> } from "@/components/ui/button"
// import Link from "next/link"
import { HostVehicleTable } from "@/components/host/host-vehicle-table"
import { HostVehicleStats } from "@/components/host/host-vehicle-stats"
import { useQuery } from "@tanstack/react-query"
import { vehiclesApi } from "@/lib/api/vehicles.api"
import { useRouter, useSearchParams } from 'next/navigation'
import { useUserVerification } from '@/hooks/use-user-verification'

export default function HostVehiclesPage() {

  const searchParams = useSearchParams()
  const { checkVerification } = useUserVerification()
  const page = Number(searchParams.get('page') || 1)
  const limit = Number(searchParams.get('limit') || 10)

  const {
    data,
    isLoading,
  } = useQuery({
    // queryKey: ['host-vehicles'],
    // queryFn: vehiclesApi.host.getAll,
    queryKey: ['host-vehicles', page, limit],
    queryFn: () => vehiclesApi.host.getAll({ page, limit }),
    staleTime: 60 * 1000, // 1 minuto
  })

  const vehicles = data?.data
  const router = useRouter()

  return (  
    <div className="space-y-6">
      <div className="flex flex-col space-y-2">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Mis Vehículos {data?.pagination.total}</h1>
            <p className="text-muted-foreground">Gestiona tus vehículos registrados en la plataforma.</p>
          </div>
          <Button
            onClick={() => {
              if (checkVerification()) {
                router.push('/dashboard/host/vehicles/new')
              }
            }}
          >
            + Añadir Vehículo
          </Button>
        </div>
      </div>



      <>
        <HostVehicleStats />

        <div className="space-y-4">
          <h2 className="text-lg font-semibold">Lista de Vehículos</h2>
          <HostVehicleTable
            vehicles={vehicles || []}
            rowCount={data?.pagination.total || 0}
            isLoading={isLoading}
          />
        </div>
        </>

    </div>
  )
}

