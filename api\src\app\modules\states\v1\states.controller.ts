import { Elysia, t } from "elysia";
import { StatesService } from "./states.service";
import { authMiddleware } from "@/app/middlewares/auth.middleware";
import { checkAdmin } from '@/lib/check-admin';


// Esquema para validación de estados
const stateSchema = t.Object({
  name: t.String(),
  code: t.String(),
  countryCode: t.Optional(t.String()),
  timezone: t.Optional(t.String()),
  active: t.Optional(t.<PERSON>())
});

// Controlador público de estados
export const publicStatesController = new Elysia({ prefix: '/states' })
  .get('/', async () => {
    return await StatesService.getAll();
  })
  .get('/:id', async ({ params }) => {
    return await StatesService.getById(params.id);
  });

// Controlador para administradores
export const adminStatesController = new Elysia({ prefix: '/admin/states' })
  .use(authMiddleware)
  .derive(({ user }) => {
    checkAdmin(user);
  })
  .get('/', async () => {
    return await StatesService.getAll();
  })
  .get('/:id', async ({ params }) => {
    return await StatesService.getById(params.id);
  })
  .post('/',
    async ({ body }) => {
      return await StatesService.create(body);
    },
    {
      body: stateSchema
    }
  )
  .put('/:id',
    async ({ params, body }) => {

      return await StatesService.update(params.id, body);
    },
    {
      body: stateSchema
    }
  )
  .patch('/:id/toggle-active', async ({ params }) => {

    return await StatesService.toggleActive(params.id);
  })
  .delete('/:id', async ({ params }) => {

    return await StatesService.delete(params.id);
  });