import * as React from 'react'
import { Tailwind, Section, Text, Link, But<PERSON> } from '@react-email/components'

export default function ResetPasswordEmail({ name, url }: { name: string, url: string }) {
  return (
    <Tailwind>
      <Section className="flex justify-center items-center w-full min-h-screen font-sans">
        <Section className="flex flex-col items-center w-96 rounded-2xl px-8 py-6 bg-gray-50">
          <Text className="text-black text-2xl font-bold mb-2">
            Restablecer Contraseña
          </Text>
          <Text className="text-gray-600 text-sm mb-6 text-center">
            <PERSON>la {name}, recibimos una solicitud para restablecer tu contraseña.
            Si no solicitaste este cambio, puedes ignorar este email.
          </Text>
          <Text className="text-gray-600 text-sm mb-6 text-center">
            Para restablecer tu contraseña, haz clic en el botón de abajo:
          </Text>
          <Section className="flex justify-center items-center">

            <Link
              href={url}
              className="inline-block text-white text-sm bg-red-500 hover:bg-red-600 transition px-6 py-3 rounded-md text-center  no-underline mb-4"
            >
              Restablecer Contraseña
            </Link>
          </Section>
          <Text className="text-gray-500 text-xs text-center">
            Este enlace expirará en 1 hora por seguridad.
          </Text>
          <Text className="text-gray-500 text-xs text-center mt-4">
            Si tienes problemas con el botón, copia y pega este enlace en tu navegador:
          </Text>
          <Text className="text-gray-400 text-xs text-center break-all">
            {url}
          </Text>
        </Section>
      </Section>
    </Tailwind>
  )
}
