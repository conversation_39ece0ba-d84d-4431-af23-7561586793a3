import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { DateTime } from "luxon"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Normaliza una fecha ISO a un objeto Date de JavaScript establecido a medianoche en la zona horaria local
 * @param dateStr Fecha en formato ISO string
 * @returns Objeto Date normalizado
 */
export function normalizeDate(dateStr: string): Date {
  return DateTime.fromISO(dateStr)
    .setZone('local')
    .startOf('day')
    .toJSDate();
}

/**
 * Convierte un array de fechas ISO a objetos Date normalizados
 * @param dateStrings Array de fechas en formato ISO string
 * @returns Array de objetos Date normalizados
 */
export function normalizeDates(dateStrings: string[]): Date[] {
  if (!dateStrings || !Array.isArray(dateStrings)) return [];
  return dateStrings.map(normalizeDate);
}

/**
 * Convierte un ISO a un string con formato "dd de MMM de yyyy"
 * @param date Fecha en formato ISO string
 * @returns String formateado
 */
export function formatISODate(date: string): string {
  return DateTime.fromISO(date)
    .setZone('local')
    .toFormat('d \'de\' MMM \'de\' yyyy', { locale: 'es' });
}

/**
 * Verifica si una fecha está en un array de fechas deshabilitadas
 * @param date Fecha a verificar
 * @param disabledDates Array de fechas deshabilitadas
 * @returns true si la fecha está deshabilitada, false en caso contrario
 */
export function isDateDisabled(date: Date, disabledDates: Date[]): boolean {
  const luxonDate = DateTime.fromJSDate(date)
    .setZone('local')
    .startOf('day');

  return disabledDates.some(disabledDate => {
    const luxonDisabledDate = DateTime.fromJSDate(disabledDate);
    return luxonDate.hasSame(luxonDisabledDate, 'day');
  });
}

/**
 * Formatea una fecha a un string en formato: "dd de MMM de yyyy"
 * @param date Fecha a formatear
 * @returns String formateado
 */
export function formatDate(date: string) {
  return DateTime.fromISO(date)
    .setZone('local')
    .toFormat('d \'de\' MMMM, yyyy', { locale: 'es' });
}

/** 
 * Formatea un número a un string en formato de moneda local, por default MXN
 * @param value Número a formatear
 * @returns String formateado
 */
export function formatCurrency(value: number, currency: string = 'MXN') {
  return new Intl.NumberFormat('es-MX', { style: 'currency', currency }).format(value);

}