type BaseProductPayment = {
  // productId: string;
  quantity: number;
  discount?: number;
  discountType?: 'percentage' | 'fixed' | null;
  // Should be a boolean field that validates if we should use productId or not to create or use the existing product
  // useProductId?: boolean; // default: true
  // create?: {
  //   name: string;
  //   description: string | null;
  //   price: number;
  //   currency: string | null;
  //   metadata: Record<string, any> | null;
  // };
  // useProductId?: true;
}

type ProductCreate = {
  useProductId: false;
  create: {
    name: string;
    description: string | null;
    price: number;
    currency: string;
    metadata: Record<string, any> | null;
  };
}

type ProductUse = {
  useProductId: true;
  productId: string;
}

type ProductPayment = BaseProductPayment & (ProductCreate | ProductUse);

