import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

const upcomingReservations = [
  {
    id: 1,
    vehicle: {
      name: "Tesla Model 3",
      image: "/placeholder.svg?height=40&width=40",
      host: "<PERSON>",
    },
    dates: {
      start: "15 Mar",
      end: "18 Mar",
      days: 3,
    },
    amount: "$360.00",
    status: "confirmed",
    location: "Ciudad de México",
  },
  {
    id: 2,
    vehicle: {
      name: "BMW X5",
      image: "/placeholder.svg?height=40&width=40",
      host: "<PERSON>",
    },
    dates: {
      start: "22 Mar",
      end: "25 Mar",
      days: 3,
    },
    amount: "$450.00",
    status: "pending",
    location: "Guadalajara",
  },
]

export function ClientUpcomingReservations() {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-md font-medium">Próximas <PERSON></CardTitle>
        <Button variant="ghost" size="sm" asChild>
          <Link href="/dashboard/client/reservations">Ver Todas</Link>
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {upcomingReservations.map((reservation) => (
            <div key={reservation.id} className="flex items-center gap-4 p-4 border rounded-lg">
              <div className="h-12 w-12 rounded overflow-hidden">
                <Image
                  src={reservation.vehicle.image || "/placeholder.svg"}
                  alt={reservation.vehicle.name}
                  width={48}
                  height={48}
                  className="h-full w-full object-cover"
                />
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <p className="text-sm font-medium">{reservation.vehicle.name}</p>
                    <Badge variant={reservation.status === "confirmed" ? "default" : "secondary"} className="text-xs">
                      {reservation.status === "confirmed" ? "Confirmado" : "Pendiente"}
                    </Badge>
                  </div>
                  <span className="text-sm font-medium">{reservation.amount}</span>
                </div>
                <p className="text-xs text-muted-foreground mb-1">Anfitrión: {reservation.vehicle.host}</p>
                <p className="text-xs text-muted-foreground mb-2">📍 {reservation.location}</p>
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <span>
                    {reservation.dates.start} - {reservation.dates.end} ({reservation.dates.days} días)
                  </span>
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" className="h-6 text-xs">
                      Ver detalles
                    </Button>
                    {reservation.status === "pending" && (
                      <Button size="sm" variant="destructive" className="h-6 text-xs">
                        Cancelar
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
