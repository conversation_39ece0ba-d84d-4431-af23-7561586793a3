"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { CheckCircle, Calendar, MapPin, Clock, Download, CalendarPlus, ClipboardList } from "lucide-react"
import { useBookingStore } from "@/lib/store/booking-store"
import { format } from "date-fns"
import { es } from "date-fns/locale"
import Link from "next/link"
import { useEffect, useState } from "react"
import { reservationsApi } from "@/lib/api/reservations.api"
import { toast } from "react-hot-toast"
import { useRouter } from "next/navigation"

export default function BookingConfirmation() {
  const router = useRouter()
  const { vehicle, dateRange, totalPrice, contactInfo, reset } = useBookingStore()
  const [bookingReference, setBookingReference] = useState<string>("")
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [reservationCreated, setReservationCreated] = useState(false)

  // Verificar si hay datos en localStorage para evitar crear reservas duplicadas
  useEffect(() => {
    // Intentar recuperar el ID de reserva del localStorage
    const storedBookingId = localStorage.getItem('lastBookingId')
    
    if (storedBookingId) {
      // Si ya existe una reserva, mostrarla en lugar de crear una nueva
      setBookingReference(storedBookingId)
      setReservationCreated(true)
      setIsLoading(false)
      return
    }

    // Si no hay vehículo o fechas, probablemente es una recarga después de completar
    if (!vehicle || !dateRange.startDate || !dateRange.endDate) {
      setError("Información de reserva incompleta")
      setIsLoading(false)
      // Redirigir al paso 1 después de un breve retraso
      setTimeout(() => {
        reset()
        router.push('/booking')
      }, 3000)
      return
    }

    const createReservation = async () => {
      try {
        setIsLoading(true)
        
        // Preparar los datos de la reserva
        const reservationData = {
          vehicleId: vehicle.id,
          startDate: dateRange.startDate!.toISOString(),
          endDate: dateRange.endDate!.toISOString(),
          totalPrice: totalPrice,
          contactName: contactInfo.contactName,
          contactEmail: contactInfo.contactEmail,
          contactPhone: contactInfo.contactPhone,
        }
        
        // Enviar la reserva a la API
        const response = await reservationsApi.client.create(reservationData)
        
        // Establecer la referencia de la reserva (asumiendo que la API devuelve un ID)
        const bookingId = response.id || "AUTO" + Math.floor(10000000 + Math.random() * 90000000)
        setBookingReference(bookingId)
        
        // Guardar el ID en localStorage para evitar duplicados en recargas
        localStorage.setItem('lastBookingId', bookingId)
        
        setReservationCreated(true)
        toast.success("¡Reserva creada con éxito!")
      } catch (error) {
        console.error("Error al crear la reserva:", error)
        setError("No se pudo crear la reserva. Por favor, intenta de nuevo más tarde.")
        toast.error("Error al crear la reserva")
      } finally {
        setIsLoading(false)
      }
    }

    // Solo crear la reserva si no se ha creado ya
    if (!reservationCreated) {
      createReservation()
    }
  }, [vehicle, dateRange, totalPrice, contactInfo, reservationCreated, reset, router])

  // Manejar la redirección al dashboard
  const handleGoToDashboard = () => {
    router.push('/dashboard/client/reservations')
  }

  // Manejar el intento de nueva reserva
  const handleNewBooking = () => {
    reset()
    router.push('/booking')
  }

  if (isLoading) {
    return (
      <div className="max-w-3xl mx-auto text-center py-12">
        <div className="animate-pulse">
          <div className="w-16 h-16 bg-gray-200 rounded-full mx-auto mb-6"></div>
          <div className="h-8 bg-gray-200 rounded w-64 mx-auto mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-40 mx-auto mb-8"></div>
          <div className="h-48 bg-gray-200 rounded w-full mx-auto"></div>
        </div>
        <p className="mt-6 text-gray-600">Procesando tu reserva...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="max-w-3xl mx-auto text-center py-12">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <span className="text-red-500 text-2xl">!</span>
        </div>
        <h2 className="text-2xl font-bold mb-4">Error en la reserva</h2>
        <p className="text-gray-600 mb-6">{error}</p>
        <div className="flex justify-center space-x-4">
          <Button asChild>
            <Link href="/">Volver al inicio</Link>
          </Button>
          <Button variant="outline" onClick={handleNewBooking}>
            Intentar de nuevo
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-3xl mx-auto text-center">
      <div className="mb-6">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
          <CheckCircle className="w-8 h-8 text-green-500" />
        </div>
      </div>
      
      <h2 className="text-2xl font-bold mb-2">¡Reserva Confirmada!</h2>
      <p className="text-gray-600 mb-6">Referencia de reserva: {bookingReference}</p>
      
      <div className="bg-white rounded-lg shadow-md p-6 mb-6 text-left">
        {vehicle ? (
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 bg-gray-200 rounded-md flex items-center justify-center mr-4">
              <span className="font-bold">{vehicle?.make?.substring(0, 2).toUpperCase()}</span>
            </div>
            <div>
              <h3 className="font-bold">{vehicle?.make} {vehicle?.model}</h3>
              <p className="text-gray-600">
                {dateRange.startDate && format(dateRange.startDate, "d MMM", { locale: es })} - 
                {dateRange.endDate && format(dateRange.endDate, "d MMM yyyy", { locale: es })}
              </p>
            </div>
            <div className="ml-auto">
              <p className="font-bold">${totalPrice}</p>
              <p className="text-gray-600 text-right">Total</p>
            </div>
          </div>
        ) : (
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 bg-gray-200 rounded-md flex items-center justify-center mr-4">
              <span className="font-bold">AU</span>
            </div>
            <div>
              <h3 className="font-bold">Reserva completada</h3>
              <p className="text-gray-600">Detalles disponibles en tu dashboard</p>
            </div>
          </div>
        )}
        
        <hr className="my-4" />
        
        <div className="space-y-4">
          <h4 className="font-bold">Próximos pasos</h4>
          
          <div className="flex items-start">
            <Calendar className="w-5 h-5 mr-3 text-gray-500 mt-0.5" />
            <div>
              <p className="font-medium">Revisa tu correo electrónico</p>
              <p className="text-gray-600">Detalles de la reserva enviados a tu correo</p>
            </div>
          </div>
          
          <div className="flex items-start">
            <MapPin className="w-5 h-5 mr-3 text-gray-500 mt-0.5" />
            <div>
              <p className="font-medium">Ubicación de recogida</p>
              <p className="text-gray-600">No establecida</p>
            </div>
          </div>
          
          <div className="flex items-start">
            <Clock className="w-5 h-5 mr-3 text-gray-500 mt-0.5" />
            <div>
              <p className="font-medium">Llega 15 minutos antes de tu hora programada</p>
              <p className="text-gray-600">Necesitarás una identificación válida y tarjeta de crédito</p>
            </div>
          </div>
          
          <div className="flex items-start">
            <ClipboardList className="w-5 h-5 mr-3 text-gray-500 mt-0.5" />
            <div>
              <p className="font-medium">Gestiona tus reservas</p>
              <p className="text-gray-600">Puedes ver todas tus reservas en tu dashboard</p>
            </div>
          </div>
        </div>
        
        <hr className="my-4" />
        
        <div className="space-y-4">
          <h4 className="font-bold">Información importante</h4>
          
          <ul className="list-disc list-inside space-y-2 text-gray-600">
            <li>Se te proporcionará un tanque lleno de combustible</li>
            <li>Contacta con soporte al 1-800-AUTOOP para cualquier pregunta</li>
            <li>Lugar de devolución: Mismo que el de recogida</li>
          </ul>
        </div>
      </div>
      
      <div className="flex flex-wrap justify-center gap-4">
        <Button variant="outline" className="flex items-center">
          <Download className="w-4 h-4 mr-2" />
          Imprimir detalles
        </Button>
        
        <Button variant="outline" className="flex items-center">
          <CalendarPlus className="w-4 h-4 mr-2" />
          Añadir al calendario
        </Button>
        
        <Button 
          className="flex items-center bg-[#1a2b5e] hover:bg-[#152348]"
          onClick={handleGoToDashboard}
        >
          <ClipboardList className="w-4 h-4 mr-2" />
          Ver mis reservas
        </Button>
      </div>
      
      <div className="mt-6 space-x-4">
        <Link href="/" className="text-blue-600 hover:underline">
          Volver al inicio
        </Link>
        <span className="text-gray-400">|</span>
        <button 
          onClick={() => {
            localStorage.removeItem('lastBookingId')
            handleNewBooking()
          }}
          className="text-blue-600 hover:underline"
        >
          Hacer otra reserva
        </button>
      </div>
    </div>
  )
}
